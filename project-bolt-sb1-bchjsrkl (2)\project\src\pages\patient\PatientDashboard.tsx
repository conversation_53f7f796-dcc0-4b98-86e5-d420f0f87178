import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, FileText, Phone, Camera, Book, Users, BarChart2, Sun } from 'lucide-react';

const PatientDashboard: React.FC = () => {
  const menuItems = [
    {
      title: 'Dossier Médical',
      icon: <FileText className="w-6 h-6" />,
      description: 'Consultez votre dossier complet ou demandez des modifications',
      path: '/patient/dossier'
    },
    {
      title: 'Suivi Quotidien',
      icon: <Calendar className="w-6 h-6" />,
      description: 'Suivez votre port d\'appareil et votre brossage',
      path: '/patient/suivi'
    },
    {
      title: 'Rendez-vous',
      icon: <Clock className="w-6 h-6" />,
      description: '<PERSON><PERSON>rez vos rendez-vous et consultations',
      path: '/patient/rendez-vous'
    },
    {
      title: 'Rappels',
      icon: <Phone className="w-6 h-6" />,
      description: 'Programmez vos rappels personnalisés',
      path: '/patient/rappels'
    },
    {
      title: 'Photos',
      icon: <Camera className="w-6 h-6" />,
      description: 'Envoyez des photos de suivi à votre praticien',
      path: '/patient/photos'
    },
    {
      title: 'Éducation',
      icon: <Book className="w-6 h-6" />,
      description: 'Accédez aux ressources éducatives',
      path: '/patient/education'
    },
    {
      title: 'Communauté',
      icon: <Users className="w-6 h-6" />,
      description: 'Échangez avec la communauté',
      path: '/patient/communaute'
    },
    {
      title: 'Analytiques',
      icon: <BarChart2 className="w-6 h-6" />,
      description: 'Visualisez vos statistiques et progrès',
      path: '/patient/analytiques'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Tableau de bord patient</h1>
        <p className="text-gray-600">Bienvenue sur votre espace personnel</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {menuItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className="p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100"
          >
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-50 rounded-lg text-blue-600">
                {item.icon}
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-800">{item.title}</h3>
            </div>
            <p className="text-gray-600 text-sm">{item.description}</p>
          </Link>
        ))}
      </div>

      <div className="mt-8 p-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Prochain rendez-vous</h2>
            <p className="opacity-90">Consultation de suivi - Dr. Martin</p>
            <p className="text-sm opacity-75">15 Juin 2024 à 14:30</p>
          </div>
          <button className="px-4 py-2 bg-white text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors">
            Voir détails
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;
