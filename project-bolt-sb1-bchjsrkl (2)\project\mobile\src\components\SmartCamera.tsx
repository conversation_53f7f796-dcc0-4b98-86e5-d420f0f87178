import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  Animated,
  Vibration,
} from 'react-native';
import { Camera, useCameraDevices, useFrameProcessor } from 'react-native-vision-camera';
import { runOnJS } from 'react-native-reanimated';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-blur/blur';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-react-native';

interface SmartCameraProps {
  onPhotoTaken: (photo: any, analysis: any) => void;
  photoType: 'frontal' | 'lateral' | 'intraoral' | 'smile';
  onClose: () => void;
}

interface AIGuidance {
  isCorrectAngle: boolean;
  isCorrectDistance: boolean;
  isWellLit: boolean;
  isFaceDetected: boolean;
  isTeethVisible: boolean;
  qualityScore: number;
  suggestions: string[];
}

const { width, height } = Dimensions.get('window');

export const SmartCamera: React.FC<SmartCameraProps> = ({
  onPhotoTaken,
  photoType,
  onClose,
}) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [flash, setFlash] = useState<'off' | 'on'>('off');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiGuidance, setAiGuidance] = useState<AIGuidance>({
    isCorrectAngle: false,
    isCorrectDistance: false,
    isWellLit: false,
    isFaceDetected: false,
    isTeethVisible: false,
    qualityScore: 0,
    suggestions: [],
  });

  const camera = useRef<Camera>(null);
  const devices = useCameraDevices();
  const device = devices.front;

  const pulseAnim = useRef(new Animated.Value(1)).current;
  const overlayOpacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    checkCameraPermission();
    startPulseAnimation();
  }, []);

  const checkCameraPermission = async () => {
    const permission = await Camera.requestCameraPermission();
    setHasPermission(permission === 'authorized');
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // Analyse en temps réel avec IA
  const frameProcessor = useFrameProcessor((frame) => {
    'worklet';
    
    // Simulation d'analyse IA en temps réel
    const mockAnalysis = {
      isCorrectAngle: Math.random() > 0.3,
      isCorrectDistance: Math.random() > 0.4,
      isWellLit: Math.random() > 0.2,
      isFaceDetected: Math.random() > 0.1,
      isTeethVisible: photoType === 'intraoral' ? Math.random() > 0.3 : Math.random() > 0.5,
      qualityScore: Math.random() * 100,
      suggestions: [],
    };

    // Générer des suggestions basées sur l'analyse
    const suggestions: string[] = [];
    if (!mockAnalysis.isCorrectAngle) suggestions.push('Ajustez l\'angle de la caméra');
    if (!mockAnalysis.isCorrectDistance) suggestions.push('Rapprochez-vous ou éloignez-vous');
    if (!mockAnalysis.isWellLit) suggestions.push('Améliorez l\'éclairage');
    if (!mockAnalysis.isFaceDetected) suggestions.push('Centrez votre visage');
    if (!mockAnalysis.isTeethVisible) suggestions.push('Montrez vos dents');

    mockAnalysis.suggestions = suggestions;

    runOnJS(setAiGuidance)(mockAnalysis);
  }, [photoType]);

  const takePhoto = async () => {
    if (!camera.current) return;

    try {
      setIsAnalyzing(true);
      
      const photo = await camera.current.takePhoto({
        flash: flash,
        enableAutoRedEyeReduction: true,
        enableAutoStabilization: true,
        enableShutterSound: true,
      });

      // Vibration de feedback
      Vibration.vibrate(100);

      // Analyse IA de la photo prise
      const analysis = await analyzePhoto(photo);
      
      onPhotoTaken(photo, analysis);
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de prendre la photo');
      console.error(error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzePhoto = async (photo: any) => {
    // Simulation d'analyse IA approfondie
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return {
      qualityScore: Math.random() * 100,
      teethDetected: photoType === 'intraoral' ? Math.floor(Math.random() * 32) : 0,
      alignmentScore: Math.random() * 100,
      progressDetected: Math.random() > 0.5,
      recommendations: [
        'Photo de bonne qualité',
        'Angle optimal détecté',
        'Éclairage suffisant',
      ],
      metadata: {
        timestamp: new Date().toISOString(),
        photoType,
        deviceInfo: 'iPhone 15 Pro',
        cameraSettings: {
          flash: flash,
          resolution: '4K',
          iso: 'Auto',
        },
      },
    };
  };

  const getPhotoTypeInstructions = () => {
    switch (photoType) {
      case 'frontal':
        return {
          title: 'Photo Frontale',
          instructions: [
            'Regardez directement la caméra',
            'Gardez la bouche fermée',
            'Centrez votre visage',
            'Éclairage uniforme',
          ],
          overlay: 'face',
        };
      case 'lateral':
        return {
          title: 'Photo de Profil',
          instructions: [
            'Tournez la tête de 90°',
            'Profil parfaitement droit',
            'Bouche fermée et détendue',
            'Oreille visible',
          ],
          overlay: 'profile',
        };
      case 'intraoral':
        return {
          title: 'Photo Intra-orale',
          instructions: [
            'Ouvrez grand la bouche',
            'Montrez toutes vos dents',
            'Utilisez un écarteur si possible',
            'Éclairage direct sur les dents',
          ],
          overlay: 'teeth',
        };
      case 'smile':
        return {
          title: 'Photo Sourire',
          instructions: [
            'Souriez naturellement',
            'Montrez vos dents',
            'Regardez la caméra',
            'Expression détendue',
          ],
          overlay: 'smile',
        };
      default:
        return {
          title: 'Photo',
          instructions: ['Suivez les instructions'],
          overlay: 'face',
        };
    }
  };

  const renderOverlay = () => {
    const instructions = getPhotoTypeInstructions();
    
    return (
      <View style={styles.overlay}>
        {/* Guide visuel */}
        <View style={styles.guideContainer}>
          <Animated.View
            style={[
              styles.guide,
              {
                transform: [{ scale: pulseAnim }],
                opacity: overlayOpacity,
              },
            ]}
          >
            {instructions.overlay === 'face' && (
              <View style={styles.faceGuide} />
            )}
            {instructions.overlay === 'profile' && (
              <View style={styles.profileGuide} />
            )}
            {instructions.overlay === 'teeth' && (
              <View style={styles.teethGuide} />
            )}
            {instructions.overlay === 'smile' && (
              <View style={styles.smileGuide} />
            )}
          </Animated.View>
        </div>

        {/* Instructions en haut */}
        <BlurView style={styles.instructionsContainer} blurType="dark">
          <Text style={styles.instructionsTitle}>{instructions.title}</Text>
          {instructions.instructions.map((instruction, index) => (
            <Text key={index} style={styles.instructionText}>
              • {instruction}
            </Text>
          ))}
        </BlurView>

        {/* Feedback IA en temps réel */}
        <View style={styles.aiGuidanceContainer}>
          <BlurView style={styles.aiGuidance} blurType="dark">
            <View style={styles.qualityIndicator}>
              <Text style={styles.qualityScore}>
                Qualité: {Math.round(aiGuidance.qualityScore)}%
              </Text>
              <View style={styles.qualityBar}>
                <View
                  style={[
                    styles.qualityFill,
                    {
                      width: `${aiGuidance.qualityScore}%`,
                      backgroundColor:
                        aiGuidance.qualityScore > 80
                          ? '#10B981'
                          : aiGuidance.qualityScore > 60
                          ? '#F59E0B'
                          : '#EF4444',
                    },
                  ]}
                />
              </View>
            </View>

            {/* Indicateurs de statut */}
            <View style={styles.statusIndicators}>
              <StatusIndicator
                icon="face"
                active={aiGuidance.isFaceDetected}
                label="Visage"
              />
              <StatusIndicator
                icon="wb-sunny"
                active={aiGuidance.isWellLit}
                label="Éclairage"
              />
              <StatusIndicator
                icon="center-focus-strong"
                active={aiGuidance.isCorrectAngle}
                label="Angle"
              />
              <StatusIndicator
                icon="zoom-in"
                active={aiGuidance.isCorrectDistance}
                label="Distance"
              />
            </View>

            {/* Suggestions */}
            {aiGuidance.suggestions.length > 0 && (
              <View style={styles.suggestions}>
                {aiGuidance.suggestions.slice(0, 2).map((suggestion, index) => (
                  <Text key={index} style={styles.suggestionText}>
                    💡 {suggestion}
                  </Text>
                ))}
              </View>
            )}
          </BlurView>
        </View>
      </View>
    );
  };

  const StatusIndicator: React.FC<{
    icon: string;
    active: boolean;
    label: string;
  }> = ({ icon, active, label }) => (
    <View style={styles.statusIndicator}>
      <Icon
        name={icon}
        size={16}
        color={active ? '#10B981' : '#6B7280'}
      />
      <Text style={[styles.statusLabel, { color: active ? '#10B981' : '#6B7280' }]}>
        {label}
      </Text>
    </View>
  );

  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Icon name="camera-alt" size={64} color="#6B7280" />
        <Text style={styles.permissionText}>
          Autorisation caméra requise
        </Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={checkCameraPermission}
        >
          <Text style={styles.permissionButtonText}>Autoriser</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Caméra non disponible</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={camera}
        style={styles.camera}
        device={device}
        isActive={isActive}
        photo={true}
        frameProcessor={frameProcessor}
      />

      {renderOverlay()}

      {/* Contrôles */}
      <View style={styles.controls}>
        {/* Bouton fermer */}
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Icon name="close" size={24} color="white" />
        </TouchableOpacity>

        {/* Bouton flash */}
        <TouchableOpacity
          style={styles.flashButton}
          onPress={() => setFlash(flash === 'off' ? 'on' : 'off')}
        >
          <Icon
            name={flash === 'off' ? 'flash-off' : 'flash-on'}
            size={24}
            color="white"
          />
        </TouchableOpacity>

        {/* Bouton capture */}
        <View style={styles.captureContainer}>
          <TouchableOpacity
            style={[
              styles.captureButton,
              {
                opacity: aiGuidance.qualityScore > 70 ? 1 : 0.6,
              },
            ]}
            onPress={takePhoto}
            disabled={isAnalyzing || aiGuidance.qualityScore < 50}
          >
            <LinearGradient
              colors={
                aiGuidance.qualityScore > 70
                  ? ['#10B981', '#059669']
                  : ['#6B7280', '#4B5563']
              }
              style={styles.captureGradient}
            >
              {isAnalyzing ? (
                <View style={styles.loadingSpinner} />
              ) : (
                <Icon name="camera-alt" size={32} color="white" />
              )}
            </LinearGradient>
          </TouchableOpacity>
          
          <Text style={styles.captureHint}>
            {aiGuidance.qualityScore > 70
              ? 'Prêt à capturer'
              : 'Ajustez la position'}
          </Text>
        </div>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'space-between',
  },
  guideContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guide: {
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 20,
  },
  faceGuide: {
    width: width * 0.6,
    height: width * 0.8,
    borderRadius: width * 0.3,
  },
  profileGuide: {
    width: width * 0.5,
    height: width * 0.7,
    borderRadius: 20,
  },
  teethGuide: {
    width: width * 0.7,
    height: width * 0.4,
    borderRadius: 20,
  },
  smileGuide: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: width * 0.3,
  },
  instructionsContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  instructionsTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  instructionText: {
    color: 'white',
    fontSize: 14,
    marginBottom: 4,
  },
  aiGuidanceContainer: {
    position: 'absolute',
    top: height * 0.3,
    left: 20,
    right: 20,
  },
  aiGuidance: {
    padding: 16,
    borderRadius: 12,
  },
  qualityIndicator: {
    marginBottom: 12,
  },
  qualityScore: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  qualityBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  qualityFill: {
    height: '100%',
    borderRadius: 2,
  },
  statusIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  statusIndicator: {
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 10,
    marginTop: 4,
  },
  suggestions: {
    marginTop: 8,
  },
  suggestionText: {
    color: 'white',
    fontSize: 12,
    marginBottom: 4,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  closeButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flashButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureContainer: {
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
  },
  captureGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureHint: {
    color: 'white',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  loadingSpinner: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: 'white',
    borderTopColor: 'transparent',
    borderRadius: 12,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  permissionText: {
    color: 'white',
    fontSize: 18,
    marginVertical: 20,
    textAlign: 'center',
  },
  permissionButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
