import React, { useState } from 'react';
import { MessageSquare, Heart, Share2, User, Clock, Search, Filter, Plus } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface Post {
  id: string;
  author: {
    name: string;
    type: 'patient' | 'practitioner';
    avatar?: string;
  };
  content: string;
  category: 'question' | 'experience' | 'conseil' | 'cas-clinique';
  timestamp: string;
  likes: number;
  comments: number;
  isLiked: boolean;
  tags: string[];
}

const Community: React.FC = () => {
  const { t } = useLanguage();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showNewPost, setShowNewPost] = useState(false);

  const posts: Post[] = [
    {
      id: '1',
      author: {
        name: 'Dr. <PERSON>',
        type: 'practitioner'
      },
      content: 'Voici un cas intéressant de correction d\'une malocclusion de classe II avec Invisalign. Le traitement a duré 18 mois avec d\'excellents résultats. N\'hésitez pas à partager vos expériences similaires !',
      category: 'cas-clinique',
      timestamp: '2024-06-15T10:30:00Z',
      likes: 24,
      comments: 8,
      isLiked: false,
      tags: ['invisalign', 'classe-ii', 'adulte']
    },
    {
      id: '2',
      author: {
        name: 'Marie D.',
        type: 'patient'
      },
      content: 'Bonjour ! Je porte mes aligneurs depuis 3 mois maintenant. Au début, j\'avais du mal à parler correctement. Quelqu\'un a-t-il des conseils pour améliorer l\'élocution avec les aligneurs ?',
      category: 'question',
      timestamp: '2024-06-15T09:15:00Z',
      likes: 12,
      comments: 15,
      isLiked: true,
      tags: ['aligneurs', 'élocution', 'conseils']
    },
    {
      id: '3',
      author: {
        name: 'Dr. Sophie Laurent',
        type: 'practitioner'
      },
      content: 'Rappel important : l\'hygiène dentaire est cruciale pendant un traitement orthodontique. Voici mes recommandations pour un brossage efficace avec des bagues...',
      category: 'conseil',
      timestamp: '2024-06-14T16:45:00Z',
      likes: 35,
      comments: 6,
      isLiked: false,
      tags: ['hygiène', 'bagues', 'brossage']
    }
  ];

  const categories = [
            { value: 'all', label: t('community.all-posts'), color: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' },
            { value: 'question', label: t('community.questions'), color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' },
            { value: 'experience', label: t('community.experiences'), color: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' },
            { value: 'conseil', label: t('community.advice'), color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300' },
            { value: 'cas-clinique', label: t('community.clinical-cases'), color: 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' }
  ];

  const filteredPosts = posts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    const matchesSearch = post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const getCategoryColor = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat ? cat.color : 'bg-gray-100 text-gray-800';
  };

  const getCategoryLabel = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat ? cat.label : category;
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('time.less-than-hour');
    if (diffInHours < 24) return `${diffInHours}h ${t('time.ago')}`;
    const days = Math.floor(diffInHours / 24);
    return `${days} ${t('time.days-ago')}`;
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">{t('community.title')}</h1>
            <p className="text-gray-600 dark:text-gray-400">{t('community.subtitle')}</p>
          </div>
          <button
            onClick={() => setShowNewPost(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            {t('community.new-post')}
          </button>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
              placeholder={t('community.search-placeholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category.value
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Liste des posts */}
      <div className="space-y-6">
        {filteredPosts.map((post) => (
          <div key={post.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
            {/* En-tête du post */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${
                  post.author.type === 'practitioner' 
                    ? 'bg-purple-500 dark:bg-purple-600' 
                    : 'bg-blue-500 dark:bg-blue-600'
                }`}>
                  {post.author.name.charAt(0)}
                </div>
                <div className="ml-3">
                  <div className="flex items-center">
                    <span className="font-semibold text-gray-800 dark:text-gray-200">{post.author.name}</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      post.author.type === 'practitioner' 
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' 
                        : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                    }`}>
                      {t(post.author.type === 'practitioner' ? 'auth.practitioner' : 'auth.patient')}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatTimeAgo(post.timestamp)}
                  </div>
                </div>
              </div>
              
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(post.category)}`}>
                {getCategoryLabel(post.category)}
              </span>
            </div>

            {/* Contenu du post */}
            <div className="mb-4">
              <p className="text-gray-800 dark:text-gray-200 leading-relaxed">{post.content}</p>
            </div>

            {/* Tags */}
            {post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-xs"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="flex items-center space-x-6">
              <button className={`flex items-center space-x-2 transition-colors ${
                post.isLiked ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400'
              }`}>
                <Heart className={`w-5 h-5 ${post.isLiked ? 'fill-current' : ''}`} />
                <span className="text-sm">{post.likes}</span>
              </button>
              
              <button className="flex items-center space-x-2 text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors">
                <MessageSquare className="w-5 h-5" />
                <span className="text-sm">{post.comments}</span>
              </button>
              
              <button className="flex items-center space-x-2 text-gray-500 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 transition-colors">
                <Share2 className="w-5 h-5" />
                <span className="text-sm">{t('community.share')}</span>
              </button>
            </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal de nouveau post */}
      {showNewPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">{t('community.new-post')}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('community.category')}
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="question">{t('community.questions')}</option>
                  <option value="experience">{t('community.experiences')}</option>
                  <option value="conseil">{t('community.advice')}</option>
                  <option value="cas-clinique">{t('community.clinical-cases')}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('community.content')}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={6}
                  placeholder={t('community.post-placeholder')}
                ></textarea>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('community.tags')}
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('community.tags-placeholder')}
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowNewPost(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={() => {
                  setShowNewPost(false);
                  alert(t('community.post-success'));
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 transition-colors"
              >
                {t('community.publish')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Community;
