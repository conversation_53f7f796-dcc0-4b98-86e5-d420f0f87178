import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Bell, Settings, LogOut, Moon, Sun } from 'lucide-react';
import Logo from '../Logo';
import LanguageSelector from '../LanguageSelector';

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/auth');
  };



  return (
    <nav className="surface-primary dark:bg-gray-800 border-b border-adaptive dark:border-gray-700 px-4 py-2.5">
      <div className="flex justify-between items-center">
        {/* Left side - Search and Logo */}
        <div className="flex-1 max-w-xl flex items-center space-x-4">
          <Logo size="md" />
          <div className="relative flex-1">
            <input
              type="search"
              className="w-full pl-4 pr-10 py-2 rounded-lg border border-adaptive dark:border-gray-600 surface-secondary dark:bg-gray-700 text-adaptive dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={t('common.search')}
            />
          </div>
        </div>

        {/* Right side - User menu */}
        <div className="flex items-center space-x-4">
          {/* Language Selector */}
          <LanguageSelector showLabel={false} />

          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg hover:surface-tertiary dark:hover:bg-gray-700 transition-colors"
            title={isDarkMode ? t('theme.light') : t('theme.dark')}
          >
            {isDarkMode ? (
              <Sun className="w-5 h-5 text-yellow-500" />
            ) : (
              <Moon className="w-5 h-5 text-adaptive-secondary dark:text-gray-400" />
            )}
          </button>

          {/* Notifications */}
          <button 
            onClick={() => navigate(user?.role === 'patient' ? '/patient/rappels' : '/practitioner/notifications')}
            className="p-2 rounded-lg hover:surface-tertiary dark:hover:bg-gray-700 transition-colors relative"
          >
            <Bell className="w-5 h-5 text-adaptive-secondary dark:text-gray-400" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* Settings */}
          <button 
            onClick={() => navigate('/settings')}
            className="p-2 rounded-lg hover:surface-tertiary dark:hover:bg-gray-700 transition-colors"
          >
            <Settings className="w-5 h-5 text-adaptive-secondary dark:text-gray-400" />
          </button>

          {/* User Profile */}
          <div 
            className="flex items-center space-x-3 cursor-pointer"
            onClick={() => navigate('/profile')}
          >
            <div className="flex flex-col items-end">
              <span className="text-sm font-medium text-adaptive dark:text-gray-100">
                {user?.firstName} {user?.lastName}
              </span>
              <span className="text-xs text-adaptive-tertiary dark:text-gray-400">
                {user?.role === 'practitioner' ? t('auth.practitioner') : t('auth.patient')}
              </span>
            </div>
            <button
              onClick={handleLogout}
              className="p-2 rounded-lg hover:surface-tertiary dark:hover:bg-gray-700 transition-colors"
              title={t('auth.logout')}
            >
              <LogOut className="w-5 h-5 text-adaptive-secondary dark:text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
