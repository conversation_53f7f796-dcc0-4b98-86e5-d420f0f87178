import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Calendar, CreditCard, MessageSquare, Settings, BarChart3, FileText, Smile, Link as LinkIcon, Brain } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Practitioner } from '../../types/user';
import LinkCodeManager from '../../components/practitioner/LinkCodeManager';

const PractitionerDashboard: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { t } = useLanguage();
  const [practitioner, setPractitioner] = useState<Practitioner>(user as Practitioner);

  const handleUpdatePractitioner = (updatedPractitioner: Practitioner) => {
    setPractitioner(updatedPractitioner);
    updateUser?.(updatedPractitioner);
  };

  const menuItems = [
    {
      title: t('practitioner.dashboard.linkCodes'),
      icon: <LinkIcon className="w-6 h-6" />,
      description: t('practitioner.dashboard.linkCodesDesc'),
      path: '#link-codes',
      isSpecial: true
    },
    {
      title: t('practitioner.dashboard.myPatients'),
      icon: <Users className="w-6 h-6" />,
      description: t('practitioner.dashboard.myPatientsDesc'),
      path: '/practitioner/patients',
      count: practitioner.patients?.length.toString() || '0'
    },
    {
      title: t('practitioner.dashboard.appointments'),
      icon: <Calendar className="w-6 h-6" />,
      description: t('practitioner.dashboard.appointmentsDesc'),
      path: '/practitioner/appointments',
      count: '8'
    },
    {
      title: t('practitioner.dashboard.invisalign'),
      icon: <Smile className="w-6 h-6" />,
      description: t('practitioner.dashboard.invisalignDesc'),
      path: '/practitioner/invisalign',
      count: '3'
    },
    {
      title: t('practitioner.dashboard.communications'),
      icon: <MessageSquare className="w-6 h-6" />,
      description: t('practitioner.dashboard.communicationsDesc'),
      path: '/practitioner/messages',
      count: '12'
    },
    {
      title: t('practitioner.dashboard.billing'),
      icon: <CreditCard className="w-6 h-6" />,
      description: t('practitioner.dashboard.billingDesc'),
      path: '/practitioner/billing'
    },
    {
      title: t('practitioner.dashboard.subscription'),
      icon: <Settings className="w-6 h-6" />,
      description: t('practitioner.dashboard.subscriptionDesc'),
      path: '/practitioner/subscription'
    },
    {
      title: 'Analyse Céphalométrique IA',
      icon: <Brain className="w-6 h-6" />,
      description: 'Analyse automatique des radiographies céphalométriques avec IA',
      path: '/practitioner/cephalometric',
      isNew: true
    },
    {
      title: t('practitioner.dashboard.analytics'),
      icon: <BarChart3 className="w-6 h-6" />,
      description: t('practitioner.dashboard.analyticsDesc'),
      path: '/practitioner/analytics'
    },
    {
      title: t('practitioner.dashboard.reports'),
      icon: <FileText className="w-6 h-6" />,
      description: t('practitioner.dashboard.reportsDesc'),
      path: '/practitioner/reports'
    }
  ];

  const recentPatients = [
    { name: 'Marie Dubois', lastVisit: '2024-06-10', status: t('practitioner.dashboard.status.inProgress') },
    { name: 'Pierre Martin', lastVisit: '2024-06-08', status: t('practitioner.dashboard.status.followUp') },
    { name: 'Sophie Laurent', lastVisit: '2024-06-05', status: t('practitioner.dashboard.status.completed') }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-adaptive">{t('practitioner.dashboard.title')}</h1>
        <p className="text-adaptive-secondary">{t('practitioner.dashboard.myPatientsDesc')}</p>
      </div>

      {/* Stats rapides - Design Premium */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="group p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-200 dark:border-blue-700 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">{t('practitioner.dashboard.activePatients')}</p>
              <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">24</p>
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">+3 {t('practitioner.dashboard.thisMonth')}</p>
            </div>
            <div className="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <Users className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="group p-6 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-green-200 dark:border-green-700 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 dark:text-green-400 font-medium">RDV aujourd'hui</p>
              <p className="text-3xl font-bold text-green-700 dark:text-green-300">8</p>
              <p className="text-xs text-green-500 dark:text-green-400 mt-1">2 en attente</p>
            </div>
            <div className="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <Calendar className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="group p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-200 dark:border-purple-700 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-purple-600 dark:text-purple-400 font-medium">Messages</p>
              <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">12</p>
              <p className="text-xs text-purple-500 dark:text-purple-400 mt-1">5 non lus</p>
            </div>
            <div className="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <MessageSquare className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="group p-6 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-200 dark:border-orange-700 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-orange-600 dark:text-orange-400 font-medium">Revenus ce mois</p>
              <p className="text-3xl font-bold text-orange-700 dark:text-orange-300">€4,250</p>
              <p className="text-xs text-orange-500 dark:text-orange-400 mt-1">+12% vs mois dernier</p>
            </div>
            <div className="p-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
              <CreditCard className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Menu principal */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {menuItems.map((item, index) => {
          if (item.isSpecial) {
            return (
              <a
                key={index}
                href={item.path}
                className="p-6 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 relative"
              >
                <div className="flex items-center mb-4">
                  <div className="p-2 surface-primary/20 rounded-lg">
                    {item.icon}
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">{item.title}</h3>
                </div>
                <p className="text-white/90 text-sm">{item.description}</p>
              </a>
            );
          }
          
          return (
            <Link
              key={index}
              to={item.path}
              className="p-6 surface-primary dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-adaptive-light dark:border-gray-700 relative"
            >
              <div className="flex items-center mb-4">
                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg text-blue-600 dark:text-blue-400">
                  {item.icon}
                </div>
                <h3 className="ml-3 text-lg font-semibold text-adaptive dark:text-white">{item.title}</h3>
                {item.count && (
                  <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {item.count}
                  </span>
                )}
              </div>
              <p className="text-adaptive-secondary dark:text-gray-400 text-sm">{item.description}</p>
            </Link>
          );
        })}
      </div>

      {/* Gestionnaire de codes de liaison */}
      <div id="link-codes" className="mb-8">
        <LinkCodeManager 
          practitioner={practitioner}
          onUpdatePractitioner={handleUpdatePractitioner}
        />
      </div>

      {/* Patients récents */}
      <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-adaptive dark:text-white mb-4">Patients récents</h2>
        <div className="space-y-3">
          {recentPatients.map((patient, index) => (
            <div key={index} className="flex items-center justify-between p-3 surface-secondary dark:bg-gray-700 rounded-lg">
              <div>
                <p className="font-medium text-adaptive dark:text-white">{patient.name}</p>
                <p className="text-sm text-adaptive-secondary dark:text-gray-400">Dernière visite: {patient.lastVisit}</p>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                patient.status === 'En cours' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' :
                patient.status === 'Suivi' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' :
                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
              }`}>
                {patient.status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PractitionerDashboard;
