import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Calendar, CreditCard, MessageSquare, Settings, BarChart3, FileText, Smile, Link as LinkIcon } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { Practitioner } from '../../types/user';
import LinkCodeManager from '../../components/practitioner/LinkCodeManager';

const PractitionerDashboard: React.FC = () => {
  const { user, updateUser } = useAuth();
  const [practitioner, setPractitioner] = useState<Practitioner>(user as Practitioner);

  const handleUpdatePractitioner = (updatedPractitioner: Practitioner) => {
    setPractitioner(updatedPractitioner);
    updateUser?.(updatedPractitioner);
  };

  const menuItems = [
    {
      title: 'Codes de liaison',
      icon: <LinkIcon className="w-6 h-6" />,
      description: '<PERSON><PERSON><PERSON>rez des codes pour lier vos patients',
      path: '#link-codes',
      isSpecial: true
    },
    {
      title: 'Mes Patients',
      icon: <Users className="w-6 h-6" />,
      description: 'Gérez vos patients et leurs dossiers',
      path: '/practitioner/patients',
      count: practitioner.patients?.length.toString() || '0'
    },
    {
      title: 'Rendez-vous',
      icon: <Calendar className="w-6 h-6" />,
      description: 'Planifiez et gérez vos consultations',
      path: '/practitioner/appointments',
      count: '8'
    },
    {
      title: 'Invisalign',
      icon: <Smile className="w-6 h-6" />,
      description: 'Créez et gérez les cas Invisalign',
      path: '/practitioner/invisalign',
      count: '3'
    },
    {
      title: 'Communications',
      icon: <MessageSquare className="w-6 h-6" />,
      description: 'Messages et notifications patients',
      path: '/practitioner/messages',
      count: '12'
    },
    {
      title: 'Factures',
      icon: <CreditCard className="w-6 h-6" />,
      description: 'Gestion financière et facturation',
      path: '/practitioner/billing'
    },
    {
      title: 'Abonnement',
      icon: <Settings className="w-6 h-6" />,
      description: 'Gérez votre abonnement (24€/mois)',
      path: '/practitioner/subscription'
    },
    {
      title: 'Analytiques',
      icon: <BarChart3 className="w-6 h-6" />,
      description: 'Statistiques de votre pratique',
      path: '/practitioner/analytics'
    },
    {
      title: 'Rapports',
      icon: <FileText className="w-6 h-6" />,
      description: 'Générez des rapports détaillés',
      path: '/practitioner/reports'
    }
  ];

  const recentPatients = [
    { name: 'Marie Dubois', lastVisit: '2024-06-10', status: 'En cours' },
    { name: 'Pierre Martin', lastVisit: '2024-06-08', status: 'Suivi' },
    { name: 'Sophie Laurent', lastVisit: '2024-06-05', status: 'Terminé' }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Tableau de bord praticien</h1>
        <p className="text-gray-600">Gérez votre pratique orthodontique</p>
      </div>

      {/* Stats rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Patients actifs</p>
              <p className="text-2xl font-bold text-gray-800">24</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">RDV aujourd'hui</p>
              <p className="text-2xl font-bold text-gray-800">8</p>
            </div>
            <Calendar className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Messages</p>
              <p className="text-2xl font-bold text-gray-800">12</p>
            </div>
            <MessageSquare className="w-8 h-8 text-purple-500" />
          </div>
        </div>
        <div className="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Revenus ce mois</p>
              <p className="text-2xl font-bold text-gray-800">€4,250</p>
            </div>
            <CreditCard className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Menu principal */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {menuItems.map((item, index) => {
          if (item.isSpecial) {
            return (
              <a
                key={index}
                href={item.path}
                className="p-6 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 relative"
              >
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-white/20 rounded-lg">
                    {item.icon}
                  </div>
                  <h3 className="ml-3 text-lg font-semibold">{item.title}</h3>
                </div>
                <p className="text-white/90 text-sm">{item.description}</p>
              </a>
            );
          }
          
          return (
            <Link
              key={index}
              to={item.path}
              className="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100 dark:border-gray-700 relative"
            >
              <div className="flex items-center mb-4">
                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg text-blue-600 dark:text-blue-400">
                  {item.icon}
                </div>
                <h3 className="ml-3 text-lg font-semibold text-gray-800 dark:text-white">{item.title}</h3>
                {item.count && (
                  <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {item.count}
                  </span>
                )}
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{item.description}</p>
            </Link>
          );
        })}
      </div>

      {/* Gestionnaire de codes de liaison */}
      <div id="link-codes" className="mb-8">
        <LinkCodeManager 
          practitioner={practitioner}
          onUpdatePractitioner={handleUpdatePractitioner}
        />
      </div>

      {/* Patients récents */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Patients récents</h2>
        <div className="space-y-3">
          {recentPatients.map((patient, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <p className="font-medium text-gray-800 dark:text-white">{patient.name}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Dernière visite: {patient.lastVisit}</p>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                patient.status === 'En cours' ? 'bg-blue-100 text-blue-800' :
                patient.status === 'Suivi' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {patient.status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PractitionerDashboard;
