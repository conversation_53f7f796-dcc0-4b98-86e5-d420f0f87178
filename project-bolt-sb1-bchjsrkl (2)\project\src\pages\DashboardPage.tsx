import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import PatientDashboard from './patient/PatientDashboard';
import PractitionerDashboard from './practitioner/PractitionerDashboard';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen surface-secondary dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-adaptive-secondary dark:text-gray-400">Chargement...</p>
        </div>
      </div>
    );
  }

  if (user.role === 'patient') {
    return <PatientDashboard />;
  }

  if (user.role === 'practitioner') {
    return <PractitionerDashboard />;
  }

  // Admin or other roles can be handled here
  return (
    <div className="min-h-screen surface-secondary dark:bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <p className="text-adaptive-secondary dark:text-gray-400">
          Tableau de bord non disponible pour ce rôle.
        </p>
      </div>
    </div>
  );
};

export default DashboardPage;
