import { Practitioner, Patient } from '../types/user';

export class LinkingService {
  // Génère un code de liaison unique pour un praticien
  static generateLinkCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Valide un code de liaison
  static validateLinkCode(code: string, practitioner: Practitioner): boolean {
    if (!practitioner.generatedCodes) return false;
    
    const linkCode = practitioner.generatedCodes.find(
      c => c.code === code && !c.used && new Date() < c.expiresAt
    );
    
    return !!linkCode;
  }

  // Marque un code comme utilisé
  static markCodeAsUsed(code: string, practitioner: Practitioner, patientId: string): Practitioner {
    if (!practitioner.generatedCodes) return practitioner;
    
    const updatedCodes = practitioner.generatedCodes.map(c => 
      c.code === code ? { ...c, used: true, usedBy: patientId } : c
    );
    
    return {
      ...practitioner,
      generatedCodes: updatedCodes,
      patients: [...(practitioner.patients || []), patientId]
    };
  }

  // Crée un nouveau code de liaison pour un praticien
  static createLinkCode(practitioner: Practitioner): { code: string; practitioner: Practitioner } {
    const code = this.generateLinkCode();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // Expire dans 30 jours
    
    const newCode = {
      code,
      createdAt: new Date(),
      expiresAt,
      used: false
    };
    
    const updatedPractitioner = {
      ...practitioner,
      generatedCodes: [...(practitioner.generatedCodes || []), newCode]
    };
    
    return { code, practitioner: updatedPractitioner };
  }

  // Lie un patient à un praticien
  static linkPatientToPractitioner(
    patient: Patient, 
    practitioner: Practitioner, 
    linkCode: string
  ): { patient: Patient; practitioner: Practitioner } | null {
    
    if (!this.validateLinkCode(linkCode, practitioner)) {
      return null;
    }
    
    const updatedPatient: Patient = {
      ...patient,
      practitionerId: practitioner.id,
      linkCode
    };
    
    const updatedPractitioner = this.markCodeAsUsed(linkCode, practitioner, patient.id);
    
    return {
      patient: updatedPatient,
      practitioner: updatedPractitioner
    };
  }

  // Obtient la liste des patients d'un praticien
  static getPractitionerPatients(practitioner: Practitioner, allPatients: Patient[]): Patient[] {
    if (!practitioner.patients) return [];
    
    return allPatients.filter(patient => 
      practitioner.patients?.includes(patient.id)
    );
  }

  // Vérifie si un patient est lié à un praticien spécifique
  static isPatientLinkedToPractitioner(patient: Patient, practitionerId: string): boolean {
    return patient.practitionerId === practitionerId;
  }

  // Obtient les codes actifs d'un praticien
  static getActiveCodes(practitioner: Practitioner): Array<{
    code: string;
    createdAt: Date;
    expiresAt: Date;
    used: boolean;
  }> {
    if (!practitioner.generatedCodes) return [];
    
    return practitioner.generatedCodes.filter(code => 
      !code.used && new Date() < code.expiresAt
    );
  }

  // Révoque un code de liaison
  static revokeCode(practitioner: Practitioner, code: string): Practitioner {
    if (!practitioner.generatedCodes) return practitioner;
    
    const updatedCodes = practitioner.generatedCodes.map(c => 
      c.code === code ? { ...c, used: true } : c
    );
    
    return {
      ...practitioner,
      generatedCodes: updatedCodes
    };
  }
}
