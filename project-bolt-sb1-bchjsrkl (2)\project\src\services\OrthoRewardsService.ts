import { Trophy, Star, Gift, Target, Calendar, Award, Zap, Crown, Medal } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  unlocked: boolean;
  unlockedDate?: string;
  category: 'daily' | 'weekly' | 'monthly' | 'milestone';
  progress?: number;
  maxProgress?: number;
}

interface Reward {
  id: string;
  title: string;
  description: string;
  cost: number;
  category: 'discount' | 'gift' | 'premium' | 'consultation';
  available: boolean;
  claimed: boolean;
  image?: string;
}

interface UserStats {
  totalPoints: number;
  level: number;
  streak: number;
  completedAchievements: number;
  totalAchievements: number;
  nextLevelPoints: number;
  currentLevelPoints: number;
}

interface LeaderboardEntry {
  rank: number;
  userId: string;
  name: string;
  points: number;
  streak: number;
  avatar?: string;
}

class RewardsService {
  private baseUrl = '/api/rewards';

  async getUserStats(userId: string): Promise<UserStats> {
    try {
      const response = await fetch(`${this.baseUrl}/stats/${userId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des statistiques');
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur getUserStats:', error);
      // Retourner des données mock en cas d'erreur
      return {
        totalPoints: 2450,
        level: 8,
        streak: 12,
        completedAchievements: 15,
        totalAchievements: 25,
        nextLevelPoints: 3000,
        currentLevelPoints: 2000
      };
    }
  }

  async getAchievements(userId: string): Promise<Achievement[]> {
    try {
      const response = await fetch(`${this.baseUrl}/achievements/${userId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des succès');
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur getAchievements:', error);
      // Retourner des données mock en cas d'erreur
      return [
        {
          id: '1',
          title: 'Premier pas',
          description: 'Complétez votre premier suivi quotidien',
          icon: 'Star',
          points: 50,
          unlocked: true,
          unlockedDate: '2024-01-15',
          category: 'milestone'
        },
        {
          id: '2',
          title: 'Régularité',
          description: 'Portez vos gouttières 7 jours consécutifs',
          icon: 'Calendar',
          points: 100,
          unlocked: true,
          unlockedDate: '2024-01-22',
          category: 'weekly'
        }
      ];
    }
  }

  async getRewards(userId: string): Promise<Reward[]> {
    try {
      const response = await fetch(`${this.baseUrl}/rewards/${userId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des récompenses');
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur getRewards:', error);
      // Retourner des données mock en cas d'erreur
      return [
        {
          id: '1',
          title: '10% de réduction',
          description: 'Réduction sur votre prochaine consultation',
          cost: 500,
          category: 'discount',
          available: true,
          claimed: false
        },
        {
          id: '2',
          title: 'Kit de nettoyage premium',
          description: 'Kit complet pour l\'entretien de vos gouttières',
          cost: 800,
          category: 'gift',
          available: true,
          claimed: false
        }
      ];
    }
  }

  async claimReward(userId: string, rewardId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/rewards/${userId}/claim`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ rewardId })
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la réclamation de la récompense');
      }

      this.showNotification('Récompense réclamée avec succès !', 'success');
    } catch (error) {
      console.error('Erreur claimReward:', error);
      this.showNotification('Erreur lors de la réclamation de la récompense', 'error');
      throw error;
    }
  }

  async getLeaderboard(): Promise<LeaderboardEntry[]> {
    try {
      const response = await fetch(`${this.baseUrl}/leaderboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération du classement');
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur getLeaderboard:', error);
      // Retourner des données mock en cas d'erreur
      return [
        { rank: 1, userId: '1', name: 'Marie L.', points: 3250, streak: 28 },
        { rank: 2, userId: '2', name: 'Pierre D.', points: 2890, streak: 22 },
        { rank: 3, userId: '3', name: 'Sophie M.', points: 2650, streak: 19 }
      ];
    }
  }

  async updateProgress(userId: string, achievementId: string, progress: number): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/achievements/${userId}/progress`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ achievementId, progress })
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la mise à jour de la progression');
      }

      // Vérifier si un succès a été débloqué
      const achievement = await response.json();
      if (achievement.unlocked) {
        this.showNotification(`Succès débloqué : ${achievement.title}`, 'success');
      }
    } catch (error) {
      console.error('Erreur updateProgress:', error);
      throw error;
    }
  }

  private showNotification(message: string, type: 'success' | 'error') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
      type === 'success' 
        ? 'bg-green-500 text-white' 
        : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  getIconComponent(iconName: string) {
    const icons = {
      Trophy,
      Star,
      Gift,
      Target,
      Calendar,
      Award,
      Zap,
      Crown,
      Medal
    };
    return icons[iconName as keyof typeof icons] || Star;
  }
}

export const rewardsService = new RewardsService();
export type { Achievement, Reward, UserStats, LeaderboardEntry };
