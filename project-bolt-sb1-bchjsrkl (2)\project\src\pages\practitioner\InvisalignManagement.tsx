import React, { useState } from 'react';
import { Plus, Eye, Download, Upload, Clock, CheckCircle, AlertCircle, User, Calendar } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

interface InvisalignCase {
  id: string;
  patientName: string;
  patientId: string;
  caseNumber: string;
  status: 'draft' | 'submitted' | 'in-review' | 'approved' | 'manufacturing' | 'delivered';
  createdDate: string;
  submittedDate?: string;
  estimatedDelivery?: string;
  treatmentType: 'comprehensive' | 'lite' | 'express' | 'first';
  alignersCount?: number;
  notes?: string;
}

const InvisalignManagement: React.FC = () => {
  const { t } = useLanguage();
  const [showNewCaseModal, setShowNewCaseModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [newCase, setNewCase] = useState({
    patientName: '',
    patientAge: '',
    treatmentType: 'comprehensive',
    clinicalNotes: ''
  });

  const cases: InvisalignCase[] = [
    {
      id: '1',
      patientName: 'Marie Dubois',
      patientId: 'MD2024001',
      caseNumber: 'INV-2024-001',
      status: 'manufacturing',
      createdDate: '2024-05-15',
      submittedDate: '2024-05-20',
      estimatedDelivery: '2024-06-25',
      treatmentType: 'comprehensive',
      alignersCount: 24,
      notes: 'Correction de classe II avec extraction'
    },
    {
      id: '2',
      patientName: 'Pierre Martin',
      patientId: 'PM2023002',
      caseNumber: 'INV-2024-002',
      status: 'approved',
      createdDate: '2024-06-01',
      submittedDate: '2024-06-05',
      estimatedDelivery: '2024-06-30',
      treatmentType: 'lite',
      alignersCount: 14
    },
    {
      id: '3',
      patientName: 'Sophie Laurent',
      patientId: 'SL2023003',
      caseNumber: 'INV-2024-003',
      status: 'draft',
      createdDate: '2024-06-10',
      treatmentType: 'express'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'surface-tertiary text-adaptive';
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'in-review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'manufacturing': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'delivered': return 'bg-emerald-100 text-emerald-800';
      default: return 'surface-tertiary text-adaptive';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return t('invisalign.status.draft');
      case 'submitted': return t('invisalign.status.submitted');
      case 'in-review': return t('invisalign.status.inReview');
      case 'approved': return t('invisalign.status.approved');
      case 'manufacturing': return t('invisalign.status.manufacturing');
      case 'delivered': return t('invisalign.status.delivered');
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <Clock className="w-4 h-4" />;
      case 'submitted': return <Upload className="w-4 h-4" />;
      case 'in-review': return <AlertCircle className="w-4 h-4" />;
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'manufacturing': return <Clock className="w-4 h-4" />;
      case 'delivered': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getTreatmentTypeText = (type: string) => {
    switch (type) {
      case 'comprehensive': return t('invisalign.comprehensive');
      case 'lite': return t('invisalign.lite');
      case 'express': return t('invisalign.express');
      case 'first': return t('invisalign.first');
      default: return type;
    }
  };

  const filteredCases = cases.filter(caseItem =>
    selectedStatus === 'all' || caseItem.status === selectedStatus
  );

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setUploadedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files) {
      const newFiles = Array.from(files);
      setUploadedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmitCase = () => {
    // Logique de soumission du cas avec les fichiers
    console.log('Nouveau cas:', newCase);
    console.log('Fichiers uploadés:', uploadedFiles);
    setShowNewCaseModal(false);
    setNewCase({
      patientName: '',
      patientAge: '',
      treatmentType: 'comprehensive',
      clinicalNotes: ''
    });
    setUploadedFiles([]);
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-adaptive">{t('invisalign.title')}</h1>
            <p className="text-adaptive-secondary">{t('invisalign.subtitle')}</p>
          </div>
          <button
            onClick={() => setShowNewCaseModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            {t('invisalign.newCase')}
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-adaptive-secondary">{t('invisalign.activeCases')}</p>
              <p className="text-2xl font-bold text-adaptive">
                {cases.filter(c => !['delivered', 'draft'].includes(c.status)).length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-adaptive-secondary">{t('invisalign.manufacturing')}</p>
              <p className="text-2xl font-bold text-adaptive">
                {cases.filter(c => c.status === 'manufacturing').length}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <AlertCircle className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-adaptive-secondary">{t('invisalign.deliveredThisMonth')}</p>
              <p className="text-2xl font-bold text-adaptive">
                {cases.filter(c => c.status === 'delivered').length}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-adaptive-secondary">{t('invisalign.drafts')}</p>
              <p className="text-2xl font-bold text-adaptive">
                {cases.filter(c => c.status === 'draft').length}
              </p>
            </div>
            <div className="p-3 surface-tertiary rounded-lg">
              <User className="w-6 h-6 text-adaptive-secondary" />
            </div>
          </div>
        </div>
      </div>

      {/* Filtres */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6 mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedStatus('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedStatus === 'all'
                ? 'bg-blue-500 text-white'
                : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
            }`}
          >
            {t('invisalign.allCases')}
          </button>
          {['draft', 'submitted', 'in-review', 'approved', 'manufacturing', 'delivered'].map((status) => (
            <button
              key={status}
              onClick={() => setSelectedStatus(status)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === status
                  ? 'bg-blue-500 text-white'
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              {getStatusText(status)}
            </button>
          ))}
        </div>
      </div>

      {/* Liste des cas */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light">
        <div className="p-6 border-b border-adaptive-light">
          <h2 className="text-xl font-semibold text-adaptive">
            {t('invisalign.cases')} ({filteredCases.length})
          </h2>
        </div>

        <div className="divide-y divide-gray-100">
          {filteredCases.map((caseItem) => (
            <div key={caseItem.id} className="p-6 hover:surface-secondary transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-3">
                    <h3 className="text-lg font-semibold text-adaptive mr-3">
                      {caseItem.patientName}
                    </h3>
                    <span className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(caseItem.status)}`}>
                      {getStatusIcon(caseItem.status)}
                      <span className="ml-1">{getStatusText(caseItem.status)}</span>
                    </span>
                    <span className="ml-3 px-2 py-1 surface-tertiary text-adaptive-secondary rounded text-sm font-mono">
                      {caseItem.caseNumber}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-adaptive-secondary mb-3">
                    <div>
                      <strong>{t('invisalign.type')}:</strong> {getTreatmentTypeText(caseItem.treatmentType)}
                    </div>
                    <div>
                      <strong>{t('invisalign.created')}:</strong> {new Date(caseItem.createdDate).toLocaleDateString('fr-FR')}
                    </div>
                    {caseItem.alignersCount && (
                      <div>
                        <strong>{t('invisalign.aligners')}:</strong> {caseItem.alignersCount}
                      </div>
                    )}
                    {caseItem.estimatedDelivery && (
                      <div className="flex items-center text-blue-600">
                        <Calendar className="w-4 h-4 mr-1" />
                        <strong>{t('invisalign.delivery')}:</strong> {new Date(caseItem.estimatedDelivery).toLocaleDateString('fr-FR')}
                      </div>
                    )}
                  </div>

                  {caseItem.notes && (
                    <p className="text-sm text-adaptive-secondary surface-secondary p-3 rounded-lg">
                      <strong>Notes:</strong> {caseItem.notes}
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-2 ml-6">
                  <button className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors">
                    <Download className="w-5 h-5" />
                  </button>
                  {caseItem.status === 'draft' && (
                    <button className="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors">
                      <Upload className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal de nouveau cas */}
      {showNewCaseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-semibold text-adaptive mb-6">{t('invisalign.newCase')} Invisalign</h3>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                    {t('invisalign.patientName')}
                  </label>
                  <input
                    type="text"
                    value={newCase.patientName}
                    onChange={(e) => setNewCase(prev => ({ ...prev, patientName: e.target.value }))}
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                    placeholder={t('invisalign.patientName')}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                    {t('invisalign.patientAge')}
                  </label>
                  <input
                    type="number"
                    value={newCase.patientAge}
                    onChange={(e) => setNewCase(prev => ({ ...prev, patientAge: e.target.value }))}
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                    placeholder={t('invisalign.patientAge')}
                    min="1"
                    max="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                    {t('invisalign.treatmentType')}
                  </label>
                  <select
                    value={newCase.treatmentType}
                    onChange={(e) => setNewCase(prev => ({ ...prev, treatmentType: e.target.value }))}
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  >
                    <option value="comprehensive">{t('invisalign.comprehensive')}</option>
                    <option value="lite">{t('invisalign.lite')}</option>
                    <option value="express">{t('invisalign.express')}</option>
                    <option value="first">{t('invisalign.first')}</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                  {t('invisalign.clinicalNotes')}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  rows={3}
                  placeholder={t('invisalign.treatmentObjectives')}
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                  {t('invisalign.uploadFiles')}
                </label>
                <div
                  className="border-2 border-dashed border-adaptive rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-adaptive-secondary">
                    {t('invisalign.dragDrop')}
                  </p>
                  <p className="text-xs text-adaptive-tertiary mt-1">
                    {t('invisalign.supportedFormats')}
                  </p>
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    accept="image/*,.pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </div>

                {/* Liste des fichiers uploadés */}
                {uploadedFiles.length > 0 && (
                  <div className="mt-4 space-y-2">
                    <p className="text-sm font-medium text-adaptive-secondary">
                      Fichiers sélectionnés ({uploadedFiles.length})
                    </p>
                    {uploadedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 surface-secondary rounded-lg">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded flex items-center justify-center">
                            <Upload className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-adaptive">{file.name}</p>
                            <p className="text-xs text-adaptive-tertiary">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => removeFile(index)}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-2">
                  {t('invisalign.clinicalNotes')}
                </label>
                <textarea
                  value={newCase.clinicalNotes}
                  onChange={(e) => setNewCase(prev => ({ ...prev, clinicalNotes: e.target.value }))}
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  rows={4}
                  placeholder={t('invisalign.clinicalNotes')}
                ></textarea>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">Checklist avant soumission</h4>
                <div className="space-y-2 text-sm">
                  <label className="flex items-center text-blue-700">
                    <input type="checkbox" className="mr-2" />
                    Photos intra-orales complètes
                  </label>
                  <label className="flex items-center text-blue-700">
                    <input type="checkbox" className="mr-2" />
                    Photos extra-orales
                  </label>
                  <label className="flex items-center text-blue-700">
                    <input type="checkbox" className="mr-2" />
                    Radiographies panoramique
                  </label>
                  <label className="flex items-center text-blue-700">
                    <input type="checkbox" className="mr-2" />
                    Empreintes ou scan 3D
                  </label>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowNewCaseModal(false)}
                className="px-4 py-2 text-adaptive-secondary hover:text-adaptive transition-colors"
              >
                {t('invisalign.cancel')}
              </button>
              <button
                onClick={handleSubmitCase}
                disabled={!newCase.patientName || !newCase.patientAge}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {t('invisalign.submit')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvisalignManagement;
