import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function removeDuplicateKeys() {
  const filePath = path.join(__dirname, 'src', 'contexts', 'LanguageContext.tsx');
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fonction pour nettoyer les doublons dans une section de langue
  function cleanLanguageSection(langCode) {
    const langPattern = new RegExp(`(${langCode}:\\s*{[\\s\\S]*?}),`, 'g');
    const match = content.match(langPattern);
    
    if (match && match[0]) {
      const section = match[0];
      const lines = section.split('\n');
      const seenKeys = new Set();
      const cleanedLines = [];
      
      for (const line of lines) {
        const keyMatch = line.match(/^\s*'([^']+)':/);
        if (keyMatch) {
          const key = keyMatch[1];
          if (!seenKeys.has(key)) {
            seenKeys.add(key);
            cleanedLines.push(line);
          } else {
            console.log(`Suppression du doublon: ${key} dans ${langCode}`);
          }
        } else {
          cleanedLines.push(line);
        }
      }
      
      const cleanedSection = cleanedLines.join('\n');
      content = content.replace(section, cleanedSection);
    }
  }
  
  // Nettoyer chaque langue
  ['fr', 'en', 'es', 'de', 'pt', 'ar', 'zh'].forEach(cleanLanguageSection);
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ Doublons supprimés avec succès !');
}

removeDuplicateKeys();
