import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Brain, Ruler, Target, Download, Upload, Zap, AlertTriangle, CheckCircle } from 'lucide-react';
import { CephalometricAnalysis, CephalometricLandmark, CephalometricMeasurement } from '../types';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

interface CephalometricAnalysisProps {
  patientId: string;
  onAnalysisComplete?: (analysis: CephalometricAnalysis) => void;
}

// Points céphalométriques standards
const STANDARD_LANDMARKS = [
  { name: 'S', description: 'Sella', x: 0, y: 0 },
  { name: 'N', description: 'Nasion', x: 0, y: 0 },
  { name: 'A', description: 'Point A', x: 0, y: 0 },
  { name: 'B', description: 'Point B', x: 0, y: 0 },
  { name: 'Pog', description: 'Pogonion', x: 0, y: 0 },
  { name: 'Me', description: '<PERSON><PERSON>', x: 0, y: 0 },
  { name: 'Go', description: 'Gonion', x: 0, y: 0 },
  { name: 'Or', description: 'Orbitale', x: 0, y: 0 },
  { name: 'Po', description: 'Porion', x: 0, y: 0 },
  { name: 'ANS', description: 'Épine nasale antérieure', x: 0, y: 0 },
  { name: 'PNS', description: 'Épine nasale postérieure', x: 0, y: 0 },
  { name: 'U1', description: 'Incisive supérieure', x: 0, y: 0 },
  { name: 'L1', description: 'Incisive inférieure', x: 0, y: 0 },
];

// Mesures céphalométriques standards avec valeurs normales
const STANDARD_MEASUREMENTS = [
  { name: 'SNA', normal_range: { min: 80, max: 84 }, unit: 'degrees' as const },
  { name: 'SNB', normal_range: { min: 78, max: 82 }, unit: 'degrees' as const },
  { name: 'ANB', normal_range: { min: 1, max: 4 }, unit: 'degrees' as const },
  { name: 'Wits', normal_range: { min: -1, max: 3 }, unit: 'mm' as const },
  { name: 'FMA', normal_range: { min: 22, max: 28 }, unit: 'degrees' as const },
  { name: 'IMPA', normal_range: { min: 88, max: 95 }, unit: 'degrees' as const },
  { name: 'U1-NA (mm)', normal_range: { min: 2, max: 6 }, unit: 'mm' as const },
  { name: 'U1-NA (°)', normal_range: { min: 18, max: 25 }, unit: 'degrees' as const },
  { name: 'L1-NB (mm)', normal_range: { min: 2, max: 6 }, unit: 'mm' as const },
  { name: 'L1-NB (°)', normal_range: { min: 22, max: 28 }, unit: 'degrees' as const },
];

export const CephalometricAnalysis: React.FC<CephalometricAnalysisProps> = ({
  patientId,
  onAnalysisComplete,
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [landmarks, setLandmarks] = useState<CephalometricLandmark[]>([]);
  const [measurements, setMeasurements] = useState<CephalometricMeasurement[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedLandmark, setSelectedLandmark] = useState<string | null>(null);
  const [showMeasurements, setShowMeasurements] = useState(true);
  const [analysisComplete, setAnalysisComplete] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Charger une image céphalométrique
  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        setImageUrl(url);
        setLandmarks([]);
        setMeasurements([]);
        setAnalysisComplete(false);
        toast.success('Image chargée avec succès');
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // Placer un point céphalométrique
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!selectedLandmark || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newLandmark: CephalometricLandmark = {
      name: selectedLandmark,
      x,
      y,
      confidence: 1.0,
      manual: true,
    };

    setLandmarks(prev => {
      const filtered = prev.filter(l => l.name !== selectedLandmark);
      return [...filtered, newLandmark];
    });

    // Passer au point suivant
    const currentIndex = STANDARD_LANDMARKS.findIndex(l => l.name === selectedLandmark);
    if (currentIndex < STANDARD_LANDMARKS.length - 1) {
      setSelectedLandmark(STANDARD_LANDMARKS[currentIndex + 1].name);
    } else {
      setSelectedLandmark(null);
      calculateMeasurements();
    }
  }, [selectedLandmark]);

  // Calculer les mesures céphalométriques
  const calculateMeasurements = useCallback(() => {
    if (landmarks.length < 5) {
      toast.error('Points insuffisants pour les calculs');
      return;
    }

    const newMeasurements: CephalometricMeasurement[] = [];

    // Fonction utilitaire pour trouver un point
    const findLandmark = (name: string) => landmarks.find(l => l.name === name);

    // Calculer SNA
    const S = findLandmark('S');
    const N = findLandmark('N');
    const A = findLandmark('A');
    
    if (S && N && A) {
      const sna = calculateAngle(S, N, A);
      newMeasurements.push({
        name: 'SNA',
        value: Math.round(sna * 10) / 10,
        unit: 'degrees',
        normal_range: { min: 80, max: 84 },
        interpretation: getInterpretation(sna, 80, 84),
      });
    }

    // Calculer SNB
    const B = findLandmark('B');
    if (S && N && B) {
      const snb = calculateAngle(S, N, B);
      newMeasurements.push({
        name: 'SNB',
        value: Math.round(snb * 10) / 10,
        unit: 'degrees',
        normal_range: { min: 78, max: 82 },
        interpretation: getInterpretation(snb, 78, 82),
      });
    }

    // Calculer ANB
    if (A && N && B) {
      const anb = calculateAngle(A, N, B);
      newMeasurements.push({
        name: 'ANB',
        value: Math.round(anb * 10) / 10,
        unit: 'degrees',
        normal_range: { min: 1, max: 4 },
        interpretation: getInterpretation(anb, 1, 4),
      });
    }

    // Calculer FMA (Angle du plan mandibulaire)
    const Go = findLandmark('Go');
    const Me = findLandmark('Me');
    const Or = findLandmark('Or');
    const Po = findLandmark('Po');
    
    if (Go && Me && Or && Po) {
      const fma = calculateAngleBetweenLines(Go, Me, Or, Po);
      newMeasurements.push({
        name: 'FMA',
        value: Math.round(fma * 10) / 10,
        unit: 'degrees',
        normal_range: { min: 22, max: 28 },
        interpretation: getInterpretation(fma, 22, 28),
      });
    }

    setMeasurements(newMeasurements);
    setAnalysisComplete(true);
    toast.success('Analyse céphalométrique terminée');
  }, [landmarks]);

  // Analyse automatique par IA
  const performAIAnalysis = useCallback(async () => {
    if (!imageUrl) {
      toast.error('Aucune image chargée');
      return;
    }

    setIsAnalyzing(true);
    
    try {
      // Simulation d'analyse IA
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Générer des points automatiquement (simulation)
      const autoLandmarks: CephalometricLandmark[] = STANDARD_LANDMARKS.map(landmark => ({
        ...landmark,
        x: Math.random() * 400 + 100,
        y: Math.random() * 400 + 100,
        confidence: 0.85 + Math.random() * 0.15,
        manual: false,
      }));

      setLandmarks(autoLandmarks);
      
      // Calculer les mesures automatiquement
      setTimeout(() => {
        calculateMeasurements();
      }, 500);

      toast.success('Analyse IA terminée');
    } catch (error) {
      toast.error('Erreur lors de l\'analyse IA');
      console.error(error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [imageUrl, calculateMeasurements]);

  // Dessiner les points et mesures sur le canvas
  useEffect(() => {
    if (!canvasRef.current || !imageRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Effacer le canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Dessiner les points
    landmarks.forEach(landmark => {
      ctx.beginPath();
      ctx.arc(landmark.x, landmark.y, 4, 0, 2 * Math.PI);
      ctx.fillStyle = landmark.manual ? '#3B82F6' : '#10B981';
      ctx.fill();
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Label du point
      ctx.fillStyle = '#1F2937';
      ctx.font = '12px Arial';
      ctx.fillText(landmark.name, landmark.x + 8, landmark.y - 8);
    });

    // Dessiner les lignes de mesure si demandé
    if (showMeasurements && landmarks.length > 2) {
      ctx.strokeStyle = '#EF4444';
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);

      // Exemple: ligne SNA
      const S = landmarks.find(l => l.name === 'S');
      const N = landmarks.find(l => l.name === 'N');
      const A = landmarks.find(l => l.name === 'A');

      if (S && N && A) {
        ctx.beginPath();
        ctx.moveTo(S.x, S.y);
        ctx.lineTo(N.x, N.y);
        ctx.lineTo(A.x, A.y);
        ctx.stroke();
      }
    }
  }, [landmarks, showMeasurements]);

  // Fonctions utilitaires pour les calculs
  const calculateAngle = (p1: CephalometricLandmark, vertex: CephalometricLandmark, p2: CephalometricLandmark): number => {
    const angle1 = Math.atan2(p1.y - vertex.y, p1.x - vertex.x);
    const angle2 = Math.atan2(p2.y - vertex.y, p2.x - vertex.x);
    let angle = Math.abs(angle1 - angle2) * (180 / Math.PI);
    return angle > 180 ? 360 - angle : angle;
  };

  const calculateAngleBetweenLines = (
    line1p1: CephalometricLandmark, line1p2: CephalometricLandmark,
    line2p1: CephalometricLandmark, line2p2: CephalometricLandmark
  ): number => {
    const slope1 = (line1p2.y - line1p1.y) / (line1p2.x - line1p1.x);
    const slope2 = (line2p2.y - line2p1.y) / (line2p2.x - line2p1.x);
    const angle = Math.atan(Math.abs((slope1 - slope2) / (1 + slope1 * slope2))) * (180 / Math.PI);
    return angle;
  };

  const getInterpretation = (value: number, min: number, max: number): 'normal' | 'increased' | 'decreased' | 'severe' => {
    if (value >= min && value <= max) return 'normal';
    if (value > max) return value > max + 5 ? 'severe' : 'increased';
    return value < min - 5 ? 'severe' : 'decreased';
  };

  // Générer le rapport d'analyse
  const generateReport = useCallback(() => {
    if (!analysisComplete) return;

    const analysis: CephalometricAnalysis = {
      id: `ceph_${Date.now()}`,
      patientId,
      date: new Date(),
      imageUrl: imageUrl!,
      landmarks,
      measurements,
      analysis: {
        skeletal: {
          sna: measurements.find(m => m.name === 'SNA')?.value || 0,
          snb: measurements.find(m => m.name === 'SNB')?.value || 0,
          anb: measurements.find(m => m.name === 'ANB')?.value || 0,
          wits: 0,
          facial_angle: 0,
          mandibular_plane_angle: measurements.find(m => m.name === 'FMA')?.value || 0,
          classification: determineSkeletalClass(),
        },
        dental: {
          upper_incisor_to_na: 0,
          lower_incisor_to_nb: 0,
          interincisal_angle: 0,
          upper_molar_position: 0,
          lower_molar_position: 0,
        },
        soft_tissue: {
          nasolabial_angle: 0,
          mentolabial_angle: 0,
          lip_protrusion: 0,
          facial_convexity: 0,
        },
      },
      interpretation: generateInterpretation(),
      recommendations: generateRecommendations(),
    };

    onAnalysisComplete?.(analysis);
    toast.success('Rapport généré avec succès');
  }, [analysisComplete, patientId, imageUrl, landmarks, measurements, onAnalysisComplete]);

  const determineSkeletalClass = (): 'Class I' | 'Class II' | 'Class III' => {
    const anb = measurements.find(m => m.name === 'ANB')?.value;
    if (!anb) return 'Class I';
    if (anb > 4) return 'Class II';
    if (anb < 1) return 'Class III';
    return 'Class I';
  };

  const generateInterpretation = (): string => {
    const abnormalMeasurements = measurements.filter(m => m.interpretation !== 'normal');
    if (abnormalMeasurements.length === 0) {
      return 'Analyse céphalométrique dans les normes. Profil squelettique équilibré.';
    }
    
    return `Analyse révèle ${abnormalMeasurements.length} mesure(s) hors normes. ${
      abnormalMeasurements.map(m => `${m.name}: ${m.interpretation}`).join(', ')
    }.`;
  };

  const generateRecommendations = (): string[] => {
    const recommendations: string[] = [];
    const skeletalClass = determineSkeletalClass();
    
    if (skeletalClass === 'Class II') {
      recommendations.push('Considérer un traitement de correction de Classe II');
      recommendations.push('Évaluer la nécessité d\'extractions ou d\'expansion');
    } else if (skeletalClass === 'Class III') {
      recommendations.push('Traitement précoce recommandé si patient en croissance');
      recommendations.push('Évaluer la composante squelettique vs dentaire');
    }

    const fma = measurements.find(m => m.name === 'FMA');
    if (fma && fma.interpretation === 'increased') {
      recommendations.push('Contrôle de la dimension verticale nécessaire');
    }

    return recommendations;
  };

  return (
    <div className="space-y-6">
      {/* Interface de chargement */}
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 text-purple-600 rounded-full">
              <Ruler size={20} />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">Analyse Céphalométrique</h2>
              <p className="text-sm text-gray-600">Analyse radiographique latérale</p>
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Upload size={16} />
              Charger image
            </button>

            {imageUrl && (
              <button
                onClick={performAIAnalysis}
                disabled={isAnalyzing}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                <Brain size={16} />
                {isAnalyzing ? 'Analyse...' : 'IA Auto'}
              </button>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />

        {/* Zone d'analyse */}
        {imageUrl && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Image et canvas */}
            <div className="lg:col-span-2">
              <div className="relative">
                <img
                  ref={imageRef}
                  src={imageUrl}
                  alt="Céphalométrie"
                  className="w-full max-w-lg mx-auto rounded-lg"
                  onLoad={() => {
                    if (canvasRef.current && imageRef.current) {
                      const canvas = canvasRef.current;
                      const img = imageRef.current;
                      canvas.width = img.clientWidth;
                      canvas.height = img.clientHeight;
                    }
                  }}
                />
                <canvas
                  ref={canvasRef}
                  onClick={handleCanvasClick}
                  className="absolute top-0 left-0 cursor-crosshair"
                  style={{ 
                    width: imageRef.current?.clientWidth || 'auto',
                    height: imageRef.current?.clientHeight || 'auto'
                  }}
                />
              </div>

              <div className="flex items-center justify-center gap-4 mt-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={showMeasurements}
                    onChange={(e) => setShowMeasurements(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">Afficher les lignes</span>
                </label>

                {analysisComplete && (
                  <button
                    onClick={generateReport}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
                  >
                    <Download size={16} />
                    Générer rapport
                  </button>
                )}
              </div>
            </div>

            {/* Panneau de contrôle */}
            <div className="space-y-4">
              {/* Sélection des points */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-3">Points céphalométriques</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {STANDARD_LANDMARKS.map(landmark => {
                    const placed = landmarks.find(l => l.name === landmark.name);
                    const isSelected = selectedLandmark === landmark.name;
                    
                    return (
                      <button
                        key={landmark.name}
                        onClick={() => setSelectedLandmark(landmark.name)}
                        className={`w-full text-left p-2 rounded text-sm transition-colors ${
                          isSelected
                            ? 'bg-blue-600 text-white'
                            : placed
                            ? 'bg-green-100 text-green-800'
                            : 'bg-white text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{landmark.name}</span>
                          {placed && <CheckCircle size={14} />}
                        </div>
                        <div className="text-xs opacity-75">{landmark.description}</div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Mesures calculées */}
              {measurements.length > 0 && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Mesures</h3>
                  <div className="space-y-2">
                    {measurements.map(measurement => (
                      <div
                        key={measurement.name}
                        className={`p-2 rounded text-sm ${
                          measurement.interpretation === 'normal'
                            ? 'bg-green-100 text-green-800'
                            : measurement.interpretation === 'severe'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{measurement.name}</span>
                          <span>{measurement.value} {measurement.unit}</span>
                        </div>
                        <div className="text-xs">
                          Normal: {measurement.normal_range.min}-{measurement.normal_range.max} {measurement.unit}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Indicateur de chargement IA */}
      {isAnalyzing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        >
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="animate-spin w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full mx-auto mb-4" />
            <h3 className="font-semibold text-gray-900 mb-2">Analyse IA en cours...</h3>
            <p className="text-sm text-gray-600">Détection automatique des points céphalométriques</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};
