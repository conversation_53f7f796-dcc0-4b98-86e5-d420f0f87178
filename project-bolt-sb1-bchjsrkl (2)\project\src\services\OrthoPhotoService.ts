export interface PhotoUpload {
  file: File;
  type: 'intraoral' | 'extraoral';
  notes?: string;
  patientId: string;
}

export interface Photo {
  id: string;
  url: string;
  type: 'intraoral' | 'extraoral';
  date: string;
  notes?: string;
  patientId: string;
  filename: string;
}

// Service de gestion des photos avec stockage local
export const photoService = {
  async uploadPhoto({ file, type, notes, patientId }: PhotoUpload): Promise<Photo> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const timestamp = Date.now();
        const photo: Photo = {
          id: timestamp.toString(),
          url: reader.result as string,
          type,
          date: new Date().toISOString(),
          notes,
          patientId,
          filename: file.name
        };

        // Stockage local
        const existingPhotos = this.getStoredPhotos();
        existingPhotos.push(photo);
        localStorage.setItem('ortho_photos', JSON.stringify(existingPhotos));

        resolve(photo);
      };
      reader.readAsDataURL(file);
    });
  },

  async getPatientPhotos(patientId: string): Promise<Photo[]> {
    const photos = this.getStoredPhotos();
    return photos
      .filter(photo => photo.patientId === patientId)
      .sort((a, b) => parseInt(b.id) - parseInt(a.id));
  },

  async deletePhoto(photoId: string): Promise<void> {
    const photos = this.getStoredPhotos();
    const filteredPhotos = photos.filter(photo => photo.id !== photoId);
    localStorage.setItem('ortho_photos', JSON.stringify(filteredPhotos));
  },

  async sharePhoto(photo: Photo): Promise<string> {
    // Simule la génération d'un lien de partage
    const shareData = {
      id: photo.id,
      type: photo.type,
      date: photo.date,
      notes: photo.notes
    };
    
    // En production, ceci générerait un vrai lien de partage sécurisé
    return `https://orthoprogress.app/shared/${btoa(JSON.stringify(shareData))}`;
  },

  getStoredPhotos(): Photo[] {
    const stored = localStorage.getItem('ortho_photos');
    return stored ? JSON.parse(stored) : [];
  },

  async exportPhotos(patientId: string, format: 'pdf' | 'zip'): Promise<Blob> {
    const photos = await this.getPatientPhotos(patientId);
    
    if (format === 'pdf') {
      // Simulation d'export PDF
      return new Blob(['PDF export simulation'], { type: 'application/pdf' });
    } else {
      // Simulation d'export ZIP
      return new Blob(['ZIP export simulation'], { type: 'application/zip' });
    }
  },

  async analyzeProgress(patientId: string): Promise<{
    totalPhotos: number;
    intraoralCount: number;
    extraoralCount: number;
    progressScore: number;
    recommendations: string[];
  }> {
    const photos = await this.getPatientPhotos(patientId);
    const intraoralCount = photos.filter(p => p.type === 'intraoral').length;
    const extraoralCount = photos.filter(p => p.type === 'extraoral').length;
    
    const progressScore = Math.min(100, (photos.length * 10));
    
    const recommendations = [];
    if (intraoralCount < 3) {
      recommendations.push('Ajoutez plus de photos intra-orales pour un meilleur suivi');
    }
    if (extraoralCount < 2) {
      recommendations.push('Prenez des photos extra-orales pour documenter les changements faciaux');
    }
    if (photos.length === 0) {
      recommendations.push('Commencez par prendre des photos de référence');
    }

    return {
      totalPhotos: photos.length,
      intraoralCount,
      extraoralCount,
      progressScore,
      recommendations
    };
  }
};
