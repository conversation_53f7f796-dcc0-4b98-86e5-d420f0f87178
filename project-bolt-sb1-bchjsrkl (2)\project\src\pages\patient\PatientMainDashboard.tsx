import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, FileText, Phone, Camera, Book, Users, BarChart2, Sun } from 'lucide-react';

const PatientDashboard: React.FC = () => {
  const menuItems = [
    {
      title: 'Dossier Médical',
      icon: <FileText className="w-6 h-6" />,
      description: 'Consultez votre dossier complet ou demandez des modifications',
      path: '/patient/dossier'
    },
    {
      title: 'Suivi Quotidien',
      icon: <Calendar className="w-6 h-6" />,
      description: 'Suivez votre port d\'appareil et votre brossage',
      path: '/patient/suivi'
    },
    {
      title: 'Rendez-vous',
      icon: <Clock className="w-6 h-6" />,
      description: '<PERSON><PERSON>rez vos rendez-vous et consultations',
      path: '/patient/rendez-vous'
    },
    {
      title: 'Rappels',
      icon: <Phone className="w-6 h-6" />,
      description: 'Programmez vos rappels personnalisés',
      path: '/patient/rappels'
    },
    {
      title: 'Photos',
      icon: <Camera className="w-6 h-6" />,
      description: 'Envoyez des photos de suivi à votre praticien',
      path: '/patient/photos'
    },
    {
      title: 'Éducation',
      icon: <Book className="w-6 h-6" />,
      description: 'Accédez aux ressources éducatives',
      path: '/patient/education'
    },
    {
      title: 'Communauté',
      icon: <Users className="w-6 h-6" />,
      description: 'Échangez avec la communauté',
      path: '/patient/communaute'
    },
    {
      title: 'Analytiques',
      icon: <BarChart2 className="w-6 h-6" />,
      description: 'Visualisez vos statistiques et progrès',
      path: '/patient/analytiques'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-adaptive">Tableau de bord patient</h1>
        <p className="text-adaptive-secondary">Bienvenue sur votre espace personnel</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {menuItems.map((item, index) => {
          const gradientColors = [
            'from-blue-500 to-blue-600',
            'from-teal-500 to-teal-600',
            'from-green-500 to-green-600',
            'from-purple-500 to-purple-600',
            'from-pink-500 to-pink-600',
            'from-indigo-500 to-indigo-600',
            'from-orange-500 to-orange-600',
            'from-cyan-500 to-cyan-600'
          ];

          return (
            <Link
              key={index}
              to={item.path}
              className="group p-6 surface-primary rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-adaptive-light hover:scale-105 hover:border-blue-200 dark:hover:border-blue-700"
            >
              <div className="flex items-center mb-4">
                <div className={`p-3 bg-gradient-to-r ${gradientColors[index % gradientColors.length]} rounded-xl text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  {item.icon}
                </div>
                <h3 className="ml-4 text-lg font-bold text-adaptive group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">{item.title}</h3>
              </div>
              <p className="text-adaptive-secondary text-sm leading-relaxed">{item.description}</p>
              <div className="mt-4 flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <span>Accéder →</span>
              </div>
            </Link>
          );
        })}
      </div>

      <div className="mt-8 p-8 bg-gradient-to-br from-blue-600 via-teal-600 to-green-600 rounded-2xl text-white shadow-2xl relative overflow-hidden">
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Prochain rendez-vous</h2>
                <p className="opacity-90 text-lg font-medium">Consultation de suivi - Dr. Martin</p>
                <p className="text-sm opacity-75 bg-white/20 px-3 py-1 rounded-full inline-block mt-2">
                  📅 15 Juin 2024 à 14:30
                </p>
              </div>
            </div>
            <button className="px-6 py-3 bg-white text-blue-600 rounded-xl font-semibold hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105">
              Voir détails
            </button>
          </div>
          <div className="mt-6 grid grid-cols-3 gap-4">
            <div className="text-center p-3 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-2xl font-bold">7</div>
              <div className="text-sm opacity-75">Jours restants</div>
            </div>
            <div className="text-center p-3 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-2xl font-bold">85%</div>
              <div className="text-sm opacity-75">Compliance</div>
            </div>
            <div className="text-center p-3 bg-white/10 rounded-xl backdrop-blur-sm">
              <div className="text-2xl font-bold">12</div>
              <div className="text-sm opacity-75">Mois de traitement</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;
