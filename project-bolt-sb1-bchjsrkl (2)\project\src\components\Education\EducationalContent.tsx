import React, { useState } from 'react';
import { Play, BookOpen, Download, Star, Clock, Users } from 'lucide-react';

interface EducationalItem {
  id: string;
  type: 'video' | 'article' | 'guide' | 'quiz';
  title: string;
  description: string;
  duration?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  rating: number;
  views: number;
  thumbnail?: string;
  content?: string;
  videoUrl?: string;
}

const EducationalContent: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedItem, setSelectedItem] = useState<EducationalItem | null>(null);

  const categories = [
    { id: 'all', name: 'Tout', icon: BookOpen },
    { id: 'basics', name: 'Bases', icon: BookOpen },
    { id: 'hygiene', name: '<PERSON>y<PERSON><PERSON>', icon: BookOpen },
    { id: 'nutrition', name: 'Nutrition', icon: BookOpen },
    { id: 'exercises', name: 'Exercices', icon: BookOpen },
    { id: 'troubleshooting', name: 'Problèmes', icon: BookOpen }
  ];

  const educationalContent: EducationalItem[] = [
    {
      id: '1',
      type: 'video',
      title: 'Comment bien porter vos aligneurs',
      description: 'Guide complet pour optimiser le port de vos aligneurs orthodontiques',
      duration: '8:30',
      difficulty: 'beginner',
      category: 'basics',
      rating: 4.8,
      views: 1250,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      thumbnail: 'https://images.pexels.com/photos/6812540/pexels-photo-6812540.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: '2',
      type: 'article',
      title: 'Hygiène bucco-dentaire avec un appareil',
      description: 'Techniques et conseils pour maintenir une excellente hygiène',
      duration: '5 min',
      difficulty: 'beginner',
      category: 'hygiene',
      rating: 4.9,
      views: 890,
      content: `
        <h3>Maintenir une hygiène parfaite</h3>
        <p>L'hygiène bucco-dentaire est cruciale pendant un traitement orthodontique...</p>
        <ul>
          <li>Brossez-vous les dents après chaque repas</li>
          <li>Utilisez du fil dentaire quotidiennement</li>
          <li>Rincez avec un bain de bouche adapté</li>
          <li>Nettoyez vos aligneurs régulièrement</li>
        </ul>
      `
    },
    {
      id: '3',
      type: 'guide',
      title: 'Alimentation pendant le traitement',
      description: 'Quoi manger et éviter pour protéger votre appareil',
      duration: '10 min',
      difficulty: 'intermediate',
      category: 'nutrition',
      rating: 4.7,
      views: 654,
      content: `
        <h3>Guide nutritionnel orthodontique</h3>
        <h4>Aliments recommandés:</h4>
        <ul>
          <li>Légumes cuits</li>
          <li>Fruits mous</li>
          <li>Protéines tendres</li>
          <li>Produits laitiers</li>
        </ul>
        <h4>Aliments à éviter:</h4>
        <ul>
          <li>Aliments durs (noix, bonbons)</li>
          <li>Aliments collants (caramel, chewing-gum)</li>
          <li>Aliments très sucrés</li>
        </ul>
      `
    },
    {
      id: '4',
      type: 'video',
      title: 'Exercices pour accélérer le traitement',
      description: 'Exercices simples pour optimiser les mouvements dentaires',
      duration: '12:15',
      difficulty: 'intermediate',
      category: 'exercises',
      rating: 4.6,
      views: 432,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      thumbnail: 'https://images.pexels.com/photos/6812033/pexels-photo-6812033.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: '5',
      type: 'article',
      title: 'Que faire si votre aligneur se casse?',
      description: 'Guide d\'urgence pour gérer les problèmes courants',
      duration: '3 min',
      difficulty: 'beginner',
      category: 'troubleshooting',
      rating: 4.9,
      views: 1100,
      content: `
        <h3>Gestion des urgences orthodontiques</h3>
        <h4>Si votre aligneur se casse:</h4>
        <ol>
          <li>Ne paniquez pas</li>
          <li>Retirez l'aligneur cassé</li>
          <li>Contactez votre praticien</li>
          <li>Utilisez l'aligneur précédent temporairement</li>
        </ol>
        <h4>Prévention:</h4>
        <ul>
          <li>Manipulez avec précaution</li>
          <li>Évitez les températures extrêmes</li>
          <li>Rangez dans l'étui fourni</li>
        </ul>
      `
    },
    {
      id: '6',
      type: 'quiz',
      title: 'Quiz: Connaissances orthodontiques',
      description: 'Testez vos connaissances sur le traitement orthodontique',
      duration: '5 min',
      difficulty: 'intermediate',
      category: 'basics',
      rating: 4.5,
      views: 320
    }
  ];

  const filteredContent = selectedCategory === 'all' 
    ? educationalContent 
    : educationalContent.filter(item => item.category === selectedCategory);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'advanced': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'surface-tertiary text-adaptive';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return Play;
      case 'article': return BookOpen;
      case 'guide': return Download;
      case 'quiz': return Star;
      default: return BookOpen;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h2 className="text-2xl font-bold text-adaptive mb-6">Contenu éducatif</h2>
        
        {/* Categories */}
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContent.map((item) => {
            const IconComponent = getTypeIcon(item.type);
            return (
              <div
                key={item.id}
                onClick={() => setSelectedItem(item)}
                className="surface-secondary rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              >
                {item.thumbnail && (
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-32 object-cover rounded-lg mb-3"
                  />
                )}
                
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-5 w-5 text-blue-600" />
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(item.difficulty)}`}>
                      {item.difficulty}
                    </span>
                  </div>
                  {item.duration && (
                    <div className="flex items-center space-x-1 text-adaptive-tertiary">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">{item.duration}</span>
                    </div>
                  )}
                </div>

                <h3 className="font-semibold text-adaptive mb-2">{item.title}</h3>
                <p className="text-sm text-adaptive-secondary mb-3">{item.description}</p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    {renderStars(item.rating)}
                    <span className="text-xs text-adaptive-tertiary ml-1">({item.rating})</span>
                  </div>
                  <div className="flex items-center space-x-1 text-adaptive-tertiary">
                    <Users className="h-4 w-4" />
                    <span className="text-xs">{item.views}</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Content Modal */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="surface-primary rounded-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-adaptive">{selectedItem.title}</h3>
              <button
                onClick={() => setSelectedItem(null)}
                className="text-adaptive-tertiary hover:text-adaptive-secondary"
              >
                ✕
              </button>
            </div>

            {selectedItem.type === 'video' && selectedItem.videoUrl && (
              <div className="aspect-video mb-4">
                <iframe
                  src={selectedItem.videoUrl}
                  className="w-full h-full rounded-lg"
                  allowFullScreen
                  title={selectedItem.title}
                />
              </div>
            )}

            {selectedItem.content && (
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: selectedItem.content }}
              />
            )}

            {selectedItem.type === 'quiz' && (
              <div className="space-y-4">
                <p className="text-adaptive-secondary">Quiz interactif à venir...</p>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800">
                    Cette fonctionnalité sera bientôt disponible. 
                    Vous pourrez tester vos connaissances orthodontiques de manière interactive.
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between mt-6 pt-4 border-t border-adaptive">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  {renderStars(selectedItem.rating)}
                  <span className="text-sm text-adaptive-secondary ml-1">({selectedItem.rating})</span>
                </div>
                <div className="flex items-center space-x-1 text-adaptive-tertiary">
                  <Users className="h-4 w-4" />
                  <span className="text-sm">{selectedItem.views} vues</span>
                </div>
              </div>
              
              <button
                onClick={() => setSelectedItem(null)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EducationalContent;