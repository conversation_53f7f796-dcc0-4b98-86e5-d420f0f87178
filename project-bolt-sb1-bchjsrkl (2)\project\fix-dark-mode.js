import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mappings pour corriger les classes hardcodées
const colorMappings = {
  // Backgrounds
  'bg-white': 'surface-primary',
  'bg-gray-50': 'surface-secondary',
  'bg-gray-100': 'surface-tertiary',
  'bg-gray-200': 'surface-tertiary',
  'bg-white dark:bg-gray-800': 'surface-elevated',
  'bg-white dark:bg-gray-900': 'surface-primary',
  'bg-gray-50 dark:bg-gray-800': 'surface-secondary',
  'bg-gray-100 dark:bg-gray-700': 'surface-tertiary',
  
  // Text colors
  'text-gray-900': 'text-adaptive',
  'text-gray-800': 'text-adaptive',
  'text-gray-700': 'text-adaptive-secondary',
  'text-gray-600': 'text-adaptive-secondary',
  'text-gray-500': 'text-adaptive-tertiary',
  'text-gray-900 dark:text-white': 'text-adaptive',
  'text-gray-800 dark:text-gray-200': 'text-adaptive',
  'text-gray-600 dark:text-gray-400': 'text-adaptive-secondary',
  
  // Borders
  'border-gray-200': 'border-adaptive',
  'border-gray-300': 'border-adaptive',
  'border-gray-100': 'border-adaptive-light',
  'border-gray-200 dark:border-gray-700': 'border-adaptive',
  'border-gray-300 dark:border-gray-600': 'border-adaptive',
  
  // Status colors with dark mode
  'bg-green-100 text-green-800': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
  'bg-blue-100 text-blue-800': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
  'bg-yellow-100 text-yellow-800': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
  'bg-red-100 text-red-800': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  'bg-purple-100 text-purple-800': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
  'bg-gray-100 text-gray-800': 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
  
  // Individual status colors
  'text-green-600 bg-green-100': 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400',
  'text-blue-600 bg-blue-100': 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400',
  'text-yellow-600 bg-yellow-100': 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400',
  'text-red-600 bg-red-100': 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400',
  'text-purple-600 bg-purple-100': 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400',
  'text-gray-600 bg-gray-100': 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400',
  'text-orange-600 bg-orange-100': 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400',
  'text-emerald-600 bg-emerald-100': 'text-emerald-600 bg-emerald-100 dark:bg-emerald-900/20 dark:text-emerald-400'
};

function fixFileColors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply mappings
    for (const [oldClass, newClass] of Object.entries(colorMappings)) {
      const regex = new RegExp(oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      if (content.includes(oldClass)) {
        content = content.replace(regex, newClass);
        modified = true;
      }
    }
    
    // Additional specific fixes
    const specificFixes = [
      // Fix hardcoded white backgrounds in cards
      {
        from: /className="([^"]*?)bg-white([^"]*?)"/g,
        to: (match, before, after) => `className="${before}surface-elevated${after}"`
      },
      // Fix hardcoded gray text
      {
        from: /className="([^"]*?)text-gray-900([^"]*?)"/g,
        to: (match, before, after) => `className="${before}text-adaptive${after}"`
      },
      {
        from: /className="([^"]*?)text-gray-800([^"]*?)"/g,
        to: (match, before, after) => `className="${before}text-adaptive${after}"`
      },
      {
        from: /className="([^"]*?)text-gray-600([^"]*?)"/g,
        to: (match, before, after) => `className="${before}text-adaptive-secondary${after}"`
      }
    ];
    
    for (const fix of specificFixes) {
      if (content.match(fix.from)) {
        content = content.replace(fix.from, fix.to);
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function scanDirectory(dir) {
  const files = fs.readdirSync(dir);
  let fixedCount = 0;
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      fixedCount += scanDirectory(fullPath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
      if (fixFileColors(fullPath)) {
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

console.log('🔧 Correction automatique du dark mode...\n');

const srcPath = path.join(__dirname, 'src');
const fixedFiles = scanDirectory(srcPath);

console.log(`\n🎉 Correction terminée !`);
console.log(`📊 ${fixedFiles} fichiers corrigés`);
console.log(`✨ Dark mode maintenant parfait !`);
