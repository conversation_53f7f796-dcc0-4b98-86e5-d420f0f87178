import React, { useState } from 'react';
import { Brain, Trophy, Users, ShoppingCart, Ruler, Camera, Zap, Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { CephalometricAnalysisComponent } from '../components/CephalometricAnalysis';
import { GamificationDashboard } from '../components/GamificationDashboard';
import { CommunityHub } from '../components/CommunityHub';
import { Marketplace } from '../components/Marketplace';
import { AIPhotoAnalysis } from '../components/AIPhotoAnalysis';

type FeatureTab = 'ai' | 'cephalometric' | 'gamification' | 'community' | 'marketplace';

export const AdvancedFeatures: React.FC = () => {
  const [activeTab, setActiveTab] = useState<FeatureTab>('ai');

  const features = [
    {
      id: 'ai' as FeatureTab,
      title: 'IA Avancée',
      description: 'Analyse intelligente des photos avec prédictions',
      icon: Brain,
      color: 'from-purple-600 to-blue-600',
      component: <AIPhotoAnalysis />,
    },
    {
      id: 'cephalometric' as FeatureTab,
      title: 'Céphalométrie',
      description: 'Analyse radiographique professionnelle',
      icon: Ruler,
      color: 'from-blue-600 to-cyan-600',
      component: <CephalometricAnalysisComponent patientId="current" />,
    },
    {
      id: 'gamification' as FeatureTab,
      title: 'Gamification',
      description: 'Système de récompenses et défis',
      icon: Trophy,
      color: 'from-yellow-600 to-orange-600',
      component: <GamificationDashboard />,
    },
    {
      id: 'community' as FeatureTab,
      title: 'Communauté',
      description: 'Réseau social orthodontique',
      icon: Users,
      color: 'from-green-600 to-teal-600',
      component: <CommunityHub />,
    },
    {
      id: 'marketplace' as FeatureTab,
      title: 'Marketplace',
      description: 'Boutique de produits orthodontiques',
      icon: ShoppingCart,
      color: 'from-pink-600 to-rose-600',
      component: <Marketplace />,
    },
  ];

  const activeFeature = features.find(f => f.id === activeTab);

  return (
    <div className="min-h-screen surface-secondary">
      {/* Header avec navigation */}
      <div className="surface-primary shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg">
                <Zap size={20} />
              </div>
              <div>
                <h1 className="text-xl font-bold text-adaptive">OrthoProgress Pro</h1>
                <p className="text-sm text-adaptive-secondary">Fonctionnalités avancées</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 px-3 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 rounded-full text-sm">
                <Star size={14} />
                Version Premium
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation des fonctionnalités */}
      <div className="surface-primary border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {features.map((feature) => {
              const Icon = feature.icon;
              const isActive = activeTab === feature.id;
              
              return (
                <button
                  key={feature.id}
                  onClick={() => setActiveTab(feature.id)}
                  className={`flex items-center gap-3 py-4 px-2 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-adaptive-tertiary hover:text-adaptive-secondary hover:border-adaptive'
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    isActive 
                      ? `bg-gradient-to-r ${feature.color} text-white` 
                      : 'surface-tertiary text-adaptive-secondary'
                  }`}>
                    <Icon size={16} />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">{feature.title}</div>
                    <div className="text-xs text-adaptive-tertiary">{feature.description}</div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeFeature?.component}
        </motion.div>
      </div>

      {/* Footer avec informations */}
      <div className="surface-primary border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="font-semibold text-adaptive mb-3">Intelligence Artificielle</h3>
              <ul className="space-y-2 text-sm text-adaptive-secondary">
                <li>• Analyse automatique des photos</li>
                <li>• Prédictions de progression</li>
                <li>• Détection des anomalies</li>
                <li>• Recommandations personnalisées</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-adaptive mb-3">Fonctionnalités Cliniques</h3>
              <ul className="space-y-2 text-sm text-adaptive-secondary">
                <li>• Analyse céphalométrique</li>
                <li>• Mesures orthodontiques</li>
                <li>• Planification de traitement</li>
                <li>• Suivi des progrès</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-adaptive mb-3">Engagement Patient</h3>
              <ul className="space-y-2 text-sm text-adaptive-secondary">
                <li>• Système de gamification</li>
                <li>• Communauté active</li>
                <li>• Marketplace intégrée</li>
                <li>• Support social</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t mt-8 pt-8 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-adaptive-secondary">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Toutes les fonctionnalités sont opérationnelles
            </div>
            <p className="text-xs text-adaptive-tertiary mt-2">
              OrthoProgress Pro - La solution orthodontique la plus avancée au monde
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
