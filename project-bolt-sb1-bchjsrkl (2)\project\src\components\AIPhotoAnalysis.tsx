import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Camera, Zap, Brain, CheckCircle, AlertTriangle, Upload, Download } from 'lucide-react';
import { aiService, ProgressAnalysis, PhotoQuality, PredictiveInsights } from '../services/aiService';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

interface AIPhotoAnalysisProps {
  patientId: string;
  photoType: 'intraoral' | 'extraoral' | 'cephalometric';
  onAnalysisComplete?: (results: {
    quality: PhotoQuality;
    analysis: ProgressAnalysis;
    insights: PredictiveInsights;
  }) => void;
}

export const AIPhotoAnalysis: React.FC<AIPhotoAnalysisProps> = ({
  patientId,
  photoType,
  onAnalysisComplete,
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isRealTimeActive, setIsRealTimeActive] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<{
    quality: PhotoQuality;
    analysis: ProgressAnalysis;
    insights: PredictiveInsights;
  } | null>(null);
  const [realTimeAnalysis, setRealTimeAnalysis] = useState<Partial<ProgressAnalysis> | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Initialiser l'IA au montage du composant
  useEffect(() => {
    const initializeAI = async () => {
      try {
        await aiService.initialize();
        toast.success('IA initialisée avec succès');
      } catch (error) {
        toast.error('Erreur initialisation IA');
        console.error(error);
      }
    };

    initializeAI();

    return () => {
      stopCamera();
    };
  }, []);

  // Démarrer la caméra
  const startCamera = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          facingMode: 'environment', // Caméra arrière sur mobile
        },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
      }

      // Démarrer l'analyse en temps réel
      if (isRealTimeActive) {
        aiService.analyzeRealTime(stream, (analysis) => {
          setRealTimeAnalysis(analysis);
        });
      }

      toast.success('Caméra activée');
    } catch (error) {
      toast.error('Impossible d\'accéder à la caméra');
      console.error(error);
    }
  }, [isRealTimeActive]);

  // Arrêter la caméra
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsRealTimeActive(false);
    setRealTimeAnalysis(null);
  }, []);

  // Capturer une photo
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);

    const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
    setCapturedImage(imageDataUrl);
    
    toast.success('Photo capturée');
  }, []);

  // Analyser une image
  const analyzeImage = useCallback(async (imageSource: string | File) => {
    setIsAnalyzing(true);
    
    try {
      let imageData: string;
      
      if (imageSource instanceof File) {
        imageData = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.readAsDataURL(imageSource);
        });
      } else {
        imageData = imageSource;
      }

      const results = await aiService.analyzePhoto(imageData, patientId, photoType);
      
      setAnalysisResults(results);
      onAnalysisComplete?.(results);
      
      toast.success('Analyse terminée avec succès');
    } catch (error) {
      toast.error('Erreur lors de l\'analyse');
      console.error(error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [patientId, photoType, onAnalysisComplete]);

  // Gérer l'upload de fichier
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      analyzeImage(file);
    }
  }, [analyzeImage]);

  // Basculer l'analyse temps réel
  const toggleRealTime = useCallback(() => {
    setIsRealTimeActive(!isRealTimeActive);
    if (!isRealTimeActive) {
      startCamera();
    } else {
      stopCamera();
    }
  }, [isRealTimeActive, startCamera, stopCamera]);

  // Rendu des résultats de qualité
  const renderQualityResults = (quality: PhotoQuality) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg p-6 shadow-lg"
    >
      <div className="flex items-center gap-3 mb-4">
        <div className={`p-2 rounded-full ${
          quality.score >= 85 ? 'bg-green-100 text-green-600' :
          quality.score >= 70 ? 'bg-yellow-100 text-yellow-600' :
          'bg-red-100 text-red-600'
        }`}>
          {quality.score >= 85 ? <CheckCircle size={20} /> : <AlertTriangle size={20} />}
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">Qualité de l'image</h3>
          <p className="text-sm text-gray-600">Score: {quality.score}/100</p>
        </div>
      </div>

      {quality.issues.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-red-600 mb-2">Problèmes détectés:</h4>
          <ul className="list-disc list-inside text-sm text-red-600 space-y-1">
            {quality.issues.map((issue, index) => (
              <li key={index}>{issue}</li>
            ))}
          </ul>
        </div>
      )}

      {quality.suggestions.length > 0 && (
        <div>
          <h4 className="font-medium text-blue-600 mb-2">Suggestions d'amélioration:</h4>
          <ul className="list-disc list-inside text-sm text-blue-600 space-y-1">
            {quality.suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </motion.div>
  );

  // Rendu des résultats d'analyse
  const renderAnalysisResults = (analysis: ProgressAnalysis) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg p-6 shadow-lg"
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-blue-100 text-blue-600 rounded-full">
          <Brain size={20} />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">Analyse du progrès</h3>
          <p className="text-sm text-gray-600">Évaluation IA complète</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Progrès global */}
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Progrès global</span>
              <span className="text-sm font-bold text-blue-600">{analysis.overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${analysis.overallProgress}%` }}
              />
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Score de compliance</span>
              <span className="text-sm font-bold text-green-600">{analysis.complianceScore}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${analysis.complianceScore}%` }}
              />
            </div>
          </div>
        </div>

        {/* Informations détaillées */}
        <div className="space-y-3">
          <div>
            <span className="text-sm font-medium text-gray-700">Phase actuelle:</span>
            <p className="text-sm text-gray-900">{analysis.currentPhase}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Prochaine étape:</span>
            <p className="text-sm text-gray-900">{analysis.nextMilestone}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Fin estimée:</span>
            <p className="text-sm text-gray-900">
              {analysis.estimatedCompletion.toLocaleDateString('fr-FR')}
            </p>
          </div>
        </div>
      </div>

      {/* Recommandations */}
      {analysis.recommendations.length > 0 && (
        <div className="mt-6">
          <h4 className="font-medium text-gray-900 mb-3">Recommandations:</h4>
          <ul className="space-y-2">
            {analysis.recommendations.map((rec, index) => (
              <li key={index} className="flex items-start gap-2">
                <CheckCircle size={16} className="text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">{rec}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Facteurs de risque */}
      {analysis.riskFactors.length > 0 && (
        <div className="mt-6">
          <h4 className="font-medium text-red-600 mb-3">Facteurs de risque:</h4>
          <ul className="space-y-2">
            {analysis.riskFactors.map((risk, index) => (
              <li key={index} className="flex items-start gap-2">
                <AlertTriangle size={16} className="text-red-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-red-700">{risk}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </motion.div>
  );

  // Rendu des insights prédictifs
  const renderPredictiveInsights = (insights: PredictiveInsights) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg p-6 shadow-lg"
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-100 text-purple-600 rounded-full">
          <Zap size={20} />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">Insights prédictifs</h3>
          <p className="text-sm text-gray-600">Analyse prédictive avancée</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Durée du traitement */}
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {insights.treatmentDuration.estimated} mois
          </div>
          <div className="text-sm text-gray-600 mb-2">Durée estimée</div>
          <div className="text-xs text-blue-600">
            Confiance: {insights.treatmentDuration.confidence}%
          </div>
        </div>

        {/* Risque de compliance */}
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600 mb-1">
            {insights.complianceRisk.score}%
          </div>
          <div className="text-sm text-gray-600 mb-2">Risque compliance</div>
          <div className="text-xs text-yellow-600">
            {insights.complianceRisk.score > 70 ? 'Élevé' : 
             insights.complianceRisk.score > 40 ? 'Modéré' : 'Faible'}
          </div>
        </div>

        {/* Risque de complications */}
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600 mb-1">
            {insights.complications.probability}%
          </div>
          <div className="text-sm text-gray-600 mb-2">Risque complications</div>
          <div className="text-xs text-red-600">
            {insights.complications.probability > 30 ? 'Élevé' : 
             insights.complications.probability > 15 ? 'Modéré' : 'Faible'}
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Interface de capture */}
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 text-blue-600 rounded-full">
              <Camera size={20} />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">Analyse IA des photos</h2>
              <p className="text-sm text-gray-600">Type: {photoType}</p>
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={toggleRealTime}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                isRealTimeActive
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isRealTimeActive ? 'Arrêter' : 'Temps réel'}
            </button>

            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <Upload size={16} />
              Importer
            </button>
          </div>
        </div>

        {/* Vidéo en temps réel */}
        {isRealTimeActive && (
          <div className="relative mb-4">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full max-w-md mx-auto rounded-lg"
            />
            
            {realTimeAnalysis && (
              <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white p-2 rounded">
                <div className="text-sm">
                  Qualité: {realTimeAnalysis.overallProgress || 0}%
                </div>
              </div>
            )}

            <button
              onClick={capturePhoto}
              className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white text-gray-900 p-3 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
            >
              <Camera size={24} />
            </button>
          </div>
        )}

        {/* Image capturée */}
        {capturedImage && (
          <div className="mb-4">
            <img
              src={capturedImage}
              alt="Photo capturée"
              className="w-full max-w-md mx-auto rounded-lg"
            />
            <div className="flex justify-center gap-2 mt-4">
              <button
                onClick={() => analyzeImage(capturedImage)}
                disabled={isAnalyzing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                <Brain size={16} />
                {isAnalyzing ? 'Analyse...' : 'Analyser'}
              </button>
              <button
                onClick={() => setCapturedImage(null)}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Nouvelle photo
              </button>
            </div>
          </div>
        )}

        <canvas ref={canvasRef} className="hidden" />
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>

      {/* Résultats d'analyse */}
      <AnimatePresence>
        {analysisResults && (
          <div className="space-y-6">
            {renderQualityResults(analysisResults.quality)}
            {renderAnalysisResults(analysisResults.analysis)}
            {renderPredictiveInsights(analysisResults.insights)}
          </div>
        )}
      </AnimatePresence>

      {/* Indicateur de chargement */}
      {isAnalyzing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        >
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="animate-spin w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4" />
            <h3 className="font-semibold text-gray-900 mb-2">Analyse en cours...</h3>
            <p className="text-sm text-gray-600">L'IA analyse votre photo</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};
