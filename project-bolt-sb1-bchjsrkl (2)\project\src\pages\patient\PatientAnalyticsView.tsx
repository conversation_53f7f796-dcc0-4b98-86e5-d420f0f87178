import React from 'react';
import { motion } from 'framer-motion';
import { ChartBarIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';
import AdvancedAnalytics from '../../components/Analytics/AdvancedAnalytics';

const Analytics: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen surface-primary p-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
              <ChartBarIcon className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-adaptive">
                {t('analytics.title')}
              </h1>
              <p className="text-adaptive-secondary">
                {t('analytics.overview')} - {t('analytics.insights')}
              </p>
            </div>
          </div>
        </motion.div>

        <AdvancedAnalytics />
      </div>
    </div>
  );
};

export default Analytics;


