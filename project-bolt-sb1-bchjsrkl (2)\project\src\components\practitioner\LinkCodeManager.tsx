import React, { useState } from 'react';
import { Co<PERSON>, Plus, Trash2, Clock, CheckCircle, Users } from 'lucide-react';
import { Practitioner } from '../../types/user';
import { LinkingService } from '../../services/linkingService';

interface LinkCodeManagerProps {
  practitioner: Practitioner;
  onUpdatePractitioner: (practitioner: Practitioner) => void;
}

const LinkCodeManager: React.FC<LinkCodeManagerProps> = ({
  practitioner,
  onUpdatePractitioner
}) => {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const activeCodes = LinkingService.getActiveCodes(practitioner);
  const usedCodes = practitioner.generatedCodes?.filter(code => code.used) || [];

  const handleGenerateCode = () => {
    const { code, practitioner: updatedPractitioner } = LinkingService.createLinkCode(practitioner);
    onUpdatePractitioner(updatedPractitioner);
  };

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Erreur lors de la copie:', err);
    }
  };

  const handleRevokeCode = (code: string) => {
    const updatedPractitioner = LinkingService.revokeCode(practitioner, code);
    onUpdatePractitioner(updatedPractitioner);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const isExpiringSoon = (expiresAt: Date) => {
    const daysUntilExpiry = Math.ceil((new Date(expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7;
  };

  return (
    <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-soft p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-adaptive dark:text-white">
            Codes de liaison patients
          </h3>
          <p className="text-sm text-adaptive-secondary dark:text-gray-400">
            Générez des codes pour permettre aux patients de se lier à votre cabinet
          </p>
        </div>
        <button
          onClick={handleGenerateCode}
          className="btn-primary btn-hover-lift flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Nouveau code
        </button>
      </div>

      {/* Codes actifs */}
      <div className="mb-8">
        <h4 className="text-md font-medium text-adaptive dark:text-white mb-4 flex items-center gap-2">
          <Clock className="w-4 h-4 text-green-500" />
          Codes actifs ({activeCodes.length})
        </h4>
        
        {activeCodes.length === 0 ? (
          <div className="text-center py-8 text-adaptive-tertiary dark:text-gray-400">
            <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Aucun code actif</p>
            <p className="text-sm">Générez un code pour permettre aux patients de se lier à votre cabinet</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {activeCodes.map((codeData) => (
              <div
                key={codeData.code}
                className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                  isExpiringSoon(codeData.expiresAt)
                    ? 'border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20'
                    : 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <code className="text-2xl font-mono font-bold text-adaptive dark:text-white surface-primary dark:bg-gray-700 px-3 py-1 rounded border">
                        {codeData.code}
                      </code>
                      <button
                        onClick={() => handleCopyCode(codeData.code)}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          copiedCode === codeData.code
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                            : 'surface-tertiary dark:bg-gray-700 text-adaptive-secondary dark:text-gray-400 hover:surface-tertiary dark:hover:bg-gray-600'
                        }`}
                        title="Copier le code"
                      >
                        {copiedCode === codeData.code ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-adaptive-secondary dark:text-gray-400">
                      <span>Créé le {formatDate(codeData.createdAt)}</span>
                      <span className={isExpiringSoon(codeData.expiresAt) ? 'text-orange-600 dark:text-orange-400 font-medium' : ''}>
                        Expire le {formatDate(codeData.expiresAt)}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => handleRevokeCode(codeData.code)}
                    className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                    title="Révoquer le code"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Codes utilisés */}
      {usedCodes.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-adaptive dark:text-white mb-4 flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-blue-500" />
            Codes utilisés ({usedCodes.length})
          </h4>
          <div className="grid gap-3">
            {usedCodes.slice(0, 5).map((codeData) => (
              <div
                key={codeData.code}
                className="p-3 rounded-lg surface-secondary dark:bg-gray-700/50 border border-adaptive dark:border-gray-600"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <code className="font-mono font-medium text-adaptive-secondary dark:text-gray-300">
                      {codeData.code}
                    </code>
                    <span className="text-sm text-adaptive-tertiary dark:text-gray-400">
                      Utilisé le {formatDate(codeData.createdAt)}
                    </span>
                  </div>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
              </div>
            ))}
            {usedCodes.length > 5 && (
              <p className="text-sm text-adaptive-tertiary dark:text-gray-400 text-center">
                ... et {usedCodes.length - 5} autres codes utilisés
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LinkCodeManager;
