import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Users, Calendar, MessageSquare, CreditCard, TrendingUp, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';

const PractitionerDashboard: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const stats = [
    {
      title: 'Patients actifs',
      value: '24',
      icon: Users,
      color: 'text-blue-600',
      bg: 'bg-blue-100'
    },
    {
      title: 'RDV cette semaine',
      value: '12',
      icon: Calendar,
      color: 'text-green-600',
      bg: 'bg-green-100'
    },
    {
      title: 'Messages non lus',
      value: '3',
      icon: MessageSquare,
      color: 'text-orange-600',
      bg: 'bg-orange-100'
    },
    {
      title: 'Taux de compliance',
      value: '87%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bg: 'bg-purple-100'
    }
  ];

  const quickActions = [
    {
      title: 'Gérer les patients',
      description: 'Voir tous vos patients et leur progression',
      icon: Users,
      link: '/practitioner/patients',
      color: 'bg-blue-500'
    },
    {
      title: 'Rendez-vous',
      description: 'Planifier et gérer les consultations',
      icon: Calendar,
      link: '/practitioner/appointments',
      color: 'bg-green-500'
    },
    {
      title: 'Forum praticiens',
      description: 'Échanger avec vos confrères',
      icon: MessageSquare,
      link: '/forum',
      color: 'bg-purple-500'
    },
    {
      title: 'Mon abonnement',
      description: 'Gérer votre abonnement',
      icon: CreditCard,
      link: '/practitioner/subscription',
      color: 'bg-orange-500'
    }
  ];

  const isTrialExpired = user?.subscriptionStatus === 'expired';
  const isOnTrial = user?.subscriptionStatus === 'trial';

  return (
    <div className="space-y-6">
      {/* Trial Warning */}
      {isTrialExpired && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-red-600" />
            <div>
              <h3 className="font-medium text-red-800">Période d'essai expirée</h3>
              <p className="text-sm text-red-600">
                Votre accès aux fonctionnalités praticien est suspendu. 
                <Link to="/subscription\" className="font-medium underline ml-1">
                  Souscrire un abonnement
                </Link>
              </p>
            </div>
          </div>
        </div>
      )}

      {isOnTrial && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-yellow-600" />
            <div>
              <h3 className="font-medium text-yellow-800">Période d'essai active</h3>
              <p className="text-sm text-yellow-600">
                Il vous reste quelques jours d'essai gratuit.
                <Link to="/subscription" className="font-medium underline ml-1">
                  Voir les options d'abonnement
                </Link>
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 text-white">
        <h1 className="text-3xl font-bold mb-2">
          {t('dashboard.welcome')}, Dr. {user?.fullName}!
        </h1>
        <p className="text-blue-100">
          Gérez vos patients et suivez leur progression orthodontique
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-adaptive-secondary">{stat.title}</p>
                <p className="text-2xl font-bold text-adaptive mt-1">{stat.value}</p>
              </div>
              <div className={`${stat.bg} p-3 rounded-lg`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            className={`surface-primary rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow border border-adaptive-light group ${
              isTrialExpired && action.link !== '/subscription' ? 'opacity-50 pointer-events-none' : ''
            }`}
          >
            <div className="flex items-start space-x-4">
              <div className={`${action.color} p-3 rounded-lg group-hover:scale-110 transition-transform`}>
                <action.icon className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-adaptive mb-1">{action.title}</h3>
                <p className="text-sm text-adaptive-secondary">{action.description}</p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h3 className="text-lg font-semibold text-adaptive mb-4">Activité récente</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-3 surface-secondary rounded-lg">
            <div className="bg-green-100 p-2 rounded-full">
              <Users className="h-4 w-4 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-adaptive">Marie Dubois a envoyé de nouvelles photos</p>
              <p className="text-xs text-adaptive-tertiary">Il y a 2 heures</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 surface-secondary rounded-lg">
            <div className="bg-blue-100 p-2 rounded-full">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-adaptive">Rendez-vous confirmé avec Pierre Martin</p>
              <p className="text-xs text-adaptive-tertiary">Il y a 4 heures</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PractitionerDashboard;