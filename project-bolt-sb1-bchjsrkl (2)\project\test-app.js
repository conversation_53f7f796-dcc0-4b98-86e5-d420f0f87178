// Script de test automatisé pour OrthoProgress
// Ce script teste toutes les fonctionnalités principales de l'application

console.log('🚀 Début des tests OrthoProgress...');

// Test 1: Vérification de l'état initial de l'application
function testInitialState() {
  console.log('\n📋 Test 1: État initial de l\'application');
  
  // Vérifier si on est redirigé vers /auth quand non connecté
  const currentPath = window.location.pathname;
  console.log(`- Chemin actuel: ${currentPath}`);
  
  if (currentPath === '/auth' || currentPath === '/') {
    console.log('✅ Redirection vers auth correcte');
  } else {
    console.log('❌ Problème de redirection');
  }
}

// Test 2: Test du thème sombre/clair
function testThemeToggle() {
  console.log('\n🌙 Test 2: Basculement de thème');
  
  const themeButton = document.querySelector('button[class*="fixed top-4 right-4"]');
  if (themeButton) {
    console.log('✅ Bouton de thème trouvé');
    
    // Simuler un clic
    const initialTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    console.log(`- Thème initial: ${initialTheme}`);
    
    themeButton.click();
    
    setTimeout(() => {
      const newTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
      console.log(`- Nouveau thème: ${newTheme}`);
      
      if (initialTheme !== newTheme) {
        console.log('✅ Basculement de thème fonctionne');
      } else {
        console.log('❌ Problème avec le basculement de thème');
      }
    }, 100);
  } else {
    console.log('❌ Bouton de thème non trouvé');
  }
}

// Test 3: Test des formulaires d'authentification
function testAuthForms() {
  console.log('\n🔐 Test 3: Formulaires d\'authentification');
  
  // Vérifier la présence du formulaire de connexion
  const loginForm = document.querySelector('form');
  if (loginForm) {
    console.log('✅ Formulaire de connexion trouvé');
    
    // Vérifier les champs
    const emailField = document.querySelector('input[type="email"]');
    const passwordField = document.querySelector('input[type="password"]');
    const roleButtons = document.querySelectorAll('button[type="button"]');
    
    console.log(`- Champ email: ${emailField ? '✅' : '❌'}`);
    console.log(`- Champ mot de passe: ${passwordField ? '✅' : '❌'}`);
    console.log(`- Boutons de rôle: ${roleButtons.length >= 2 ? '✅' : '❌'}`);
    
    // Test de sélection de rôle
    if (roleButtons.length >= 2) {
      console.log('- Test de sélection de rôle...');
      roleButtons[1].click(); // Cliquer sur praticien
      setTimeout(() => {
        roleButtons[0].click(); // Retour à patient
        console.log('✅ Sélection de rôle fonctionne');
      }, 100);
    }
  } else {
    console.log('❌ Formulaire de connexion non trouvé');
  }
}

// Test 4: Test de connexion simulée
function testLogin() {
  console.log('\n👤 Test 4: Connexion simulée');
  
  const emailField = document.querySelector('input[type="email"]');
  const passwordField = document.querySelector('input[type="password"]');
  const submitButton = document.querySelector('button[type="submit"]');
  
  if (emailField && passwordField && submitButton) {
    console.log('✅ Tous les éléments de connexion trouvés');
    
    // Remplir les champs
    emailField.value = '<EMAIL>';
    passwordField.value = 'password123';
    
    // Déclencher les événements
    emailField.dispatchEvent(new Event('input', { bubbles: true }));
    passwordField.dispatchEvent(new Event('input', { bubbles: true }));
    
    console.log('- Champs remplis avec des données de test');
    console.log('- Prêt pour la soumission (non exécutée automatiquement)');
  } else {
    console.log('❌ Éléments de connexion manquants');
  }
}

// Test 5: Test de la responsivité
function testResponsiveness() {
  console.log('\n📱 Test 5: Responsivité');
  
  const originalWidth = window.innerWidth;
  console.log(`- Largeur originale: ${originalWidth}px`);
  
  // Simuler différentes tailles d'écran
  const testSizes = [
    { width: 375, name: 'Mobile' },
    { width: 768, name: 'Tablette' },
    { width: 1024, name: 'Desktop' },
    { width: 1920, name: 'Large Desktop' }
  ];
  
  testSizes.forEach(size => {
    // Note: On ne peut pas vraiment redimensionner la fenêtre en JS pour des raisons de sécurité
    // Mais on peut vérifier les classes CSS responsives
    console.log(`- Test ${size.name} (${size.width}px): Vérification des classes CSS`);
    
    const responsiveElements = document.querySelectorAll('[class*="sm:"], [class*="md:"], [class*="lg:"], [class*="xl:"]');
    console.log(`  Classes responsives trouvées: ${responsiveElements.length}`);
  });
  
  console.log('✅ Test de responsivité terminé');
}

// Test 6: Test des performances
function testPerformance() {
  console.log('\n⚡ Test 6: Performances');
  
  // Mesurer le temps de chargement
  const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
  console.log(`- Temps de chargement: ${loadTime}ms`);
  
  // Vérifier les ressources
  const resources = performance.getEntriesByType('resource');
  console.log(`- Nombre de ressources chargées: ${resources.length}`);
  
  // Vérifier les erreurs dans la console
  const errors = [];
  const originalError = console.error;
  console.error = function(...args) {
    errors.push(args.join(' '));
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.log(`- Erreurs détectées: ${errors.length}`);
    if (errors.length === 0) {
      console.log('✅ Aucune erreur de console détectée');
    } else {
      console.log('❌ Erreurs trouvées:', errors);
    }
    console.error = originalError;
  }, 1000);
}

// Fonction principale de test
function runAllTests() {
  console.log('🧪 Exécution de tous les tests...\n');
  
  testInitialState();
  
  setTimeout(() => testThemeToggle(), 500);
  setTimeout(() => testAuthForms(), 1000);
  setTimeout(() => testLogin(), 1500);
  setTimeout(() => testResponsiveness(), 2000);
  setTimeout(() => testPerformance(), 2500);
  
  setTimeout(() => {
    console.log('\n🎉 Tests terminés !');
    console.log('\n📊 Résumé des tests:');
    console.log('- État initial: Vérifié');
    console.log('- Thème: Testé');
    console.log('- Authentification: Testée');
    console.log('- Responsivité: Vérifiée');
    console.log('- Performances: Analysées');
  }, 3000);
}

// Démarrer les tests
runAllTests();
