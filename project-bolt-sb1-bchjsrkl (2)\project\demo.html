<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OrthoProgress - Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function LoginForm() {
            const [userType, setUserType] = useState('');
            const [email, setEmail] = useState('');
            const [password, setPassword] = useState('');
            const [patientCode, setPatientCode] = useState('');
            const [isLogin, setIsLogin] = useState(true);

            const handleSubmit = (e) => {
                e.preventDefault();
                if (userType === 'patient') {
                    alert(`Connexion Patient avec le code: ${patientCode}`);
                } else if (userType === 'practitioner') {
                    alert(`Connexion Praticien: ${email}`);
                }
            };

            return (
                <div className="min-h-screen gradient-bg flex items-center justify-center p-4">
                    <div className="card w-full max-w-md p-8">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold text-gray-800 mb-2">OrthoProgress</h1>
                            <p className="text-gray-600">Plateforme de suivi orthodontique</p>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Type d'utilisateur
                                </label>
                                <div className="grid grid-cols-2 gap-3">
                                    <button
                                        type="button"
                                        onClick={() => setUserType('patient')}
                                        className={`p-3 rounded-lg border-2 transition-all ${
                                            userType === 'patient'
                                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                    >
                                        👤 Patient
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => setUserType('practitioner')}
                                        className={`p-3 rounded-lg border-2 transition-all ${
                                            userType === 'practitioner'
                                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                    >
                                        👨‍⚕️ Praticien
                                    </button>
                                </div>
                            </div>

                            {userType === 'patient' && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Code patient
                                    </label>
                                    <input
                                        type="text"
                                        value={patientCode}
                                        onChange={(e) => setPatientCode(e.target.value)}
                                        className="input-field"
                                        placeholder="Entrez votre code patient"
                                        required
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Code fourni par votre praticien
                                    </p>
                                </div>
                            )}

                            {userType === 'practitioner' && (
                                <>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Email
                                        </label>
                                        <input
                                            type="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            className="input-field"
                                            placeholder="<EMAIL>"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Mot de passe
                                        </label>
                                        <input
                                            type="password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            className="input-field"
                                            placeholder="••••••••"
                                            required
                                        />
                                    </div>
                                </>
                            )}

                            {userType && (
                                <button type="submit" className="btn-primary w-full">
                                    Se connecter
                                </button>
                            )}
                        </form>

                        <div className="mt-6 text-center">
                            <p className="text-sm text-gray-600">
                                {isLogin ? "Pas encore de compte ?" : "Déjà un compte ?"}
                                <button
                                    onClick={() => setIsLogin(!isLogin)}
                                    className="ml-1 text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    {isLogin ? "S'inscrire" : "Se connecter"}
                                </button>
                            </p>
                        </div>

                        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                            <h3 className="font-semibold text-blue-800 mb-2">Fonctionnalités :</h3>
                            <ul className="text-sm text-blue-700 space-y-1">
                                <li>• Interface Patient & Praticien</li>
                                <li>• Suivi quotidien des appareils</li>
                                <li>• Gestion des rendez-vous</li>
                                <li>• Communauté professionnelle</li>
                                <li>• Mode sombre/clair</li>
                            </ul>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<LoginForm />, document.getElementById('root'));
    </script>
</body>
</html>
