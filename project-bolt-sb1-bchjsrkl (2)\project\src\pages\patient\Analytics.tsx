import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Clock, Calendar, Target, Award, CheckCircle2, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface AnalyticsData {
  wearTime: {
    daily: number[];
    weekly: number;
    monthly: number;
    target: number;
  };
  brushingTime: {
    daily: number[];
    weekly: number;
    monthly: number;
  };
  progress: {
    current: number;
    total: number;
    estimatedCompletion: string;
  };
  compliance: {
    score: number;
    trend: 'up' | 'down' | 'stable';
    lastWeek: number;
  };
  achievements: {
    total: number;
    recent: {
      title: string;
      date: string;
      icon: string;
    }[];
  };
  alerts: {
    id: string;
    type: 'warning' | 'info';
    message: string;
    date: string;
  }[];
}

const Analytics: React.FC = () => {
  const { user } = useAuth();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  // Données simulées
  useEffect(() => {
    const mockData: AnalyticsData = {
      wearTime: {
        daily: [22, 21, 23, 20, 22, 21, 23],
        weekly: 21.5,
        monthly: 22,
        target: 22
      },
      brushingTime: {
        daily: [6, 8, 7, 6, 8, 7, 6],
        weekly: 7,
        monthly: 7.2
      },
      progress: {
        current: 15,
        total: 24,
        estimatedCompletion: '2024-09-15'
      },
      compliance: {
        score: 92,
        trend: 'up',
        lastWeek: 88
      },
      achievements: {
        total: 12,
        recent: [
          {
            title: 'Port parfait - 30 jours',
            date: '2024-02-15',
            icon: '🌟'
          },
          {
            title: 'Champion du brossage',
            date: '2024-02-10',
            icon: '🦷'
          },
          {
            title: 'Photos régulières',
            date: '2024-02-05',
            icon: '📸'
          }
        ]
      },
      alerts: [
        {
          id: '1',
          type: 'warning',
          message: 'Temps de port légèrement en dessous de l\'objectif hier',
          date: '2024-02-15'
        },
        {
          id: '2',
          type: 'info',
          message: 'Prochain changement de gouttière dans 2 jours',
          date: '2024-02-14'
        }
      ]
    };

    setData(mockData);
    setLoading(false);
  }, []);

  const formatTime = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h${m > 0 ? ` ${m}min` : ''}`;
  };

  const getProgressColor = (value: number, target: number) => {
    const percentage = (value / target) * 100;
    if (percentage >= 95) return 'text-green-500';
    if (percentage >= 80) return 'text-yellow-500';
    return 'text-red-500';
  };

  if (loading || !data) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">Analytiques</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Suivez votre progression et vos performances
        </p>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Temps de port */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Clock className="w-6 h-6 text-blue-500" />
            </div>
            <span className={`text-sm font-medium ${
              data.wearTime.daily[6] >= data.wearTime.target
                ? 'text-green-500'
                : 'text-yellow-500'
            }`}>
              {data.wearTime.daily[6] >= data.wearTime.target ? 'Objectif atteint' : 'En dessous de l\'objectif'}
            </span>
          </div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Temps de port quotidien
          </h3>
          <div className="flex items-baseline">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatTime(data.wearTime.daily[6])}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
              / {formatTime(data.wearTime.target)}
            </span>
          </div>
        </div>

        {/* Score de conformité */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Target className="w-6 h-6 text-green-500" />
            </div>
            <div className="flex items-center">
              <TrendingUp className={`w-4 h-4 ${
                data.compliance.trend === 'up'
                  ? 'text-green-500'
                  : data.compliance.trend === 'down'
                  ? 'text-red-500'
                  : 'text-yellow-500'
              } mr-1`} />
              <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                +{data.compliance.score - data.compliance.lastWeek}%
              </span>
            </div>
          </div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Score de conformité
          </h3>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {data.compliance.score}%
          </div>
        </div>

        {/* Progression */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <BarChart3 className="w-6 h-6 text-purple-500" />
            </div>
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Gouttière {data.progress.current}/{data.progress.total}
            </span>
          </div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Progression du traitement
          </h3>
          <div className="relative pt-1">
            <div className="flex mb-2 items-center justify-between">
              <div>
                <span className="text-xs font-semibold inline-block text-purple-600">
                  {Math.round((data.progress.current / data.progress.total) * 100)}%
                </span>
              </div>
            </div>
            <div className="flex h-2 mb-4 overflow-hidden rounded bg-purple-100 dark:bg-purple-900/20">
              <div
                style={{ width: `${(data.progress.current / data.progress.total) * 100}%` }}
                className="flex flex-col justify-center overflow-hidden bg-purple-500"
              ></div>
            </div>
          </div>
        </div>

        {/* Récompenses */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <Award className="w-6 h-6 text-yellow-500" />
            </div>
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Total: {data.achievements.total}
            </span>
          </div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Dernières récompenses
          </h3>
          <div className="space-y-2">
            {data.achievements.recent.slice(0, 2).map((achievement, index) => (
              <div key={index} className="flex items-center">
                <span className="text-xl mr-2">{achievement.icon}</span>
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {achievement.title}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Graphiques détaillés */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Temps de port hebdomadaire */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Temps de port hebdomadaire
            </h3>
            <div className="flex gap-2">
              <button
                onClick={() => setTimeframe('daily')}
                className={`px-3 py-1 rounded-lg text-sm font-medium ${
                  timeframe === 'daily'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                7 jours
              </button>
              <button
                onClick={() => setTimeframe('weekly')}
                className={`px-3 py-1 rounded-lg text-sm font-medium ${
                  timeframe === 'weekly'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                4 semaines
              </button>
              <button
                onClick={() => setTimeframe('monthly')}
                className={`px-3 py-1 rounded-lg text-sm font-medium ${
                  timeframe === 'monthly'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                12 mois
              </button>
            </div>
          </div>
          <div className="h-64 flex items-end justify-between">
            {data.wearTime.daily.map((hours, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div
                  style={{ height: `${(hours / 24) * 100}%` }}
                  className={`w-4/5 rounded-t ${
                    hours >= data.wearTime.target
                      ? 'bg-blue-500'
                      : 'bg-yellow-500'
                  }`}
                ></div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  J-{6 - index}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Alertes et notifications */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Alertes et notifications
          </h3>
          <div className="space-y-4">
            {data.alerts.map(alert => (
              <div
                key={alert.id}
                className={`flex items-start p-4 rounded-lg ${
                  alert.type === 'warning'
                    ? 'bg-yellow-50 dark:bg-yellow-900/20'
                    : 'bg-blue-50 dark:bg-blue-900/20'
                }`}
              >
                {alert.type === 'warning' ? (
                  <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                ) : (
                  <CheckCircle2 className="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                )}
                <div className="flex-1">
                  <p className={`text-sm font-medium ${
                    alert.type === 'warning'
                      ? 'text-yellow-800 dark:text-yellow-300'
                      : 'text-blue-800 dark:text-blue-300'
                  }`}>
                    {alert.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(alert.date).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Prévisions et objectifs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Prévisions et objectifs
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              Date de fin estimée
            </div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {new Date(data.progress.estimatedCompletion).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              Gouttières restantes
            </div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {data.progress.total - data.progress.current}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              Temps de port moyen
            </div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {formatTime(data.wearTime.weekly)} / jour
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
