import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🌍 TEST COMPLET DU SYSTÈME MULTILINGUE ORTHOPROGRESS');
console.log('====================================================\n');

// Test des 7 langues supportées
const languages = [
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: '<PERSON>spaño<PERSON>', flag: '🇪🇸' },
  { code: 'de', name: 'Deuts<PERSON>', flag: '🇩🇪' },
  { code: 'pt', name: 'Português', flag: '🇵🇹' },
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
];

// Clés de traduction critiques pour analytics praticien
const criticalKeys = [
  'practitioner.analytics.title',
  'practitioner.analytics.subtitle',
  'practitioner.analytics.description',
  'practitioner.analytics.totalPatients',
  'practitioner.analytics.monthlyRevenue',
  'practitioner.analytics.avgCompliance',
  'practitioner.analytics.satisfaction',
  'practitioner.analytics.monthlyEvolution',
  'practitioner.analytics.topTreatments',
  'practitioner.analytics.aiInsights',
  'practitioner.analytics.aiInsightsDesc',
  'practitioner.analytics.successRate',
  'practitioner.analytics.avgDuration',
  'practitioner.analytics.referralRate'
];

console.log('✅ Test 1: Vérification de la structure des langues');
console.log('===================================================');

const langFile = path.join(__dirname, 'src/contexts/LanguageContext.tsx');
const langContent = fs.readFileSync(langFile, 'utf8');

let totalTranslations = 0;
let missingTranslations = 0;

languages.forEach(lang => {
  console.log(`\n${lang.flag} ${lang.name.toUpperCase()} (${lang.code}):`);
  console.log('─'.repeat(40));
  
  let langMissing = 0;
  criticalKeys.forEach(key => {
    // Recherche plus précise pour éviter les faux positifs
    const pattern = new RegExp(`${lang.code}:\\s*{[\\s\\S]*?'${key.replace('.', '\\.')}':\\s*'[^']+?'`, 'g');
    if (pattern.test(langContent)) {
      console.log(`   ✅ ${key}`);
      totalTranslations++;
    } else {
      console.log(`   ❌ ${key} - MANQUANT`);
      langMissing++;
      missingTranslations++;
    }
  });
  
  const completion = ((criticalKeys.length - langMissing) / criticalKeys.length * 100).toFixed(1);
  console.log(`\n   📊 Complétude: ${completion}% (${criticalKeys.length - langMissing}/${criticalKeys.length})`);
  
  if (langMissing === 0) {
    console.log(`   🎉 ${lang.name} - COMPLET !`);
  } else {
    console.log(`   ⚠️  ${lang.name} - ${langMissing} traductions manquantes`);
  }
});

console.log('\n' + '='.repeat(60));
console.log('📈 STATISTIQUES GLOBALES');
console.log('='.repeat(60));

const totalExpected = languages.length * criticalKeys.length;
const globalCompletion = ((totalExpected - missingTranslations) / totalExpected * 100).toFixed(1);

console.log(`📊 Traductions totales: ${totalExpected - missingTranslations}/${totalExpected}`);
console.log(`🎯 Complétude globale: ${globalCompletion}%`);
console.log(`✅ Traductions présentes: ${totalExpected - missingTranslations}`);
console.log(`❌ Traductions manquantes: ${missingTranslations}`);

if (missingTranslations === 0) {
  console.log('\n🎉 FÉLICITATIONS ! SYSTÈME MULTILINGUE 100% COMPLET !');
  console.log('🌟 Toutes les traductions analytics praticien sont présentes');
  console.log('🚀 Support complet pour les 7 langues');
} else {
  console.log(`\n⚠️  ${missingTranslations} traductions à compléter`);
}

console.log('\n✅ Test 2: Vérification des fonctionnalités RTL');
console.log('===============================================');

// Test du support RTL pour l'arabe
if (langContent.includes("direction: 'rtl'") && langContent.includes("document.documentElement.dir = isRTL ? 'rtl' : 'ltr'")) {
  console.log('✅ Support RTL pour l\'arabe - Présent');
  console.log('✅ Direction automatique du texte - Configurée');
  console.log('✅ Classes CSS RTL - Implémentées');
} else {
  console.log('❌ Support RTL - Incomplet');
}

console.log('\n✅ Test 3: Vérification de l\'interpolation de paramètres');
console.log('========================================================');

if (langContent.includes('params?: Record<string, string | number>') && 
    langContent.includes('translation.replace(new RegExp(`\\\\{${param}\\\\}`, \'g\'), String(value))')) {
  console.log('✅ Interpolation de paramètres - Fonctionnelle');
  console.log('✅ Support {param} dans les traductions - Activé');
} else {
  console.log('❌ Interpolation de paramètres - Manquante');
}

console.log('\n🎯 RECOMMANDATIONS FINALES');
console.log('==========================');

if (globalCompletion >= 100) {
  console.log('🌟 Système multilingue PARFAIT !');
  console.log('🚀 Prêt pour déploiement international');
  console.log('📱 Interface praticien traduite dans 7 langues');
  console.log('🌍 Support RTL complet pour l\'arabe');
} else if (globalCompletion >= 90) {
  console.log('✅ Système multilingue EXCELLENT');
  console.log('🔧 Quelques ajustements mineurs recommandés');
} else if (globalCompletion >= 70) {
  console.log('⚠️  Système multilingue BON');
  console.log('🔧 Compléter les traductions manquantes');
} else {
  console.log('❌ Système multilingue INCOMPLET');
  console.log('🚨 Action urgente requise');
}

console.log('\n📋 RÉSUMÉ TECHNIQUE');
console.log('==================');
console.log('• 7 langues supportées: FR, EN, ES, DE, PT, AR, ZH');
console.log('• 14 clés de traduction par langue pour analytics');
console.log('• Support RTL automatique pour l\'arabe');
console.log('• Interpolation de paramètres {param}');
console.log('• Persistance localStorage des préférences');
console.log('• Direction automatique du document HTML');
