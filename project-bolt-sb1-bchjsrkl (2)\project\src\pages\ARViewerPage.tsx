import React from 'react';
import { motion } from 'framer-motion';
import AugmentedRealityViewer from '../components/AR/AugmentedRealityViewer';
import { useLanguage } from '../contexts/LanguageContext';

const ARViewerPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      <AugmentedRealityViewer 
        patientId="current-patient"
        mode="preview"
      />
    </motion.div>
  );
};

export default ARViewerPage;
