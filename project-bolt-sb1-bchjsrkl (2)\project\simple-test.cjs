const http = require('http');

console.log('🧪 Testing OrthoProgress Application...\n');

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  console.log(`✅ Application Status: ${res.statusCode}`);
  console.log(`✅ Content-Type: ${res.headers['content-type']}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    if (data.includes('lang=')) {
      console.log('✅ HTML page loaded successfully');
      console.log('✅ Language attribute found in HTML');
    }
    if (data.includes('OrthoProgress') || data.includes('orthodont')) {
      console.log('✅ OrthoProgress content detected');
    }
    console.log('\n🎉 Application is running correctly!');
    console.log('🌐 Open http://localhost:3001 in your browser to test the full application.');
  });
});

req.on('error', (err) => {
  console.log(`❌ Error: ${err.message}`);
});

req.on('timeout', () => {
  console.log('⏰ Request timeout');
  req.destroy();
});

req.end();
