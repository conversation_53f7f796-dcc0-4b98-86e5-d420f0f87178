// Script de test de performance pour OrthoProgress
console.log('⚡ Test de performance OrthoProgress...');

// Fonction pour mesurer les métriques de performance
function measurePerformanceMetrics() {
  console.log('\n📊 Métriques de performance...');
  
  // Temps de chargement
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    console.log(`- Temps de chargement DOM: ${Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart)}ms`);
    console.log(`- Temps de chargement complet: ${Math.round(navigation.loadEventEnd - navigation.navigationStart)}ms`);
    console.log(`- Temps de réponse serveur: ${Math.round(navigation.responseEnd - navigation.requestStart)}ms`);
    console.log(`- Temps de rendu: ${Math.round(navigation.domComplete - navigation.domLoading)}ms`);
  }
  
  // First Paint et First Contentful Paint
  const paintEntries = performance.getEntriesByType('paint');
  paintEntries.forEach(entry => {
    console.log(`- ${entry.name}: ${Math.round(entry.startTime)}ms`);
  });
}

// Fonction pour analyser les ressources
function analyzeResources() {
  console.log('\n📦 Analyse des ressources...');
  
  const resources = performance.getEntriesByType('resource');
  const resourceTypes = {};
  let totalSize = 0;
  
  resources.forEach(resource => {
    const type = getResourceType(resource.name);
    if (!resourceTypes[type]) {
      resourceTypes[type] = { count: 0, size: 0, duration: 0 };
    }
    
    resourceTypes[type].count++;
    resourceTypes[type].size += resource.transferSize || 0;
    resourceTypes[type].duration += resource.duration || 0;
    totalSize += resource.transferSize || 0;
  });
  
  console.log(`- Nombre total de ressources: ${resources.length}`);
  console.log(`- Taille totale transférée: ${formatBytes(totalSize)}`);
  
  Object.keys(resourceTypes).forEach(type => {
    const data = resourceTypes[type];
    console.log(`- ${type}: ${data.count} fichiers, ${formatBytes(data.size)}, ${Math.round(data.duration)}ms`);
  });
}

function getResourceType(url) {
  if (url.includes('.js')) return 'JavaScript';
  if (url.includes('.css')) return 'CSS';
  if (url.includes('.png') || url.includes('.jpg') || url.includes('.jpeg') || url.includes('.svg')) return 'Images';
  if (url.includes('.woff') || url.includes('.woff2') || url.includes('.ttf')) return 'Fonts';
  return 'Autres';
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Fonction pour tester la mémoire
function testMemoryUsage() {
  console.log('\n🧠 Utilisation mémoire...');
  
  if ('memory' in performance) {
    const memory = performance.memory;
    console.log(`- Mémoire utilisée: ${formatBytes(memory.usedJSHeapSize)}`);
    console.log(`- Mémoire totale: ${formatBytes(memory.totalJSHeapSize)}`);
    console.log(`- Limite mémoire: ${formatBytes(memory.jsHeapSizeLimit)}`);
  } else {
    console.log('- Informations mémoire non disponibles');
  }
}

// Fonction pour tester les erreurs
function checkForErrors() {
  console.log('\n🚨 Vérification des erreurs...');
  
  let errorCount = 0;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  // Intercepter les erreurs
  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };
  
  console.warn = function(...args) {
    console.log('⚠️ Warning détecté:', args.join(' '));
    originalWarn.apply(console, args);
  };
  
  // Vérifier les erreurs réseau
  const failedResources = performance.getEntriesByType('resource').filter(resource => 
    resource.responseStatus >= 400 || resource.duration === 0
  );
  
  console.log(`- Erreurs console détectées: ${errorCount}`);
  console.log(`- Ressources échouées: ${failedResources.length}`);
  
  if (failedResources.length > 0) {
    failedResources.forEach(resource => {
      console.log(`  ❌ ${resource.name} (${resource.responseStatus})`);
    });
  }
  
  // Restaurer les fonctions originales
  setTimeout(() => {
    console.error = originalError;
    console.warn = originalWarn;
  }, 1000);
}

// Fonction pour tester la réactivité
function testResponsiveness() {
  console.log('\n⚡ Test de réactivité...');
  
  let clickCount = 0;
  let totalResponseTime = 0;
  
  // Tester les clics sur les boutons
  const buttons = document.querySelectorAll('button');
  console.log(`- Boutons trouvés: ${buttons.length}`);
  
  if (buttons.length > 0) {
    const testButton = buttons[0];
    const startTime = performance.now();
    
    testButton.addEventListener('click', () => {
      const responseTime = performance.now() - startTime;
      clickCount++;
      totalResponseTime += responseTime;
      console.log(`- Temps de réponse clic: ${Math.round(responseTime)}ms`);
    });
    
    // Simuler un clic
    testButton.click();
  }
}

// Fonction pour analyser le DOM
function analyzeDOMComplexity() {
  console.log('\n🌳 Complexité du DOM...');
  
  const allElements = document.querySelectorAll('*');
  const depth = getMaxDOMDepth();
  const textNodes = getTextNodesCount();
  
  console.log(`- Nombre total d'éléments: ${allElements.length}`);
  console.log(`- Profondeur maximale: ${depth}`);
  console.log(`- Nœuds de texte: ${textNodes}`);
  
  // Analyser les éléments les plus fréquents
  const elementTypes = {};
  allElements.forEach(el => {
    const tagName = el.tagName.toLowerCase();
    elementTypes[tagName] = (elementTypes[tagName] || 0) + 1;
  });
  
  const sortedTypes = Object.entries(elementTypes)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);
  
  console.log('- Top 5 éléments:');
  sortedTypes.forEach(([tag, count]) => {
    console.log(`  ${tag}: ${count}`);
  });
}

function getMaxDOMDepth(element = document.body, depth = 0) {
  let maxDepth = depth;
  for (let child of element.children) {
    maxDepth = Math.max(maxDepth, getMaxDOMDepth(child, depth + 1));
  }
  return maxDepth;
}

function getTextNodesCount(element = document.body) {
  let count = 0;
  for (let node of element.childNodes) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
      count++;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      count += getTextNodesCount(node);
    }
  }
  return count;
}

// Fonction pour tester les animations
function testAnimations() {
  console.log('\n🎬 Test des animations...');
  
  const animatedElements = document.querySelectorAll('[class*="animate-"], [class*="transition-"]');
  console.log(`- Éléments animés: ${animatedElements.length}`);
  
  // Vérifier les transitions CSS
  const transitionElements = document.querySelectorAll('[class*="transition"]');
  console.log(`- Éléments avec transitions: ${transitionElements.length}`);
  
  // Vérifier les transformations
  const transformElements = document.querySelectorAll('[class*="transform"], [class*="rotate"], [class*="scale"]');
  console.log(`- Éléments avec transformations: ${transformElements.length}`);
}

// Fonction pour générer un score de performance
function generatePerformanceScore() {
  console.log('\n🏆 Score de performance...');
  
  const navigation = performance.getEntriesByType('navigation')[0];
  let score = 100;
  
  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.navigationStart;
    const domTime = navigation.domContentLoadedEventEnd - navigation.navigationStart;
    
    // Pénalités basées sur les temps de chargement
    if (loadTime > 3000) score -= 30;
    else if (loadTime > 2000) score -= 20;
    else if (loadTime > 1000) score -= 10;
    
    if (domTime > 1500) score -= 20;
    else if (domTime > 1000) score -= 10;
    
    // Bonus pour les bonnes performances
    if (loadTime < 500) score += 10;
    if (domTime < 300) score += 10;
  }
  
  const resources = performance.getEntriesByType('resource');
  const totalSize = resources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
  
  // Pénalités pour la taille
  if (totalSize > 2000000) score -= 20; // > 2MB
  else if (totalSize > 1000000) score -= 10; // > 1MB
  
  score = Math.max(0, Math.min(100, score));
  
  console.log(`- Score de performance: ${score}/100`);
  
  if (score >= 90) console.log('🟢 Excellent');
  else if (score >= 70) console.log('🟡 Bon');
  else if (score >= 50) console.log('🟠 Moyen');
  else console.log('🔴 À améliorer');
  
  return score;
}

// Fonction principale
function runPerformanceTests() {
  console.log('🚀 Démarrage des tests de performance...\n');
  
  measurePerformanceMetrics();
  analyzeResources();
  testMemoryUsage();
  checkForErrors();
  testResponsiveness();
  analyzeDOMComplexity();
  testAnimations();
  
  setTimeout(() => {
    const score = generatePerformanceScore();
    
    console.log('\n✅ Tests de performance terminés !');
    console.log('\n📋 Recommandations:');
    if (score < 70) {
      console.log('- Optimiser les images');
      console.log('- Réduire la taille du bundle JavaScript');
      console.log('- Implémenter le lazy loading');
      console.log('- Utiliser la compression gzip');
    } else {
      console.log('- Performance globalement bonne');
      console.log('- Continuer à surveiller les métriques');
    }
  }, 2000);
}

// Démarrer les tests
runPerformanceTests();
