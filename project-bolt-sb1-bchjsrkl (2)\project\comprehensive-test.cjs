const http = require('http');

const testPages = [
  { path: '/', name: '<PERSON> d\'accueil' },
  { path: '/login', name: 'Page de connexion' },
  { path: '/register', name: '<PERSON> d\'inscription' },
  { path: '/dashboard', name: 'Tableau de bord patient' },
  { path: '/analytics', name: 'Page Analytics' },
  { path: '/teleconsultation', name: 'Page Téléconsultation' },
  { path: '/practitioner', name: 'Tableau de bord praticien' },
  { path: '/practitioner/patients', name: 'Gestion des patients' },
  { path: '/practitioner/analytics', name: 'Analytics praticien' }
];

async function testPage(path, name) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET',
      headers: {
        'User-Agent': 'OrthoProgress-Test/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const success = res.statusCode === 200 && data.includes('OrthoProgress');
        console.log(`${success ? '✅' : '❌'} ${name}: ${res.statusCode} ${success ? '(Contenu détecté)' : '(Erreur)'}`);
        resolve(success);
      });
    });

    req.on('error', (err) => {
      console.log(`❌ ${name}: Erreur - ${err.message}`);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.log(`❌ ${name}: Timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function runComprehensiveTest() {
  console.log('🧪 Test complet de l\'application OrthoProgress...\n');
  
  let successCount = 0;
  const totalTests = testPages.length;
  
  for (const page of testPages) {
    const success = await testPage(page.path, page.name);
    if (success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 100)); // Petite pause entre les tests
  }
  
  console.log(`\n📊 Résultats: ${successCount}/${totalTests} pages fonctionnelles`);
  
  if (successCount === totalTests) {
    console.log('🎉 TOUTES LES PAGES FONCTIONNENT PARFAITEMENT !');
    console.log('🌐 Application prête pour utilisation sur http://localhost:3001');
  } else {
    console.log('⚠️  Certaines pages nécessitent une attention.');
  }
  
  // Test des fonctionnalités spécifiques
  console.log('\n🔍 Test des fonctionnalités avancées...');
  
  // Test de la page Analytics avec contenu spécifique
  const analyticsTest = await testSpecificContent('/analytics', 'Analytics', ['analytics', 'chart', 'progress']);
  console.log(`${analyticsTest ? '✅' : '❌'} Interface Analytics avancée`);
  
  // Test de la page Communication
  const commTest = await testSpecificContent('/teleconsultation', 'Communication', ['communication', 'chat', 'video']);
  console.log(`${commTest ? '✅' : '❌'} Interface Communication avancée`);
  
  console.log('\n🎯 Test terminé !');
}

async function testSpecificContent(path, name, keywords) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const hasKeywords = keywords.some(keyword => 
          data.toLowerCase().includes(keyword.toLowerCase())
        );
        resolve(res.statusCode === 200 && hasKeywords);
      });
    });

    req.on('error', () => resolve(false));
    req.setTimeout(5000, () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

runComprehensiveTest().catch(console.error);
