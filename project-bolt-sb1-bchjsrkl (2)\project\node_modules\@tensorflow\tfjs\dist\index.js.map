{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,uBAAuB,CAAC;AAC/B,iDAAiD;AACjD,OAAO,mDAAmD,CAAC;AAC3D,iDAAiD;AACjD,OAAO,wEAAwE,CAAC;AAEhF,cAAc,uBAAuB,CAAC;AACtC,cAAc,yBAAyB,CAAC;AACxC,cAAc,4BAA4B,CAAC;AAE3C,6BAA6B;AAC7B,OAAO,KAAK,IAAI,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAC,IAAI,EAAC,CAAC;AAEd,gCAAgC;AAChC,cAAc,8BAA8B,CAAC;AAC7C,cAAc,gCAAgC,CAAC;AAE/C,uCAAuC;AACvC,OAAO,EAAC,YAAY,EAAC,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAC,WAAW,EAAC,MAAM,8BAA8B,CAAC;AACzD,OAAO,EAAC,aAAa,EAAC,MAAM,gCAAgC,CAAC;AAC7D,OAAO,EAAC,YAAY,EAAC,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAC,cAAc,EAAC,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAC,iBAAiB,EAAC,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAC,OAAO,IAAI,aAAa,EAAC,MAAM,WAAW,CAAC;AAEnD,MAAM,CAAC,MAAM,OAAO,GAAG;IACrB,WAAW,EAAE,YAAY;IACzB,kBAAkB,EAAE,WAAW;IAC/B,oBAAoB,EAAE,aAAa;IACnC,WAAW,EAAE,YAAY;IACzB,aAAa,EAAE,cAAc;IAC7B,gBAAgB,EAAE,iBAAiB;IACnC,MAAM,EAAE,aAAa;CACtB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport '@tensorflow/tfjs-core';\n// tslint:disable-next-line: no-imports-from-dist\nimport '@tensorflow/tfjs-core/dist/register_all_gradients';\n// tslint:disable-next-line: no-imports-from-dist\nimport '@tensorflow/tfjs-core/dist/public/chained_ops/register_all_chained_ops';\n\nexport * from '@tensorflow/tfjs-core';\nexport * from '@tensorflow/tfjs-layers';\nexport * from '@tensorflow/tfjs-converter';\n\n// Export data api as tf.data\nimport * as data from '@tensorflow/tfjs-data';\nexport {data};\n\n// Import and register backends.\nexport * from '@tensorflow/tfjs-backend-cpu';\nexport * from '@tensorflow/tfjs-backend-webgl';\n\n// Import versions of all sub-packages.\nimport {version_core} from '@tensorflow/tfjs-core';\nimport {version_cpu} from '@tensorflow/tfjs-backend-cpu';\nimport {version_webgl} from '@tensorflow/tfjs-backend-webgl';\nimport {version_data} from '@tensorflow/tfjs-data';\nimport {version_layers} from '@tensorflow/tfjs-layers';\nimport {version_converter} from '@tensorflow/tfjs-converter';\nimport {version as version_union} from './version';\n\nexport const version = {\n  'tfjs-core': version_core,\n  'tfjs-backend-cpu': version_cpu,\n  'tfjs-backend-webgl': version_webgl,\n  'tfjs-data': version_data,\n  'tfjs-layers': version_layers,\n  'tfjs-converter': version_converter,\n  'tfjs': version_union\n};\n"]}