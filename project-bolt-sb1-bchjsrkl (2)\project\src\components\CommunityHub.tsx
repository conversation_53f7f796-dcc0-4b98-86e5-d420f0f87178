import React, { useState, useEffect } from 'react';
import { MessageCircle, Heart, Share2, Users, HelpCircle, BookOpen, Video, Award, Filter, Search, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

interface Post {
  id: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    level: number;
    badges: string[];
    treatmentPhase: string;
  };
  content: string;
  images?: string[];
  type: 'question' | 'progress' | 'tip' | 'celebration' | 'support';
  category: 'general' | 'compliance' | 'pain' | 'hygiene' | 'lifestyle' | 'results';
  timestamp: Date;
  likes: number;
  comments: number;
  isLiked: boolean;
  isAnonymous: boolean;
  tags: string[];
}

interface Comment {
  id: string;
  postId: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    level: number;
    isExpert: boolean;
  };
  content: string;
  timestamp: Date;
  likes: number;
  isLiked: boolean;
}

interface ExpertTip {
  id: string;
  title: string;
  content: string;
  author: {
    name: string;
    title: string;
    avatar: string;
  };
  category: string;
  readTime: number;
  likes: number;
  isBookmarked: boolean;
}

export const CommunityHub: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'feed' | 'questions' | 'tips' | 'groups'>('feed');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewPost, setShowNewPost] = useState(false);

  const [posts, setPosts] = useState<Post[]>([
    {
      id: '1',
      author: {
        id: '1',
        name: 'Sarah M.',
        avatar: '👩‍⚕️',
        level: 15,
        badges: ['mentor', 'consistent'],
        treatmentPhase: 'Mois 8/18',
      },
      content: 'Salut tout le monde! Je viens de terminer mon 8ème mois de traitement et je vois déjà une énorme différence! Pour ceux qui commencent, accrochez-vous, ça vaut vraiment le coup! 💪',
      images: ['/progress-before.jpg', '/progress-after.jpg'],
      type: 'progress',
      category: 'results',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      likes: 24,
      comments: 8,
      isLiked: false,
      isAnonymous: false,
      tags: ['motivation', 'progrès', 'avant-après'],
    },
    {
      id: '2',
      author: {
        id: '2',
        name: 'Anonyme',
        avatar: '🤐',
        level: 3,
        badges: [],
        treatmentPhase: 'Mois 2/12',
      },
      content: 'Est-ce que quelqu\'un d\'autre a des difficultés avec la douleur les premiers jours de nouveaux aligneurs? Des conseils pour gérer ça?',
      type: 'question',
      category: 'pain',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      likes: 12,
      comments: 15,
      isLiked: true,
      isAnonymous: true,
      tags: ['douleur', 'nouveaux-aligneurs', 'conseils'],
    },
    {
      id: '3',
      author: {
        id: '3',
        name: 'Dr. Martin',
        avatar: '👨‍⚕️',
        level: 25,
        badges: ['expert', 'orthodontist'],
        treatmentPhase: 'Orthodontiste',
      },
      content: 'Conseil du jour: Utilisez des bâtonnets de mastication (chewies) pendant 10-15 minutes après avoir mis vos nouveaux aligneurs. Cela aide à un meilleur ajustement et peut réduire l\'inconfort initial.',
      type: 'tip',
      category: 'compliance',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      likes: 45,
      comments: 6,
      isLiked: true,
      isAnonymous: false,
      tags: ['conseil-expert', 'chewies', 'ajustement'],
    },
  ]);

  const [expertTips, setExpertTips] = useState<ExpertTip[]>([
    {
      id: '1',
      title: 'Les 5 erreurs les plus courantes avec les aligneurs',
      content: 'Découvrez les erreurs que font 90% des patients et comment les éviter pour optimiser votre traitement...',
      author: {
        name: 'Dr. Sophie Laurent',
        title: 'Orthodontiste spécialisée',
        avatar: '👩‍⚕️',
      },
      category: 'Compliance',
      readTime: 5,
      likes: 156,
      isBookmarked: false,
    },
    {
      id: '2',
      title: 'Hygiène dentaire pendant le traitement orthodontique',
      content: 'Guide complet pour maintenir une hygiène parfaite pendant votre traitement...',
      author: {
        name: 'Dr. Pierre Dubois',
        title: 'Hygiéniste dentaire',
        avatar: '👨‍⚕️',
      },
      category: 'Hygiène',
      readTime: 8,
      likes: 203,
      isBookmarked: true,
    },
  ]);

  const categories = [
    { id: 'all', label: 'Tout', icon: '📋' },
    { id: 'general', label: 'Général', icon: '💬' },
    { id: 'compliance', label: 'Compliance', icon: '⏰' },
    { id: 'pain', label: 'Douleur', icon: '😣' },
    { id: 'hygiene', label: 'Hygiène', icon: '🦷' },
    { id: 'lifestyle', label: 'Style de vie', icon: '🏃‍♀️' },
    { id: 'results', label: 'Résultats', icon: '✨' },
  ];

  const handleLikePost = (postId: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { 
            ...post, 
            isLiked: !post.isLiked,
            likes: post.isLiked ? post.likes - 1 : post.likes + 1
          }
        : post
    ));
  };

  const handleShare = (post: Post) => {
    if (navigator.share) {
      navigator.share({
        title: `Post de ${post.author.name}`,
        text: post.content,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Lien copié dans le presse-papiers');
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours < 1) return 'À l\'instant';
    if (hours < 24) return `Il y a ${hours}h`;
    const days = Math.floor(hours / 24);
    return `Il y a ${days}j`;
  };

  const getPostTypeIcon = (type: Post['type']) => {
    switch (type) {
      case 'question': return '❓';
      case 'progress': return '📈';
      case 'tip': return '💡';
      case 'celebration': return '🎉';
      case 'support': return '🤗';
      default: return '💬';
    }
  };

  const getPostTypeColor = (type: Post['type']) => {
    switch (type) {
      case 'question': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'progress': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'tip': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'celebration': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'support': return 'bg-pink-100 text-pink-800';
      default: return 'surface-tertiary text-adaptive';
    }
  };

  const filteredPosts = posts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="surface-primary rounded-lg p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 text-blue-600 rounded-full">
              <Users size={20} />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-adaptive">Communauté OrthoProgress</h1>
              <p className="text-adaptive-secondary">Partagez votre parcours et soutenez-vous mutuellement</p>
            </div>
          </div>
          
          <button
            onClick={() => setShowNewPost(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <Plus size={16} />
            Nouveau post
          </button>
        </div>

        {/* Navigation */}
        <div className="flex space-x-1 surface-tertiary rounded-lg p-1">
          {[
            { id: 'feed', label: 'Fil d\'actualité', icon: MessageCircle },
            { id: 'questions', label: 'Questions', icon: HelpCircle },
            { id: 'tips', label: 'Conseils experts', icon: BookOpen },
            { id: 'groups', label: 'Groupes', icon: Users },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'surface-primary text-blue-600 shadow-sm'
                  : 'text-adaptive-secondary hover:text-adaptive'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="surface-primary rounded-lg p-4 shadow-lg">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Recherche */}
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans la communauté..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filtres par catégorie */}
          <div className="flex gap-2 overflow-x-auto">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg whitespace-nowrap text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
                }`}
              >
                <span>{category.icon}</span>
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      {activeTab === 'feed' && (
        <div className="space-y-4">
          {filteredPosts.map(post => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="surface-primary rounded-lg p-6 shadow-lg"
            >
              {/* Header du post */}
              <div className="flex items-start gap-3 mb-4">
                <div className="text-2xl">{post.author.avatar}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-adaptive">{post.author.name}</span>
                    <span className="text-sm text-adaptive-tertiary">Niveau {post.author.level}</span>
                    {post.author.badges.includes('expert') && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 text-xs rounded-full">Expert</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-adaptive-tertiary">
                    <span>{post.author.treatmentPhase}</span>
                    <span>•</span>
                    <span>{formatTimeAgo(post.timestamp)}</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPostTypeColor(post.type)}`}>
                    {getPostTypeIcon(post.type)} {post.type}
                  </span>
                </div>
              </div>

              {/* Contenu */}
              <div className="mb-4">
                <p className="text-adaptive mb-3">{post.content}</p>
                
                {post.images && post.images.length > 0 && (
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {post.images.map((image, index) => (
                      <div key={index} className="aspect-video surface-tertiary rounded-lg flex items-center justify-center">
                        <span className="text-adaptive-tertiary">Image {index + 1}</span>
                      </div>
                    ))}
                  </div>
                )}

                {post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 surface-tertiary text-adaptive-secondary text-xs rounded-full">
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-adaptive-light">
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => handleLikePost(post.id)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                      post.isLiked 
                        ? 'bg-red-100 text-red-600' 
                        : 'text-adaptive-secondary hover:surface-tertiary'
                    }`}
                  >
                    <Heart size={16} fill={post.isLiked ? 'currentColor' : 'none'} />
                    {post.likes}
                  </button>
                  
                  <button className="flex items-center gap-2 px-3 py-2 text-adaptive-secondary hover:surface-tertiary rounded-lg transition-colors">
                    <MessageCircle size={16} />
                    {post.comments}
                  </button>
                  
                  <button
                    onClick={() => handleShare(post)}
                    className="flex items-center gap-2 px-3 py-2 text-adaptive-secondary hover:surface-tertiary rounded-lg transition-colors"
                  >
                    <Share2 size={16} />
                    Partager
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Conseils experts */}
      {activeTab === 'tips' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {expertTips.map(tip => (
            <motion.div
              key={tip.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="surface-primary rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer"
            >
              <div className="flex items-start gap-3 mb-4">
                <div className="text-2xl">{tip.author.avatar}</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-adaptive mb-1">{tip.title}</h3>
                  <div className="text-sm text-adaptive-secondary">
                    Par {tip.author.name} • {tip.author.title}
                  </div>
                </div>
              </div>
              
              <p className="text-adaptive-secondary mb-4">{tip.content}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-adaptive-tertiary">
                  <span>{tip.readTime} min de lecture</span>
                  <span>•</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 rounded-full text-xs">
                    {tip.category}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <button className="flex items-center gap-1 text-adaptive-secondary hover:text-red-600 transition-colors">
                    <Heart size={16} />
                    {tip.likes}
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Questions */}
      {activeTab === 'questions' && (
        <div className="space-y-4">
          {filteredPosts.filter(post => post.type === 'question').map(post => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="surface-primary rounded-lg p-6 shadow-lg"
            >
              <div className="flex items-start gap-3">
                <div className="text-2xl">{post.author.avatar}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-semibold text-adaptive">{post.author.name}</span>
                    <span className="text-sm text-adaptive-tertiary">{formatTimeAgo(post.timestamp)}</span>
                  </div>
                  <p className="text-adaptive mb-3">{post.content}</p>
                  
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => handleLikePost(post.id)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                        post.isLiked 
                          ? 'bg-red-100 text-red-600' 
                          : 'text-adaptive-secondary hover:surface-tertiary'
                      }`}
                    >
                      <Heart size={16} fill={post.isLiked ? 'currentColor' : 'none'} />
                      {post.likes}
                    </button>
                    
                    <button className="flex items-center gap-2 px-3 py-2 text-adaptive-secondary hover:surface-tertiary rounded-lg transition-colors">
                      <MessageCircle size={16} />
                      {post.comments} réponses
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Groupes */}
      {activeTab === 'groups' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { name: 'Débutants Invisalign', members: 1247, description: 'Pour ceux qui commencent leur parcours' },
            { name: 'Adolescents en traitement', members: 856, description: 'Groupe spécial pour les ados' },
            { name: 'Compliance Champions', members: 432, description: 'Les experts de la compliance' },
            { name: 'Avant/Après Transformations', members: 2134, description: 'Partagez vos résultats' },
          ].map((group, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="surface-primary rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer"
            >
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users size={24} className="text-blue-600" />
                </div>
                <h3 className="font-semibold text-adaptive mb-1">{group.name}</h3>
                <p className="text-sm text-adaptive-secondary mb-2">{group.description}</p>
                <div className="text-sm text-adaptive-tertiary">{group.members.toLocaleString()} membres</div>
              </div>
              
              <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Rejoindre
              </button>
            </motion.div>
          ))}
        </div>
      )}

      {/* Modal nouveau post */}
      <AnimatePresence>
        {showNewPost && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowNewPost(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="surface-primary rounded-lg p-6 w-full max-w-lg"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-xl font-bold text-adaptive mb-4">Nouveau post</h2>
              
              <div className="space-y-4">
                <textarea
                  placeholder="Partagez votre expérience, posez une question..."
                  className="w-full h-32 p-3 border border-adaptive rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                
                <div className="flex gap-2">
                  <select className="px-3 py-2 border border-adaptive rounded-lg">
                    <option>Type de post</option>
                    <option value="question">Question</option>
                    <option value="progress">Progrès</option>
                    <option value="tip">Conseil</option>
                    <option value="celebration">Célébration</option>
                  </select>
                  
                  <select className="px-3 py-2 border border-adaptive rounded-lg">
                    <option>Catégorie</option>
                    <option value="general">Général</option>
                    <option value="compliance">Compliance</option>
                    <option value="pain">Douleur</option>
                    <option value="hygiene">Hygiène</option>
                  </select>
                </div>
                
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="anonymous" />
                  <label htmlFor="anonymous" className="text-sm text-adaptive-secondary">
                    Publier anonymement
                  </label>
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowNewPost(false)}
                    className="flex-1 px-4 py-2 border border-adaptive text-adaptive-secondary rounded-lg hover:surface-secondary transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={() => {
                      setShowNewPost(false);
                      toast.success('Post publié avec succès!');
                    }}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Publier
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
