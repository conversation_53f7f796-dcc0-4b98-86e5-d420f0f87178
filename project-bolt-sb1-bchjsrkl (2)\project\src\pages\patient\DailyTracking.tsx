import React, { useState } from 'react';
import { Calendar, TrendingUp, Target, Award } from 'lucide-react';
import DailyTrackingCalendar from '../../components/patient/DailyTrackingCalendar';

interface DailyTrackingData {
  date: Date;
  applianceWear: boolean;
  brushingMorning: boolean;
  brushingEvening: boolean;
  notes?: string;
}

const DailyTracking: React.FC = () => {
  // Données d'exemple pour le suivi quotidien
  const [trackingData, setTrackingData] = useState<DailyTrackingData[]>([
    {
      date: new Date(2024, 5, 10), // 10 juin 2024
      applianceWear: true,
      brushingMorning: true,
      brushingEvening: false
    },
    {
      date: new Date(2024, 5, 9), // 9 juin 2024
      applianceWear: true,
      brushingMorning: true,
      brushingEvening: true
    },
    {
      date: new Date(2024, 5, 8), // 8 juin 2024
      applianceWear: false,
      brushingMorning: true,
      brushingEvening: true
    },
    {
      date: new Date(2024, 5, 7), // 7 juin 2024
      applianceWear: true,
      brushingMorning: true,
      brushingEvening: true
    },
    {
      date: new Date(2024, 5, 6), // 6 juin 2024
      applianceWear: true,
      brushingMorning: false,
      brushingEvening: true
    },
    {
      date: new Date(2024, 5, 5), // 5 juin 2024
      applianceWear: true,
      brushingMorning: true,
      brushingEvening: true
    }
  ]);

  const handleUpdateTracking = (newTracking: DailyTrackingData) => {
    setTrackingData(prev => {
      const existingIndex = prev.findIndex(tracking => 
        tracking.date.toDateString() === newTracking.date.toDateString()
      );
      
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = newTracking;
        return updated;
      } else {
        return [...prev, newTracking];
      }
    });
  };

  const getCompletionRate = () => {
    const completedDays = trackingData.filter(tracking => 
      tracking.applianceWear && tracking.brushingMorning && tracking.brushingEvening
    ).length;
    return trackingData.length > 0 ? Math.round((completedDays / trackingData.length) * 100) : 0;
  };

  const getCurrentStreak = () => {
    let streak = 0;
    const sortedData = [...trackingData].sort((a, b) => b.date.getTime() - a.date.getTime());
    
    for (const tracking of sortedData) {
      if (tracking.applianceWear && tracking.brushingMorning && tracking.brushingEvening) {
        streak++;
      } else {
        break;
      }
    }
    return streak;
  };

  const getApplianceWearRate = () => {
    const wearDays = trackingData.filter(tracking => tracking.applianceWear).length;
    return trackingData.length > 0 ? Math.round((wearDays / trackingData.length) * 100) : 0;
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Suivi quotidien</h1>
        <p className="text-gray-600 dark:text-gray-400">Suivez votre port d'appareil et votre hygiène dentaire</p>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Taux de réussite</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{getCompletionRate()}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">Ce mois-ci</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Série actuelle</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{getCurrentStreak()}</p>
            </div>
            <Award className="w-8 h-8 text-blue-500" />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">Jours consécutifs</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Port d'appareil</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{getApplianceWearRate()}%</p>
            </div>
            <Calendar className="w-8 h-8 text-purple-500" />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">Taux de port</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Objectif mensuel</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">90%</p>
            </div>
            <Target className="w-8 h-8 text-orange-500" />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">Cible à atteindre</p>
        </div>
      </div>

      {/* Calendrier de suivi quotidien */}
      <DailyTrackingCalendar 
        trackingData={trackingData}
        onUpdateTracking={handleUpdateTracking}
      />

      {/* Conseils et motivation */}
      <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          💡 Conseils pour un suivi optimal
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
          <div className="flex items-start gap-2">
            <span className="text-green-500">✓</span>
            <span>Portez votre appareil au moins 22h par jour</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-500">✓</span>
            <span>Brossez-vous les dents matin et soir</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-500">ℹ</span>
            <span>Retirez l'appareil uniquement pour manger</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-500">ℹ</span>
            <span>Nettoyez votre appareil quotidiennement</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyTracking;
