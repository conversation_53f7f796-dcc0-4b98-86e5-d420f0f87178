import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Trophy, Star, Gift, Target, Award, Medal } from 'lucide-react';

const Rewards: React.FC = () => {
  const { t } = useLanguage();

  const rewards = [
    {
      id: 1,
      title: 'Premier pas',
      description: 'Première connexion à l\'application',
      icon: Star,
      points: 50,
      unlocked: true,
      date: '2024-01-15'
    },
    {
      id: 2,
      title: 'Régularité',
      description: '7 jours consécutifs de port d\'appareil',
      icon: Target,
      points: 100,
      unlocked: true,
      date: '2024-01-22'
    },
    {
      id: 3,
      title: 'Photographe',
      description: '10 photos de progression envoyées',
      icon: Award,
      points: 150,
      unlocked: true,
      date: '2024-02-01'
    },
    {
      id: 4,
      title: 'Champion',
      description: '30 jours consécutifs de port d\'appareil',
      icon: Medal,
      points: 300,
      unlocked: false,
      progress: 75
    },
    {
      id: 5,
      title: 'Expert',
      description: '100 photos de progression envoyées',
      icon: Trophy,
      points: 500,
      unlocked: false,
      progress: 45
    }
  ];

  const totalPoints = rewards.filter(r => r.unlocked).reduce((sum, r) => sum + r.points, 0);

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Mes Récompenses</h1>
        <p className="text-gray-600">Suivez vos accomplissements et débloquez de nouveaux badges</p>
      </div>

      {/* Points totaux */}
      <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Points totaux</h2>
            <p className="text-yellow-100">Continuez vos efforts pour gagner plus de points !</p>
          </div>
          <div className="text-right">
            <div className="text-4xl font-bold">{totalPoints}</div>
            <div className="text-yellow-100">points</div>
          </div>
        </div>
      </div>

      {/* Badges débloqués */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Badges débloqués</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rewards.filter(reward => reward.unlocked).map((reward) => (
            <div key={reward.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-start space-x-4">
                <div className="bg-green-100 p-3 rounded-lg">
                  <reward.icon className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{reward.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{reward.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-green-600">+{reward.points} points</span>
                    <span className="text-xs text-gray-500">{reward.date}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Badges en cours */}
      <div>
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Badges en cours</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rewards.filter(reward => !reward.unlocked).map((reward) => (
            <div key={reward.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-start space-x-4">
                <div className="bg-gray-100 p-3 rounded-lg">
                  <reward.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{reward.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{reward.description}</p>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600">Progression</span>
                      <span className="text-gray-600">{reward.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${reward.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-blue-600">+{reward.points} points</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Rewards;
