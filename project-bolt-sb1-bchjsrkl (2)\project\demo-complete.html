<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OrthoProgress - Démonstration Complète</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="demo-complete.js" defer></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .demo-section {
            display: none;
        }
        .demo-section.active {
            display: block;
        }
        .nav-btn.active {
            color: #2563eb;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold text-blue-600">OrthoProgress</h1>
                        </div>
                        <div class="ml-10 flex items-baseline space-x-4">
                            <button onclick="showDemo('auth')" class="nav-btn active text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Connexion</button>
                            <button onclick="showDemo('patient')" class="nav-btn text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Interface Patient</button>
                            <button onclick="showDemo('practitioner')" class="nav-btn text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Interface Praticien</button>
                            <button onclick="showDemo('features')" class="nav-btn text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Fonctionnalités</button>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500">Démonstration Interactive</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu Principal -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Page de Connexion -->
            <div id="auth-demo" class="demo-section active">
                <div class="min-h-[calc(100vh-4rem)] gradient-bg flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                    <div class="max-w-md w-full space-y-8">
                        <div class="bg-white rounded-2xl shadow-xl p-8">
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-800">OrthoProgress</h2>
                                <p class="text-gray-600 mt-2">Plateforme de suivi orthodontique</p>
                            </div>

                            <div class="mb-6">
                                <h3 class="text-lg font-medium text-gray-800 mb-4">Type d'utilisateur</h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <button onclick="showPatientLogin()" class="user-type-btn p-4 border-2 border-blue-200 rounded-lg hover:border-blue-500 transition-colors">
                                        <i data-lucide="user" class="w-6 h-6 mx-auto mb-2 text-blue-600"></i>
                                        <span class="block text-sm font-medium">Patient</span>
                                    </button>
                                    <button onclick="showPractitionerLogin()" class="user-type-btn p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors">
                                        <i data-lucide="stethoscope" class="w-6 h-6 mx-auto mb-2 text-gray-600"></i>
                                        <span class="block text-sm font-medium">Praticien</span>
                                    </button>
                                </div>
                            </div>

                            <div id="login-form">
                                <div id="patient-login" class="hidden">
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Code patient</label>
                                        <input type="text" placeholder="Entrez votre code patient" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <p class="text-xs text-gray-500 mt-1">Code fourni par votre praticien</p>
                                    </div>
                                    <button onclick="loginAsPatient()" class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors">
                                        Se connecter
                                    </button>
                                </div>

                                <div id="practitioner-login" class="hidden">
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                            <input type="email" placeholder="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Mot de passe</label>
                                            <input type="password" placeholder="••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        </div>
                                    </div>
                                    <button onclick="loginAsPractitioner()" class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors mt-6">
                                        Se connecter
                                    </button>
                                </div>
                            </div>

                            <div class="mt-6 text-center">
                                <p class="text-sm text-gray-600">
                                    Pas encore de compte ? 
                                    <a href="#" class="text-blue-500 hover:text-blue-700">S'inscrire</a>
                                </p>
                            </div>

                            <div class="mt-8">
                                <h4 class="font-medium text-gray-800 mb-3">Fonctionnalités :</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• Interface Patient & Praticien</li>
                                    <li>• Suivi quotidien des appareils</li>
                                    <li>• Gestion des rendez-vous</li>
                                    <li>• Communauté professionnelle</li>
                                    <li>• Mode sombre/clair</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interface Patient -->
            <div id="patient-demo" class="demo-section">
                <div class="p-6">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-800">Tableau de bord patient</h1>
                        <p class="text-gray-600">Bienvenue Marie, voici votre espace personnel</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-blue-50 rounded-lg text-blue-600">
                                    <i data-lucide="file-text" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Dossier Médical</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Consultez votre dossier complet</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-green-50 rounded-lg text-green-600">
                                    <i data-lucide="calendar" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Suivi Quotidien</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Port d'appareil et brossage</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-purple-50 rounded-lg text-purple-600">
                                    <i data-lucide="clock" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Rendez-vous</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Gérez vos consultations</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-orange-50 rounded-lg text-orange-600">
                                    <i data-lucide="camera" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Photos</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Envoyez des photos de suivi</p>
                        </div>
                    </div>

                    <!-- Suivi Quotidien -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6">Suivi d'aujourd'hui</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <i data-lucide="smile" class="w-6 h-6 text-blue-600 mr-3"></i>
                                        <h3 class="text-lg font-medium text-gray-800">Port d'appareil</h3>
                                    </div>
                                    <div class="w-6 h-6 bg-blue-500 rounded border-2 border-blue-500 flex items-center justify-center">
                                        <i data-lucide="check" class="w-4 h-4 text-white"></i>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Heures de port: 18h</label>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="p-4 bg-green-50 rounded-lg">
                                <div class="flex items-center mb-4">
                                    <i data-lucide="brush" class="w-6 h-6 text-green-600 mr-3"></i>
                                    <h3 class="text-lg font-medium text-gray-800">Brossage des dents</h3>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-700">Brossage du matin</span>
                                        <div class="w-6 h-6 bg-green-500 rounded border-2 border-green-500 flex items-center justify-center">
                                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-700">Brossage du soir</span>
                                        <div class="w-6 h-6 bg-green-500 rounded border-2 border-green-500 flex items-center justify-center">
                                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Prochain rendez-vous -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold mb-2">Prochain rendez-vous</h2>
                                <p class="opacity-90">Consultation de suivi - Dr. Martin</p>
                                <p class="text-sm opacity-75">15 Juin 2024 à 14:30</p>
                            </div>
                            <button class="px-4 py-2 bg-white text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                                Voir détails
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interface Praticien -->
            <div id="practitioner-demo" class="demo-section">
                <div class="p-6">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-800">Tableau de bord praticien</h1>
                        <p class="text-gray-600">Gérez votre pratique orthodontique</p>
                    </div>

                    <!-- Statistiques -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">Patients actifs</p>
                                    <p class="text-2xl font-bold text-gray-800">24</p>
                                </div>
                                <i data-lucide="users" class="w-8 h-8 text-blue-500"></i>
                            </div>
                        </div>
                        <div class="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">RDV aujourd'hui</p>
                                    <p class="text-2xl font-bold text-gray-800">8</p>
                                </div>
                                <i data-lucide="calendar" class="w-8 h-8 text-green-500"></i>
                            </div>
                        </div>
                        <div class="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">Messages</p>
                                    <p class="text-2xl font-bold text-gray-800">12</p>
                                </div>
                                <i data-lucide="message-square" class="w-8 h-8 text-purple-500"></i>
                            </div>
                        </div>
                        <div class="p-4 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600">Revenus ce mois</p>
                                    <p class="text-2xl font-bold text-gray-800">€4,250</p>
                                </div>
                                <i data-lucide="credit-card" class="w-8 h-8 text-orange-500"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Menu principal -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100 relative">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-blue-50 rounded-lg text-blue-600">
                                    <i data-lucide="users" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Mes Patients</h3>
                                <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">24</span>
                            </div>
                            <p class="text-gray-600 text-sm">Gérez vos patients et leurs dossiers</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-green-50 rounded-lg text-green-600">
                                    <i data-lucide="calendar" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Rendez-vous</h3>
                                <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">8</span>
                            </div>
                            <p class="text-gray-600 text-sm">Planifiez et gérez vos consultations</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-purple-50 rounded-lg text-purple-600">
                                    <i data-lucide="smile" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Invisalign</h3>
                                <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                            </div>
                            <p class="text-gray-600 text-sm">Créez et gérez les cas Invisalign</p>
                        </div>

                        <div class="card-hover p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-orange-50 rounded-lg text-orange-600">
                                    <i data-lucide="credit-card" class="w-6 h-6"></i>
                                </div>
                                <h3 class="ml-3 text-lg font-semibold text-gray-800">Abonnement</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Gérez votre abonnement (24€/mois)</p>
                        </div>
                    </div>

                    <!-- Patients récents -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">Patients récents</h2>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-800">Marie Dubois</p>
                                    <p class="text-sm text-gray-600">Dernière visite: 10/06/2024</p>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">En cours</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-800">Pierre Martin</p>
                                    <p class="text-sm text-gray-600">Dernière visite: 08/06/2024</p>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Suivi</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-800">Sophie Laurent</p>
                                    <p class="text-sm text-gray-600">Dernière visite: 05/06/2024</p>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Terminé</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fonctionnalités -->
            <div id="features-demo" class="demo-section">
                <div class="p-6">
                    <div class="text-center mb-12">
                        <h1 class="text-4xl font-bold text-gray-800 mb-4">Fonctionnalités OrthoProgress</h1>
                        <p class="text-xl text-gray-600">Une plateforme complète pour le suivi orthodontique</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
                        <!-- Fonctionnalités Patient -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                <i data-lucide="user" class="w-8 h-8 text-blue-600 mr-3"></i>
                                Pour les Patients
                            </h2>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i data-lucide="calendar" class="w-6 h-6 text-green-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Suivi Quotidien</h3>
                                        <p class="text-gray-600">Enregistrez vos heures de port d'appareil et votre brossage avec un calendrier interactif</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="file-text" class="w-6 h-6 text-blue-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Dossier Médical</h3>
                                        <p class="text-gray-600">Accédez à votre historique de traitement et vos documents médicaux</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="camera" class="w-6 h-6 text-purple-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Photos de Suivi</h3>
                                        <p class="text-gray-600">Envoyez facilement des photos de vos progrès à votre praticien</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="users" class="w-6 h-6 text-orange-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Communauté</h3>
                                        <p class="text-gray-600">Échangez avec d'autres patients et partagez vos expériences</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Fonctionnalités Praticien -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                <i data-lucide="stethoscope" class="w-8 h-8 text-green-600 mr-3"></i>
                                Pour les Praticiens
                            </h2>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i data-lucide="users" class="w-6 h-6 text-blue-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Gestion des Patients</h3>
                                        <p class="text-gray-600">Base de données complète avec codes patients et suivi des traitements</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="smile" class="w-6 h-6 text-purple-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Gestion Invisalign</h3>
                                        <p class="text-gray-600">Créez et suivez vos cas Invisalign avec workflow intégré</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="calendar" class="w-6 h-6 text-green-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Calendrier Avancé</h3>
                                        <p class="text-gray-600">Planification intelligente des rendez-vous avec notifications</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i data-lucide="bar-chart" class="w-6 h-6 text-orange-500 mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-gray-800">Analytiques</h3>
                                        <p class="text-gray-600">Statistiques détaillées de votre pratique et revenus</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tarification -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-gray-800 mb-4">Tarification Simple</h2>
                            <p class="text-gray-600">Un abonnement unique pour tous les praticiens</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                            <div class="border-2 border-gray-200 rounded-xl p-6">
                                <div class="text-center mb-6">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Mensuel</h3>
                                    <div class="text-3xl font-bold text-gray-800">€24<span class="text-lg font-normal text-gray-500">/mois</span></div>
                                </div>
                                <ul class="space-y-3 text-sm text-gray-600">
                                    <li
