import React from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Navbar from './Navbar';

const Layout: React.FC = () => {
  return (
    <div className="min-h-screen surface-secondary dark:bg-gray-900 flex">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation */}
        <Navbar />

        {/* Main Content Area */}
        <main className="flex-1 p-6 overflow-y-auto">
          <div className="container mx-auto">
            <Outlet />
          </div>
        </main>

        {/* Footer */}
        <footer className="p-4 border-t border-adaptive dark:border-gray-800">
          <div className="container mx-auto text-center text-sm text-adaptive-secondary dark:text-gray-400">
            © {new Date().getFullYear()} OrthoProgress. Tous droits réservés.
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Layout;
