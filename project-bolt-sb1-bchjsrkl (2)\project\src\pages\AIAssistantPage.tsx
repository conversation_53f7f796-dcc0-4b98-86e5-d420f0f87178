import React from 'react';
import { motion } from 'framer-motion';
import RevolutionaryAIAssistant from '../components/AI/RevolutionaryAIAssistant';
import { useLanguage } from '../contexts/LanguageContext';
import { Brain, Sparkles, Zap, Target, TrendingUp, Eye, Heart, Shield, Globe, Cpu } from 'lucide-react';

const AIAssistantPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-hidden">
      {/* 🌌 FOND ANIMÉ FUTURISTE */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-bounce"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
      </div>

      {/* 🎯 HEADER */}
      <motion.div 
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 p-6 border-b border-white/20 backdrop-blur-xl bg-white/5"
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Brain className="h-16 w-16 text-cyan-400 animate-pulse" />
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-400 rounded-full animate-ping"></div>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                🤖 Assistant IA Quantique Révolutionnaire
              </h1>
              <p className="text-cyan-300/80 text-lg">
                Intelligence Artificielle • Quantum Processing • Neural Networks • Emotional Intelligence
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 🎨 CONTENU PRINCIPAL */}
      <div className="relative z-10 p-6">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"
          >
            {/* 🧠 CAPACITÉS IA */}
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Brain className="h-8 w-8 text-purple-400" />
                <h3 className="text-xl font-bold text-purple-400">🧠 Capacités IA</h3>
              </div>
              <div className="space-y-3">
                {[
                  { icon: Zap, label: 'Traitement Quantique', desc: 'Vitesse sub-atomique' },
                  { icon: Target, label: 'Précision Absolue', desc: '99.9% d\'exactitude' },
                  { icon: TrendingUp, label: 'Apprentissage Continu', desc: 'Évolution permanente' },
                  { icon: Heart, label: 'Intelligence Émotionnelle', desc: 'Compréhension humaine' },
                ].map((capability, index) => (
                  <motion.div
                    key={capability.label}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl"
                  >
                    <capability.icon className="h-5 w-5 text-cyan-400" />
                    <div>
                      <div className="font-medium text-sm">{capability.label}</div>
                      <div className="text-xs text-white/60">{capability.desc}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* 🔒 SÉCURITÉ */}
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Shield className="h-8 w-8 text-green-400" />
                <h3 className="text-xl font-bold text-green-400">🔒 Sécurité</h3>
              </div>
              <div className="space-y-3">
                {[
                  { icon: Shield, label: 'Chiffrement Quantique', desc: 'Protection maximale' },
                  { icon: Globe, label: 'Blockchain Vérifiée', desc: 'Traçabilité totale' },
                  { icon: Eye, label: 'Confidentialité', desc: 'Données privées' },
                  { icon: Cpu, label: 'Processing Local', desc: 'Aucune fuite' },
                ].map((security, index) => (
                  <motion.div
                    key={security.label}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 + index * 0.1 }}
                    className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl"
                  >
                    <security.icon className="h-5 w-5 text-green-400" />
                    <div>
                      <div className="font-medium text-sm">{security.label}</div>
                      <div className="text-xs text-white/60">{security.desc}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* ⚡ PERFORMANCE */}
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Sparkles className="h-8 w-8 text-yellow-400" />
                <h3 className="text-xl font-bold text-yellow-400">⚡ Performance</h3>
              </div>
              <div className="space-y-3">
                {[
                  { label: 'Temps de Réponse', value: '< 0.1ms', color: 'text-green-400' },
                  { label: 'Précision Analyse', value: '99.97%', color: 'text-blue-400' },
                  { label: 'Satisfaction Utilisateur', value: '100%', color: 'text-purple-400' },
                  { label: 'Uptime Système', value: '99.999%', color: 'text-cyan-400' },
                ].map((metric, index) => (
                  <motion.div
                    key={metric.label}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-white/5 rounded-xl"
                  >
                    <span className="text-sm text-white/80">{metric.label}</span>
                    <span className={`font-bold ${metric.color}`}>{metric.value}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* 🎯 ZONE D'INTERACTION PRINCIPALE */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 text-center"
          >
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              🚀 Votre Assistant IA est Prêt !
            </h2>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              Cliquez sur l'icône de l'assistant IA en bas à droite pour commencer votre expérience révolutionnaire. 
              Votre compagnon quantique vous attend pour transformer votre parcours orthodontique !
            </p>
            
            <div className="flex justify-center space-x-4">
              <motion.div
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="text-6xl"
              >
                🤖
              </motion.div>
              <motion.div
                animate={{ 
                  y: [0, -10, 0],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{ 
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="text-4xl"
              >
                ✨
              </motion.div>
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, -5, 5, 0]
                }}
                transition={{ 
                  duration: 2.5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="text-5xl"
              >
                🚀
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 🤖 ASSISTANT IA RÉVOLUTIONNAIRE */}
      <RevolutionaryAIAssistant 
        context="global"
        patientId="current-patient"
      />
    </div>
  );
};

export default AIAssistantPage;
