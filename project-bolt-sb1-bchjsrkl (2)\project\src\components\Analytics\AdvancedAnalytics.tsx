import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  SparklesIcon,
  EyeIcon,
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  LightBulbIcon,
  StarIcon,
  FireIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface AnalyticsData {
  complianceScore: number;
  trend: 'up' | 'down' | 'stable';
  weeklyProgress: number[];
  monthlyProgress: number[];
  currentStreak: number;
  longestStreak: number;
  averageWearTime: number;
  totalHours: number;
  predictions: {
    nextWeekCompliance: number;
    treatmentCompletion: string;
    riskLevel: 'low' | 'medium' | 'high';
  };
  insights: string[];
  recommendations: string[];
}

const AdvancedAnalytics: React.FC = () => {
  const { t } = useLanguage();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month'>('week');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    complianceScore: 87,
    trend: 'up',
    weeklyProgress: [85, 88, 82, 90, 87, 89, 91],
    monthlyProgress: [82, 85, 88, 87],
    currentStreak: 12,
    longestStreak: 18,
    averageWearTime: 21.5,
    totalHours: 1290,
    predictions: {
      nextWeekCompliance: 89,
      treatmentCompletion: '2024-08-15',
      riskLevel: 'low'
    },
    insights: [
      'Votre conformité s\'améliore constamment',
      'Excellent port nocturne détecté',
      'Tendance positive sur les 7 derniers jours'
    ],
    recommendations: [
      'Continuez vos excellents efforts',
      'Maintenez le port nocturne régulier',
      'Prochaine visite recommandée dans 2 semaines'
    ]
  });

  const getQualityLevel = (score: number) => {
    if (score >= 90) return { level: t('analytics.excellent'), color: 'text-green-600 dark:text-green-400', bgColor: 'bg-green-100 dark:bg-green-900/30' };
    if (score >= 80) return { level: t('analytics.good'), color: 'text-blue-600 dark:text-blue-400', bgColor: 'bg-blue-100 dark:bg-blue-900/30' };
    if (score >= 70) return { level: t('analytics.fair'), color: 'text-yellow-600 dark:text-yellow-400', bgColor: 'bg-yellow-100 dark:bg-yellow-900/30' };
    return { level: t('analytics.poor'), color: 'text-red-600 dark:text-red-400', bgColor: 'bg-red-100 dark:bg-red-900/30' };
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowTrendingUpIcon className="w-5 h-5 text-green-500" />;
      case 'down':
        return <ArrowTrendingDownIcon className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 bg-gray-400 rounded-full" />;
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-600 dark:text-green-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'high':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-adaptive-secondary dark:text-gray-400';
    }
  };

  const quality = getQualityLevel(analyticsData.complianceScore);

  return (
    <div className="space-y-6">
      {/* En-tête avec score principal */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="surface-elevated rounded-2xl p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
              <ChartBarIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-adaptive">{t('analytics.title')}</h2>
              <p className="text-adaptive-secondary">{t('analytics.insights')}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setSelectedPeriod('week')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                selectedPeriod === 'week'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'surface-secondary text-adaptive-secondary hover-surface'
              }`}
            >
              {t('analytics.weekly')}
            </button>
            <button
              onClick={() => setSelectedPeriod('month')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                selectedPeriod === 'month'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'surface-secondary text-adaptive-secondary hover-surface'
              }`}
            >
              {t('analytics.monthly')}
            </button>
          </div>
        </div>

        {/* Score principal */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-2xl font-bold text-white">{analyticsData.complianceScore}%</span>
                </div>
                <div className="absolute -top-2 -right-2">
                  {getTrendIcon(analyticsData.trend)}
                </div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-adaptive">{t('analytics.score')}</h3>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${quality.bgColor} ${quality.color}`}>
                  <StarIcon className="w-4 h-4 mr-1" />
                  {quality.level}
                </div>
                <p className="text-adaptive-secondary mt-1">{t('analytics.trend')}: {t(`analytics.${analyticsData.trend}`)}</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="surface-secondary rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <FireIcon className="w-5 h-5 text-orange-500" />
                <span className="font-medium text-adaptive">{t('analytics.current')} {t('analytics.streak')}</span>
              </div>
              <div className="text-2xl font-bold text-adaptive">{analyticsData.currentStreak} {t('analytics.days')}</div>
              <div className="text-sm text-adaptive-secondary">
                {t('analytics.longest')}: {analyticsData.longestStreak} {t('analytics.days')}
              </div>
            </div>

            <div className="surface-secondary rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <ClockIcon className="w-5 h-5 text-blue-500" />
                <span className="font-medium text-adaptive">{t('analytics.average')}</span>
              </div>
              <div className="text-2xl font-bold text-adaptive">{analyticsData.averageWearTime}h</div>
              <div className="text-sm text-adaptive-secondary">
                {t('analytics.total')}: {analyticsData.totalHours}h
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Graphique de progression */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="surface-elevated rounded-2xl p-6"
      >
        <h3 className="text-lg font-semibold text-adaptive mb-4">{t('analytics.progress')}</h3>
        <div className="h-64 flex items-end space-x-2">
          {(selectedPeriod === 'week' ? analyticsData.weeklyProgress : analyticsData.monthlyProgress).map((value, index) => (
            <motion.div
              key={index}
              initial={{ height: 0 }}
              animate={{ height: `${(value / 100) * 100}%` }}
              transition={{ delay: index * 0.1 }}
              className="flex-1 bg-gradient-to-t from-blue-500 to-purple-600 rounded-t-lg min-h-[20px] relative group"
            >
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:surface-tertiary text-white dark:text-adaptive px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                {value}%
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Prédictions IA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="surface-elevated rounded-2xl p-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
            <SparklesIcon className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-adaptive">{t('analytics.predictions')}</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="surface-secondary rounded-xl p-4">
            <div className="text-sm text-adaptive-secondary mb-1">Conformité prochaine semaine</div>
            <div className="text-xl font-bold text-adaptive">{analyticsData.predictions.nextWeekCompliance}%</div>
          </div>
          <div className="surface-secondary rounded-xl p-4">
            <div className="text-sm text-adaptive-secondary mb-1">Fin de traitement estimée</div>
            <div className="text-xl font-bold text-adaptive">{analyticsData.predictions.treatmentCompletion}</div>
          </div>
          <div className="surface-secondary rounded-xl p-4">
            <div className="text-sm text-adaptive-secondary mb-1">Niveau de risque</div>
            <div className={`text-xl font-bold ${getRiskColor(analyticsData.predictions.riskLevel)}`}>
              {analyticsData.predictions.riskLevel.toUpperCase()}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Insights et Recommandations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="surface-elevated rounded-2xl p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg">
              <LightBulbIcon className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-adaptive">{t('analytics.insights')}</h3>
          </div>
          <div className="space-y-3">
            {analyticsData.insights.map((insight, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="flex items-start space-x-3 p-3 surface-secondary rounded-lg"
              >
                <EyeIcon className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-adaptive-secondary">{insight}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="surface-elevated rounded-2xl p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
              <ShieldCheckIcon className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-adaptive">{t('analytics.recommendations')}</h3>
          </div>
          <div className="space-y-3">
            {analyticsData.recommendations.map((recommendation, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="flex items-start space-x-3 p-3 surface-secondary rounded-lg"
              >
                <ExclamationTriangleIcon className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <span className="text-adaptive-secondary">{recommendation}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;
