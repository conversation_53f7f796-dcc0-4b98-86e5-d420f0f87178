import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { CreditCard, Check, Clock, AlertTriangle } from 'lucide-react';

const SubscriptionManager: React.FC = () => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubscribe = async (plan: 'monthly' | 'yearly') => {
    setIsProcessing(true);
    
    // Simulate Stripe payment process
    setTimeout(() => {
      // Update user subscription status
      const users = JSON.parse(localStorage.getItem('orthoprogress_users') || '[]');
      const updatedUsers = users.map((u: any) => 
        u.id === user?.id 
          ? { ...u, subscriptionStatus: 'active', trialEndDate: null }
          : u
      );
      localStorage.setItem('orthoprogress_users', JSON.stringify(updatedUsers));
      
      // Update current user
      const updatedUser = { ...user!, subscriptionStatus: 'active' as const };
      localStorage.setItem('orthoprogress_user', JSON.stringify(updatedUser));
      
      setIsProcessing(false);
      window.location.reload();
    }, 2000);
  };

  const getStatusColor = () => {
    switch (user?.subscriptionStatus) {
      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'trial': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'expired': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      default: return 'text-adaptive-secondary surface-tertiary';
    }
  };

  const getStatusText = () => {
    switch (user?.subscriptionStatus) {
      case 'active': return 'Actif';
      case 'trial': return 'Période d\'essai';
      case 'expired': return 'Expiré';
      default: return 'Inactif';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h2 className="text-2xl font-bold text-adaptive mb-6">Mon abonnement</h2>
        
        <div className="flex items-center justify-between p-4 surface-secondary rounded-lg">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${getStatusColor()}`}>
              {user?.subscriptionStatus === 'active' ? (
                <Check className="h-5 w-5" />
              ) : user?.subscriptionStatus === 'trial' ? (
                <Clock className="h-5 w-5" />
              ) : (
                <AlertTriangle className="h-5 w-5" />
              )}
            </div>
            <div>
              <p className="font-medium text-adaptive">Statut: {getStatusText()}</p>
              {user?.trialEndDate && (
                <p className="text-sm text-adaptive-secondary">
                  Fin d'essai: {new Date(user.trialEndDate).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
          
          {user?.isVip && (
            <span className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300 px-3 py-1 rounded-full text-sm font-medium">
              Compte VIP
            </span>
          )}
        </div>
      </div>

      {/* Subscription Plans */}
      {user?.subscriptionStatus !== 'active' && !user?.isVip && (
        <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
          <h3 className="text-xl font-semibold text-adaptive mb-6">Choisir un abonnement</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Monthly Plan */}
            <div className="border border-adaptive rounded-xl p-6 hover:border-blue-500 transition-colors">
              <div className="text-center mb-6">
                <h4 className="text-lg font-semibold text-adaptive">Mensuel</h4>
                <div className="mt-2">
                  <span className="text-3xl font-bold text-adaptive">24$</span>
                  <span className="text-adaptive-secondary">/mois</span>
                </div>
              </div>
              
              <ul className="space-y-3 mb-6">
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Accès complet aux fonctionnalités</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Gestion illimitée de patients</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Forum praticiens</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Support prioritaire</span>
                </li>
              </ul>
              
              <button
                onClick={() => handleSubscribe('monthly')}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                <CreditCard className="h-4 w-4" />
                <span>{isProcessing ? 'Traitement...' : 'S\'abonner'}</span>
              </button>
            </div>

            {/* Yearly Plan */}
            <div className="border border-blue-500 rounded-xl p-6 relative bg-blue-50">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Recommandé
                </span>
              </div>
              
              <div className="text-center mb-6">
                <h4 className="text-lg font-semibold text-adaptive">Annuel</h4>
                <div className="mt-2">
                  <span className="text-3xl font-bold text-adaptive">119$</span>
                  <span className="text-adaptive-secondary">/an</span>
                </div>
                <p className="text-sm text-green-600 font-medium mt-1">
                  Économisez 169$ par an
                </p>
              </div>
              
              <ul className="space-y-3 mb-6">
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Accès complet aux fonctionnalités</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Gestion illimitée de patients</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Forum praticiens</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Support prioritaire</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-adaptive-secondary">Fonctionnalités avancées</span>
                </li>
              </ul>
              
              <button
                onClick={() => handleSubscribe('yearly')}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                <CreditCard className="h-4 w-4" />
                <span>{isProcessing ? 'Traitement...' : 'S\'abonner'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Active Subscription Info */}
      {user?.subscriptionStatus === 'active' && (
        <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
          <h3 className="text-xl font-semibold text-adaptive mb-4">Abonnement actif</h3>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Check className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Votre abonnement est actif</p>
                <p className="text-sm text-green-600">
                  Vous avez accès à toutes les fonctionnalités praticien
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManager;