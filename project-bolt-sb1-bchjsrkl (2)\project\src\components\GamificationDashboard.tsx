import React, { useState, useEffect } from 'react';
import { Trophy, Star, Target, Gift, Users, Zap, Crown, Medal, Award, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'compliance' | 'progress' | 'social' | 'milestone' | 'special';
  points: number;
  unlocked: boolean;
  unlockedAt?: Date;
  progress: number;
  maxProgress: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'monthly' | 'special';
  category: 'wear_time' | 'photos' | 'appointments' | 'education' | 'community';
  target: number;
  current: number;
  reward: {
    points: number;
    badge?: string;
    unlock?: string;
  };
  expiresAt: Date;
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
}

interface LeaderboardEntry {
  rank: number;
  userId: string;
  username: string;
  avatar: string;
  points: number;
  level: number;
  streak: number;
  badges: string[];
}

interface UserStats {
  totalPoints: number;
  level: number;
  currentXP: number;
  nextLevelXP: number;
  streak: number;
  longestStreak: number;
  achievementsUnlocked: number;
  totalAchievements: number;
  rank: number;
  totalUsers: number;
}

export const GamificationDashboard: React.FC = () => {
  const { user } = useAuth();
  const [userStats, setUserStats] = useState<UserStats>({
    totalPoints: 2847,
    level: 12,
    currentXP: 347,
    nextLevelXP: 500,
    streak: 15,
    longestStreak: 28,
    achievementsUnlocked: 23,
    totalAchievements: 45,
    rank: 156,
    totalUsers: 12847,
  });

  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: '1',
      title: 'Première Semaine',
      description: 'Porter vos aligneurs 7 jours consécutifs',
      icon: '🎯',
      category: 'compliance',
      points: 100,
      unlocked: true,
      unlockedAt: new Date('2024-01-15'),
      progress: 7,
      maxProgress: 7,
      rarity: 'common',
    },
    {
      id: '2',
      title: 'Photographe Expert',
      description: 'Prendre 50 photos de progrès',
      icon: '📸',
      category: 'progress',
      points: 250,
      unlocked: true,
      unlockedAt: new Date('2024-02-01'),
      progress: 50,
      maxProgress: 50,
      rarity: 'rare',
    },
    {
      id: '3',
      title: 'Mentor Communautaire',
      description: 'Aider 10 nouveaux patients',
      icon: '🤝',
      category: 'social',
      points: 500,
      unlocked: false,
      progress: 7,
      maxProgress: 10,
      rarity: 'epic',
    },
    {
      id: '4',
      title: 'Légende Orthodontique',
      description: 'Atteindre 100 jours de compliance parfaite',
      icon: '👑',
      category: 'milestone',
      points: 1000,
      unlocked: false,
      progress: 67,
      maxProgress: 100,
      rarity: 'legendary',
    },
  ]);

  const [challenges, setChallenges] = useState<Challenge[]>([
    {
      id: '1',
      title: 'Défi Quotidien',
      description: 'Porter vos aligneurs 22h aujourd\'hui',
      type: 'daily',
      category: 'wear_time',
      target: 22,
      current: 18,
      reward: { points: 50 },
      expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000),
      difficulty: 'easy',
    },
    {
      id: '2',
      title: 'Semaine Parfaite',
      description: 'Compliance parfaite pendant 7 jours',
      type: 'weekly',
      category: 'wear_time',
      target: 7,
      current: 4,
      reward: { points: 300, badge: 'perfect_week' },
      expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      difficulty: 'medium',
    },
    {
      id: '3',
      title: 'Éducation Continue',
      description: 'Lire 5 articles éducatifs ce mois',
      type: 'monthly',
      category: 'education',
      target: 5,
      current: 2,
      reward: { points: 200, unlock: 'expert_tips' },
      expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
      difficulty: 'medium',
    },
  ]);

  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([
    {
      rank: 1,
      userId: '1',
      username: 'Sarah M.',
      avatar: '👩‍⚕️',
      points: 5420,
      level: 18,
      streak: 45,
      badges: ['perfect_month', 'mentor', 'photographer'],
    },
    {
      rank: 2,
      userId: '2',
      username: 'Alex K.',
      avatar: '👨‍💼',
      points: 4890,
      level: 16,
      streak: 32,
      badges: ['consistent', 'helper'],
    },
    {
      rank: 3,
      userId: '3',
      username: 'Emma L.',
      avatar: '👩‍🎓',
      points: 4567,
      level: 15,
      streak: 28,
      badges: ['educator', 'progress_tracker'],
    },
  ]);

  const [selectedTab, setSelectedTab] = useState<'overview' | 'achievements' | 'challenges' | 'leaderboard'>('overview');
  const [newAchievement, setNewAchievement] = useState<Achievement | null>(null);

  // Simulation de déblocage d'achievement
  useEffect(() => {
    const timer = setTimeout(() => {
      const unlockedAchievement = achievements.find(a => !a.unlocked && a.progress >= a.maxProgress);
      if (unlockedAchievement) {
        setNewAchievement(unlockedAchievement);
        setAchievements(prev => prev.map(a => 
          a.id === unlockedAchievement.id 
            ? { ...a, unlocked: true, unlockedAt: new Date() }
            : a
        ));
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [achievements]);

  const getRarityColor = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'common': return 'text-adaptive-secondary surface-tertiary';
      case 'rare': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'epic': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      case 'legendary': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
    }
  };

  const getDifficultyColor = (difficulty: Challenge['difficulty']) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'hard': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'expert': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
    }
  };

  const formatTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}j ${hours % 24}h`;
    }
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header avec stats principales */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Tableau de Bord Gamification</h1>
            <p className="text-blue-100">Votre progression orthodontique</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{userStats.totalPoints.toLocaleString()}</div>
            <div className="text-blue-100">Points totaux</div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="surface-primary bg-opacity-20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold">Niveau {userStats.level}</div>
            <div className="text-sm text-blue-100">
              {userStats.currentXP}/{userStats.nextLevelXP} XP
            </div>
            <div className="w-full surface-primary bg-opacity-30 rounded-full h-2 mt-2">
              <div 
                className="surface-primary h-2 rounded-full transition-all duration-500"
                style={{ width: `${(userStats.currentXP / userStats.nextLevelXP) * 100}%` }}
              />
            </div>
          </div>

          <div className="surface-primary bg-opacity-20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold">{userStats.streak}</div>
            <div className="text-sm text-blue-100">Jours consécutifs</div>
            <div className="text-xs text-blue-200">Record: {userStats.longestStreak}</div>
          </div>

          <div className="surface-primary bg-opacity-20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold">#{userStats.rank}</div>
            <div className="text-sm text-blue-100">Classement</div>
            <div className="text-xs text-blue-200">sur {userStats.totalUsers.toLocaleString()}</div>
          </div>

          <div className="surface-primary bg-opacity-20 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold">{userStats.achievementsUnlocked}</div>
            <div className="text-sm text-blue-100">Succès débloqués</div>
            <div className="text-xs text-blue-200">sur {userStats.totalAchievements}</div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="surface-primary rounded-lg shadow-lg">
        <div className="border-b border-adaptive">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Vue d\'ensemble', icon: TrendingUp },
              { id: 'achievements', label: 'Succès', icon: Trophy },
              { id: 'challenges', label: 'Défis', icon: Target },
              { id: 'leaderboard', label: 'Classement', icon: Crown },
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-adaptive-tertiary hover:text-adaptive-secondary'
                }`}
              >
                <tab.icon size={16} />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Vue d'ensemble */}
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Défis actifs */}
              <div>
                <h3 className="font-semibold text-adaptive mb-4 flex items-center gap-2">
                  <Target size={20} className="text-blue-600" />
                  Défis Actifs
                </h3>
                <div className="space-y-3">
                  {challenges.slice(0, 3).map(challenge => (
                    <div key={challenge.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-adaptive">{challenge.title}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty)}`}>
                          {challenge.difficulty}
                        </span>
                      </div>
                      <p className="text-sm text-adaptive-secondary mb-3">{challenge.description}</p>
                      
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-adaptive-tertiary">
                          {challenge.current}/{challenge.target}
                        </span>
                        <span className="text-sm text-adaptive-tertiary">
                          {formatTimeRemaining(challenge.expiresAt)}
                        </span>
                      </div>
                      
                      <div className="w-full surface-tertiary rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${(challenge.current / challenge.target) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Succès récents */}
              <div>
                <h3 className="font-semibold text-adaptive mb-4 flex items-center gap-2">
                  <Trophy size={20} className="text-yellow-600" />
                  Succès Récents
                </h3>
                <div className="space-y-3">
                  {achievements.filter(a => a.unlocked).slice(0, 3).map(achievement => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`border rounded-lg p-4 ${getRarityColor(achievement.rarity)}`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{achievement.icon}</div>
                        <div className="flex-1">
                          <h4 className="font-medium">{achievement.title}</h4>
                          <p className="text-sm opacity-75">{achievement.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Star size={14} />
                            <span className="text-sm font-medium">{achievement.points} points</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Succès */}
          {selectedTab === 'achievements' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map(achievement => (
                <motion.div
                  key={achievement.id}
                  whileHover={{ scale: 1.02 }}
                  className={`border rounded-lg p-4 transition-all ${
                    achievement.unlocked 
                      ? getRarityColor(achievement.rarity)
                      : 'surface-secondary text-gray-400'
                  }`}
                >
                  <div className="text-center mb-3">
                    <div className={`text-4xl mb-2 ${achievement.unlocked ? '' : 'grayscale'}`}>
                      {achievement.icon}
                    </div>
                    <h3 className="font-semibold">{achievement.title}</h3>
                    <p className="text-sm opacity-75">{achievement.description}</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progrès</span>
                      <span>{achievement.progress}/{achievement.maxProgress}</span>
                    </div>
                    <div className="w-full surface-tertiary rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-500 ${
                          achievement.unlocked ? 'bg-green-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                      />
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(achievement.rarity)}`}>
                        {achievement.rarity}
                      </span>
                      <div className="flex items-center gap-1 text-sm">
                        <Star size={14} />
                        {achievement.points}
                      </div>
                    </div>

                    {achievement.unlocked && achievement.unlockedAt && (
                      <div className="text-xs text-center opacity-75">
                        Débloqué le {achievement.unlockedAt.toLocaleDateString('fr-FR')}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Défis */}
          {selectedTab === 'challenges' && (
            <div className="space-y-6">
              {['daily', 'weekly', 'monthly'].map(type => (
                <div key={type}>
                  <h3 className="font-semibold text-adaptive mb-4 capitalize">
                    Défis {type === 'daily' ? 'Quotidiens' : type === 'weekly' ? 'Hebdomadaires' : 'Mensuels'}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {challenges.filter(c => c.type === type).map(challenge => (
                      <div key={challenge.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <h4 className="font-medium text-adaptive">{challenge.title}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty)}`}>
                            {challenge.difficulty}
                          </span>
                        </div>
                        
                        <p className="text-sm text-adaptive-secondary mb-4">{challenge.description}</p>
                        
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span>Progrès</span>
                            <span>{challenge.current}/{challenge.target}</span>
                          </div>
                          
                          <div className="w-full surface-tertiary rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${Math.min((challenge.current / challenge.target) * 100, 100)}%` }}
                            />
                          </div>
                          
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-adaptive-tertiary">
                              Expire dans {formatTimeRemaining(challenge.expiresAt)}
                            </span>
                            <div className="flex items-center gap-1 text-blue-600">
                              <Gift size={14} />
                              {challenge.reward.points} pts
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Classement */}
          {selectedTab === 'leaderboard' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold text-adaptive">Classement Global</h3>
                <div className="text-sm text-adaptive-tertiary">
                  Votre position: #{userStats.rank}
                </div>
              </div>
              
              <div className="space-y-2">
                {leaderboard.map((entry, index) => (
                  <motion.div
                    key={entry.userId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex items-center gap-4 p-4 rounded-lg border ${
                      entry.userId === user?.id ? 'bg-blue-50 border-blue-200' : 'surface-primary'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                      entry.rank === 1 ? 'bg-yellow-100 text-yellow-600' :
                      entry.rank === 2 ? 'surface-tertiary text-adaptive-secondary' :
                      entry.rank === 3 ? 'bg-orange-100 text-orange-600' :
                      'bg-blue-100 text-blue-600'
                    }`}>
                      {entry.rank <= 3 ? (
                        entry.rank === 1 ? '🥇' : entry.rank === 2 ? '🥈' : '🥉'
                      ) : (
                        entry.rank
                      )}
                    </div>
                    
                    <div className="text-2xl">{entry.avatar}</div>
                    
                    <div className="flex-1">
                      <div className="font-medium text-adaptive">{entry.username}</div>
                      <div className="text-sm text-adaptive-tertiary">
                        Niveau {entry.level} • {entry.streak} jours de suite
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-bold text-adaptive">{entry.points.toLocaleString()}</div>
                      <div className="text-sm text-adaptive-tertiary">points</div>
                    </div>
                    
                    <div className="flex gap-1">
                      {entry.badges.slice(0, 3).map((badge, i) => (
                        <div key={i} className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center text-xs">
                          🏆
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Popup nouveau succès */}
      <AnimatePresence>
        {newAchievement && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setNewAchievement(null)}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              className="surface-primary rounded-lg p-8 text-center max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-2xl font-bold text-adaptive mb-2">Nouveau Succès!</h2>
              <div className="text-4xl mb-4">{newAchievement.icon}</div>
              <h3 className="text-xl font-semibold text-adaptive mb-2">{newAchievement.title}</h3>
              <p className="text-adaptive-secondary mb-4">{newAchievement.description}</p>
              <div className="flex items-center justify-center gap-2 text-yellow-600 mb-6">
                <Star size={20} />
                <span className="font-bold text-lg">+{newAchievement.points} points</span>
              </div>
              <button
                onClick={() => setNewAchievement(null)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Fantastique!
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
