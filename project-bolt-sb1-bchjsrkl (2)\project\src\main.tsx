// Test React avec imports statiques
console.log('🚀 main.tsx chargé - Test React statique');

import React from 'react';
import { createRoot } from 'react-dom/client';

console.log('📦 React importé:', React);
console.log('📦 createRoot importé:', createRoot);

const rootElement = document.getElementById('root');
console.log('📍 Root element:', rootElement);

if (rootElement) {
  try {
    const root = createRoot(rootElement);
    console.log('✅ Root créé:', root);

    // Composant ultra-minimal avec JSX
    const TestComponent = () => {
      return (
        <div style={{
          padding: '20px',
          fontFamily: 'Arial',
          background: '#e8f5e8',
          minHeight: '100vh'
        }}>
          <h1 style={{ color: '#2e7d32', textAlign: 'center' }}>
            🎉 REACT FONCTIONNE !
          </h1>
          <div style={{
            background: 'white',
            padding: '20px',
            borderRadius: '8px',
            margin: '20px auto',
            maxWidth: '600px',
            border: '2px solid #4caf50'
          }}>
            <h2>✅ React se monte correctement</h2>
            <p>Si vous voyez ce message, React fonctionne parfaitement.</p>
            <p>Timestamp: {new Date().toLocaleString()}</p>
            <div style={{
              marginTop: '20px',
              padding: '10px',
              background: '#f3e5f5',
              borderRadius: '4px'
            }}>
              <strong>🎯 Prochaine étape: Restaurer l'application complète</strong>
            </div>
          </div>
        </div>
      );
    };

    root.render(<TestComponent />);
    console.log('✅ Composant rendu avec succès');

  } catch (error) {
    console.error('❌ Erreur lors du rendu React:', error);
    rootElement.innerHTML = `
      <div style="padding: 20px; background: #ffebee; min-height: 100vh;">
        <h1 style="color: #c62828;">❌ ERREUR REACT</h1>
        <pre style="background: white; padding: 10px; border-radius: 4px;">${error.message}</pre>
      </div>
    `;
  }
} else {
  console.error('❌ Element #root introuvable !');
}
