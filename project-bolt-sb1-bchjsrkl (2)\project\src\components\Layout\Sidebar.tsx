import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  Home, 
  Calendar, 
  Camera, 
  FileText, 
  Video, 
  Trophy, 
  Bell, 
  BarChart3, 
  BookOpen, 
  MessageSquare,
  CreditCard,
  Users,
  Settings,
  Stethoscope,
  User
} from 'lucide-react';
import Logo from '../Logo';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  roles: ('patient' | 'practitioner' | 'admin')[];
}

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const { t } = useLanguage();

  const patientNavItems: NavItem[] = [
    { path: '/patient/dashboard', label: t('nav.dashboard'), icon: <Home className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/dossier', label: t('nav.medical-record'), icon: <FileText className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/suivi', label: t('nav.progress'), icon: <Calendar className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/photos', label: t('nav.photos'), icon: <Camera className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/rendez-vous', label: t('nav.teleconsultation'), icon: <Video className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/rappels', label: t('nav.reminders'), icon: <Bell className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/factures', label: t('nav.billing'), icon: <CreditCard className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/education', label: t('nav.education'), icon: <BookOpen className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/analytiques', label: t('nav.analytics'), icon: <BarChart3 className="w-5 h-5" />, roles: ['patient'] },
    { path: '/patient/recompenses', label: t('nav.rewards'), icon: <Trophy className="w-5 h-5" />, roles: ['patient'] },
  ];

  const practitionerNavItems: NavItem[] = [
    { path: '/practitioner/dashboard', label: t('nav.dashboard'), icon: <Home className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/patients', label: t('nav.patients'), icon: <Users className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/invisalign', label: t('nav.invisalign'), icon: <Stethoscope className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/appointments', label: t('nav.appointments'), icon: <Calendar className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/messages', label: t('nav.communication'), icon: <MessageSquare className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/subscription', label: t('nav.subscription'), icon: <CreditCard className="w-5 h-5" />, roles: ['practitioner'] },
    { path: '/practitioner/analytics', label: t('nav.analytics'), icon: <BarChart3 className="w-5 h-5" />, roles: ['practitioner'] },
  ];

  const commonNavItems: NavItem[] = [
    { 
      path: user?.role === 'patient' ? '/patient/communaute' : '/practitioner/community', 
      label: t('nav.forum'), 
      icon: <MessageSquare className="w-5 h-5" />, 
      roles: ['patient', 'practitioner'] 
    },
    { 
      path: '/profile', 
      label: t('nav.profile'), 
      icon: <User className="w-5 h-5" />, 
      roles: ['patient', 'practitioner'] 
    },
    { 
      path: '/settings', 
      label: t('nav.settings'), 
      icon: <Settings className="w-5 h-5" />, 
      roles: ['patient', 'practitioner'] 
    },
  ];

  const getNavItems = () => {
    if (!user) return [];
    
    let items: NavItem[] = [];
    
    if (user.role === 'patient') {
      items = [...patientNavItems];
    } else if (user.role === 'practitioner') {
      items = [...practitionerNavItems];
    }
    
    // Add common items
    items = [...items, ...commonNavItems];
    
    return items.filter(item => item.roles.includes(user.role));
  };

  const navItems = getNavItems();

  return (
    <aside className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <Logo size="md" />
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              OrthoProgress
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t(user?.role === 'practitioner' ? 'dashboard.practitioner-space' : 'dashboard.patient-space')}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <li key={item.path}>
                <NavLink
                  to={item.path}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    isActive
                      ? user?.role === 'patient'
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-r-2 border-blue-500'
                        : 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-r-2 border-purple-500'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {item.icon}
                  <span className="font-medium">{item.label}</span>
                </NavLink>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Info */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            user?.role === 'patient' 
              ? 'bg-blue-100 dark:bg-blue-900/30' 
              : 'bg-purple-100 dark:bg-purple-900/30'
          }`}>
            <User className={`w-5 h-5 ${
              user?.role === 'patient' 
                ? 'text-blue-600 dark:text-blue-400' 
                : 'text-purple-600 dark:text-purple-400'
            }`} />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {user?.firstName} {user?.lastName}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t(user?.role === 'practitioner' ? 'auth.practitioner' : 'auth.patient')}
            </p>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
