import React, { useState, useEffect } from 'react';
import { CreditCard, Download, FileText, Filter, Plus, Search, X, CheckCircle, AlertCircle, Calendar, TrendingUp, PieChart, Receipt, Wallet, Shield, Star } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string;
  amount: number;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
  description: string;
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
  paymentMethod?: string;
  paidAt?: string;
  paidAmount?: number;
  discount?: number;
  tax?: number;
  category: 'consultation' | 'treatment' | 'equipment' | 'emergency';
}

interface PaymentPlan {
  id: string;
  name: string;
  totalAmount: number;
  monthlyAmount: number;
  remainingPayments: number;
  nextPaymentDate: string;
  status: 'active' | 'completed' | 'paused';
}

const BillingPremium: React.FC = () => {
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [paymentPlans, setPaymentPlans] = useState<PaymentPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [showInvoiceModal, setShowInvoiceModal] = useState<Invoice | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState<Invoice | null>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'paid' | 'pending' | 'overdue' | 'partial'>('all');
  const [filterCategory, setFilterCategory] = useState<'all' | 'consultation' | 'treatment' | 'equipment' | 'emergency'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Données simulées enrichies
  useEffect(() => {
    const mockInvoices: Invoice[] = [
      {
        id: '1',
        number: 'INV-2024-001',
        date: '2024-01-15',
        dueDate: '2024-02-15',
        amount: 150.00,
        status: 'paid',
        description: 'Consultation orthodontique - Janvier 2024',
        category: 'consultation',
        items: [
          { description: 'Consultation de suivi', quantity: 1, unitPrice: 80.00, total: 80.00 },
          { description: 'Gouttières Invisalign (x2)', quantity: 2, unitPrice: 35.00, total: 70.00 }
        ],
        paymentMethod: 'Carte bancaire',
        paidAt: '2024-01-15',
        paidAmount: 150.00,
        tax: 15.00
      },
      {
        id: '2',
        number: 'INV-2024-002',
        date: '2024-02-15',
        dueDate: '2024-03-15',
        amount: 150.00,
        status: 'partial',
        description: 'Consultation orthodontique - Février 2024',
        category: 'consultation',
        items: [
          { description: 'Consultation de suivi', quantity: 1, unitPrice: 80.00, total: 80.00 },
          { description: 'Gouttières Invisalign (x2)', quantity: 2, unitPrice: 35.00, total: 70.00 }
        ],
        paidAmount: 75.00,
        tax: 15.00
      },
      {
        id: '3',
        number: 'INV-2024-003',
        date: '2024-03-01',
        dueDate: '2024-03-31',
        amount: 350.00,
        status: 'pending',
        description: 'Équipement orthodontique',
        category: 'equipment',
        items: [
          { description: 'Kit de démarrage Invisalign', quantity: 1, unitPrice: 350.00, total: 350.00 }
        ],
        tax: 35.00,
        discount: 50.00
      }
    ];

    const mockPaymentPlans: PaymentPlan[] = [
      {
        id: '1',
        name: 'Plan Invisalign Complet',
        totalAmount: 4800.00,
        monthlyAmount: 400.00,
        remainingPayments: 8,
        nextPaymentDate: '2024-03-15',
        status: 'active'
      }
    ];

    setInvoices(mockInvoices);
    setPaymentPlans(mockPaymentPlans);
    setLoading(false);
  }, []);

  const filteredInvoices = invoices
    .filter(invoice => filterStatus === 'all' || invoice.status === filterStatus)
    .filter(invoice => filterCategory === 'all' || invoice.category === filterCategory)
    .filter(invoice =>
      invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(invoice => {
      if (!dateRange.start || !dateRange.end) return true;
      const invoiceDate = new Date(invoice.date);
      const start = new Date(dateRange.start);
      const end = new Date(dateRange.end);
      return invoiceDate >= start && invoiceDate <= end;
    });

  const totalAmount = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
  const paidAmount = invoices
    .filter(invoice => invoice.status === 'paid')
    .reduce((sum, invoice) => sum + invoice.amount, 0);
  const pendingAmount = invoices
    .filter(invoice => invoice.status === 'pending' || invoice.status === 'overdue')
    .reduce((sum, invoice) => sum + invoice.amount, 0);
  const partialAmount = invoices
    .filter(invoice => invoice.status === 'partial')
    .reduce((sum, invoice) => sum + (invoice.amount - (invoice.paidAmount || 0)), 0);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'partial':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'overdue':
        return 'En retard';
      case 'partial':
        return 'Partiellement payée';
      default:
        return status;
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'consultation':
        return 'Consultation';
      case 'treatment':
        return 'Traitement';
      case 'equipment':
        return 'Équipement';
      case 'emergency':
        return 'Urgence';
      default:
        return category;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Factures</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Gérez vos factures et paiements
            </p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowAnalytics(!showAnalytics)}
              className="flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
            >
              <PieChart className="w-5 h-5 mr-2" />
              Analytiques
            </button>
          </div>
        </div>
      </div>

      {/* Plans de paiement */}
      {paymentPlans.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Plans de paiement actifs
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {paymentPlans.map(plan => (
              <div
                key={plan.id}
                className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl p-6 text-white"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Shield className="w-6 h-6 mr-2" />
                    <h3 className="text-lg font-semibold">{plan.name}</h3>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    plan.status === 'active'
                      ? 'bg-green-500/20 text-green-100'
                      : plan.status === 'completed'
                      ? 'bg-blue-500/20 text-blue-100'
                      : 'bg-yellow-500/20 text-yellow-100'
                  }`}>
                    {plan.status === 'active' ? 'Actif' : plan.status === 'completed' ? 'Terminé' : 'En pause'}
                  </span>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-purple-100">Montant total</span>
                    <span className="font-semibold">{formatCurrency(plan.totalAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-purple-100">Mensualité</span>
                    <span className="font-semibold">{formatCurrency(plan.monthlyAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-purple-100">Paiements restants</span>
                    <span className="font-semibold">{plan.remainingPayments}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between pt-4 border-t border-white/20">
                  <div className="text-sm">
                    Prochain paiement le {formatDate(plan.nextPaymentDate)}
                  </div>
                  <button className="px-4 py-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                    Gérer
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Total facturé
            </h3>
            <FileText className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(totalAmount)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {invoices.length} factures au total
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Montant payé
            </h3>
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(paidAmount)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {invoices.filter(i => i.status === 'paid').length} factures payées
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Montant en attente
            </h3>
            <AlertCircle className="w-5 h-5 text-yellow-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(pendingAmount)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {invoices.filter(i => i.status === 'pending' || i.status === 'overdue').length} factures en attente
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Paiements partiels
            </h3>
            <Wallet className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(partialAmount)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {invoices.filter(i => i.status === 'partial').length} factures partiellement payées
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Rechercher une facture..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Tous les statuts</option>
              <option value="paid">Payées</option>
              <option value="pending">En attente</option>
              <option value="overdue">En retard</option>
              <option value="partial">Partiellement payées</option>
            </select>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Toutes les catégories</option>
              <option value="consultation">Consultations</option>
              <option value="treatment">Traitements</option>
              <option value="equipment">Équipements</option>
              <option value="emergency">Urgences</option>
            </select>
            <div className="flex items-center gap-2">
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <span className="text-gray-500">à</span>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Liste des factures */}
      <div className="space-y-4">
        {filteredInvoices.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 text-center">
            <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Aucune facture trouvée
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm
                ? 'Aucune facture ne correspond à votre recherche'
                : 'Vous n\'avez pas encore de factures'}
            </p>
          </div>
        ) : (
          filteredInvoices.map(invoice => (
            <div
              key={invoice.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6"
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {invoice.number}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                      {getStatusText(invoice.status)}
                    </span>
                    <span className="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                      {getCategoryText(invoice.category)}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 mb-2">
                    {invoice.description}
                  </p>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div>
                      <span className="font-medium">Date :</span> {formatDate(invoice.date)}
                    </div>
                    <div>
                      <span className="font-medium">Échéance :</span> {formatDate(invoice.dueDate)}
                    </div>
                    {invoice.paidAt && (
                      <div>
                        <span className="font-medium">Payée le :</span> {formatDate(invoice.paidAt)}
                      </div>
                    )}
                    {invoice.paymentMethod && (
                      <div>
                        <span className="font-medium">Méthode :</span> {invoice.paymentMethod}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatCurrency(invoice.amount)}
                    </div>
                    {invoice.status === 'partial' && (
                      <div className="text-sm text-green-600 dark:text-green-400">
                        Payé : {formatCurrency(invoice.paidAmount || 0)}
                      </div>
                    )}
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {invoice.items.length} article{invoice.items.length > 1 ? 's' : ''}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setShowInvoiceModal(invoice)}
                      className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      title="Voir les détails"
                    >
                      <FileText className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => {/* Logique de téléchargement */}}
                      className="p-2 text-gray-500 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                      title="Télécharger"
                    >
                      <Download className="w-5 h-5" />
                    </button>
                    {invoice.status !== 'paid' && (
                      <button
                        onClick={() => setShowPaymentModal(invoice)}
                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        {invoice.status === 'partial' ? 'Compléter' : 'Payer'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Modal de détails de facture */}
      {showInvoiceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-3xl mx-4">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                  Facture {showInvoiceModal.number}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {showInvoiceModal.description}
                </p>
              </div>
              <button
                onClick={() => setShowInvoiceModal(null)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="border-t border-b border-gray-200 dark:border-gray-700 py-4 mb-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-gray-500 dark:text-gray-400 mb-1">Date de facture</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatDate(showInvoiceModal.date)}
                  </div>
                </div>
                <div>
                  <div className="text-gray-500 dark:text-gray-400 mb-1">Date d'échéance</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatDate(showInvoiceModal.dueDate)}
                  </div>
                </div>
                <div>
                  <div className="text-gray-500 dark:text-gray-400 mb-1">Statut</div>
                  <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(showInvoiceModal.status)}`}>
                    {getStatusText(showInvoiceModal.status)}
                  </div>
                </div>
                <div>
                  <div className="text-gray-500 dark:text-gray-400 mb-1">Montant total</div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatCurrency(showInvoiceModal.amount)}
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Détails des prestations
              </h4>
              <div className="space-y-4">
                {showInvoiceModal.items.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-0"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {item.description}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {item.quantity} x {formatCurrency(item.unitPrice)}
                      </div>
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(item.total)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <button
                onClick={() => {/* Logique de téléchargement */}}
                className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <Download className="w-5 h-5 mr-2" />
                Télécharger PDF
              </button>
              {showInvoiceModal.status !== 'paid' && (
                <button
                  onClick={() => {
                    setShowPaymentModal(showInvoiceModal);
                    setShowInvoiceModal(null);
                  }}
                  className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payer maintenant
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Modal de paiement */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Paiement de la facture {showPaymentModal.number}
              </h3>
              <button
                onClick={() => setShowPaymentModal(null)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600 dark:text-gray-400">Montant total</span>
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(showPaymentModal.amount)}
                  </span>
                </div>
                {showPaymentModal.status === 'partial' && (
                  <>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-600 dark:text-gray-400">Déjà payé</span>
                      <span className="text-green-600 dark:text-green-400">
                        -{formatCurrency(showPaymentModal.paidAmount || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center font-semibold">
                      <span className="text-gray-900 dark:text-white">Reste à payer</span>
                      <span className="text-lg text-red-600 dark:text-red-400">
                        {formatCurrency(showPaymentModal.amount - (showPaymentModal.paidAmount || 0))}
                      </span>
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Méthode de paiement
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option>Carte bancaire</option>
                    <option>Virement bancaire</option>
                    <option>PayPal</option>
                    <option>Apple Pay</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Numéro de carte
                  </label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Date d'expiration
                    </label>
                    <input
                      type="text"
                      placeholder="MM/AA"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      CVV
                    </label>
                    <input
                      type="text"
                      placeholder="123"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setShowPaymentModal(null)}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  // Logique de paiement
                  setShowPaymentModal(null);
                  alert('Paiement effectué avec succès !');
                }}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Payer maintenant
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BillingPremium;
