export interface User {
  id: string;
  email: string;
  fullName: string;
  role: 'patient' | 'practitioner' | 'admin';
  practitionerCode?: string;
  isVip?: boolean;
  subscriptionStatus?: 'trial' | 'active' | 'expired';
  trialEndDate?: Date;
  createdAt: Date;
}

export interface Patient extends User {
  role: 'patient';
  practitionerCode: string;
  medicalRecord: MedicalRecord;
  progressData: ProgressData;
}

export interface Practitioner extends User {
  role: 'practitioner';
  practitionerCode: string;
  patients: string[];
  subscriptionStatus: 'trial' | 'active' | 'expired';
  trialEndDate: Date;
}

export interface MedicalRecord {
  patientId: string;
  diagnosis: string;
  treatmentPlan: string;
  notes: string[];
  photos: Photo[];
  appointments: Appointment[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Photo {
  id: string;
  patientId: string;
  url: string;
  thumbnail: string;
  type: 'intraoral' | 'extraoral' | 'cephalometric' | 'panoramic' | 'cbct' | 'clinical' | 'progress';
  category: string;
  date: Date;
  notes?: string;
  analysis?: {
    quality: number;
    issues: string[];
    progress: number;
    aiAnalysis?: AIAnalysisResult;
  };
  metadata?: {
    camera: string;
    settings: any;
    location: string;
    practitioner: string;
  };
}

export interface AIAnalysisResult {
  confidence: number;
  detectedTeeth: number[];
  movements: ToothMovement[];
  qualityScore: number;
  recommendations: string[];
  riskFactors: string[];
  progressScore: number;
}

export interface ToothMovement {
  toothNumber: number;
  movement: {
    mesialDistal: number;
    buccoLingual: number;
    intrusion: number;
    rotation: number;
  };
  status: 'on-track' | 'delayed' | 'ahead' | 'concern';
}

export interface Appointment {
  id: string;
  patientId: string;
  practitionerId: string;
  date: Date;
  type: 'regular' | 'emergency';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

export interface ProgressData {
  patientId: string;
  dailyWear: { [date: string]: boolean };
  totalDays: number;
  completedDays: number;
  progressPercentage: number;
  clinicalMeasurements?: ClinicalMeasurement[];
  cephalometricAnalysis?: CephalometricAnalysis;
  treatmentPlan?: TreatmentPlan;
}

// Nouvelles interfaces pour les fonctionnalités cliniques avancées

export interface ClinicalMeasurement {
  id: string;
  patientId: string;
  date: Date;
  type: 'overjet' | 'overbite' | 'midline' | 'arch_width' | 'arch_length' | 'crowding' | 'spacing';
  value: number;
  unit: 'mm' | 'degrees' | 'percentage';
  reference: 'upper' | 'lower' | 'both';
  notes?: string;
  practitioner: string;
}

export interface CephalometricAnalysis {
  id: string;
  patientId: string;
  date: Date;
  imageUrl: string;
  landmarks: CephalometricLandmark[];
  measurements: CephalometricMeasurement[];
  analysis: {
    skeletal: SkeletalAnalysis;
    dental: DentalAnalysis;
    soft_tissue: SoftTissueAnalysis;
  };
  interpretation: string;
  recommendations: string[];
}

export interface CephalometricLandmark {
  name: string;
  x: number;
  y: number;
  confidence: number;
  manual: boolean;
}

export interface CephalometricMeasurement {
  name: string;
  value: number;
  unit: 'mm' | 'degrees';
  normal_range: { min: number; max: number };
  interpretation: 'normal' | 'increased' | 'decreased' | 'severe';
}

export interface SkeletalAnalysis {
  sna: number;
  snb: number;
  anb: number;
  wits: number;
  facial_angle: number;
  mandibular_plane_angle: number;
  classification: 'Class I' | 'Class II' | 'Class III';
}

export interface DentalAnalysis {
  upper_incisor_to_na: number;
  lower_incisor_to_nb: number;
  interincisal_angle: number;
  upper_molar_position: number;
  lower_molar_position: number;
}

export interface SoftTissueAnalysis {
  nasolabial_angle: number;
  mentolabial_angle: number;
  lip_protrusion: number;
  facial_convexity: number;
}

export interface TreatmentPlan {
  id: string;
  patientId: string;
  practitionerId: string;
  created: Date;
  updated: Date;
  status: 'draft' | 'active' | 'completed' | 'modified';

  objectives: TreatmentObjective[];
  phases: TreatmentPhase[];
  timeline: TreatmentTimeline;

  appliances: ApplianceInfo[];
  protocols: TreatmentProtocol[];

  estimatedDuration: number; // en mois
  estimatedCost: number;

  notes: string;
  attachments: string[];
}

export interface TreatmentObjective {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'alignment' | 'spacing' | 'bite' | 'esthetics' | 'function';
  target_value?: number;
  current_value?: number;
  achieved: boolean;
}

export interface TreatmentPhase {
  id: string;
  name: string;
  description: string;
  order: number;
  duration: number; // en semaines
  objectives: string[];
  appliances: string[];
  appointments: number;
  status: 'pending' | 'active' | 'completed';
  start_date?: Date;
  end_date?: Date;
}

export interface TreatmentTimeline {
  total_duration: number;
  phases: {
    phase_id: string;
    start_week: number;
    end_week: number;
  }[];
  milestones: {
    week: number;
    description: string;
    type: 'appointment' | 'evaluation' | 'change' | 'completion';
  }[];
}

export interface ApplianceInfo {
  id: string;
  type: 'aligners' | 'brackets' | 'expander' | 'retainer' | 'elastics';
  brand: string;
  model: string;
  specifications: any;
  wear_schedule: {
    hours_per_day: number;
    change_frequency: number; // en jours
    total_sets?: number;
  };
  instructions: string[];
}

export interface TreatmentProtocol {
  id: string;
  name: string;
  description: string;
  category: 'hygiene' | 'compliance' | 'emergency' | 'maintenance';
  steps: ProtocolStep[];
  frequency: string;
  duration: string;
}

export interface ProtocolStep {
  order: number;
  instruction: string;
  duration?: string;
  tools_required?: string[];
  warnings?: string[];
}

export interface Model3D {
  id: string;
  patientId: string;
  date: Date;
  type: 'initial' | 'progress' | 'final' | 'prediction';
  upper_arch_url: string;
  lower_arch_url: string;
  bite_registration_url?: string;

  measurements: {
    arch_width: { upper: number; lower: number };
    arch_length: { upper: number; lower: number };
    tooth_sizes: { [tooth: number]: number };
    crowding: { upper: number; lower: number };
    spacing: { upper: number; lower: number };
  };

  analysis: {
    bolton_ratio: number;
    arch_discrepancy: number;
    predicted_movements: ToothMovement[];
  };

  scanner_info: {
    brand: string;
    model: string;
    resolution: number;
    accuracy: number;
  };
}

export interface RadiographicImage {
  id: string;
  patientId: string;
  date: Date;
  type: 'panoramic' | 'lateral_ceph' | 'pa_ceph' | 'periapical' | 'bitewing' | 'cbct';
  url: string;
  thumbnail: string;

  technical_data: {
    kvp: number;
    ma: number;
    exposure_time: number;
    focal_distance: number;
  };

  analysis?: {
    pathology_detected: boolean;
    findings: string[];
    recommendations: string[];
    ai_confidence: number;
  };

  measurements?: RadiographicMeasurement[];
  annotations?: ImageAnnotation[];
}

export interface RadiographicMeasurement {
  id: string;
  name: string;
  value: number;
  unit: string;
  coordinates: { x1: number; y1: number; x2: number; y2: number };
  reference: string;
}

export interface ImageAnnotation {
  id: string;
  type: 'point' | 'line' | 'circle' | 'polygon' | 'text';
  coordinates: number[];
  label: string;
  color: string;
  practitioner: string;
  date: Date;
}

export interface ForumPost {
  id: string;
  authorId: string;
  authorName: string;
  title: string;
  content: string;
  category: string;
  isAnonymous: boolean;
  createdAt: Date;
  comments: Comment[];
  likes: number;
}

export interface Comment {
  id: string;
  authorId: string;
  authorName: string;
  content: string;
  createdAt: Date;
  isAnonymous: boolean;
}

export type Language = 'fr' | 'en';