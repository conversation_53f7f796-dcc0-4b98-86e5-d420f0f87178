import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  SparklesIcon,
  EyeIcon,
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  LightBulbIcon,
  StarIcon,
  FireIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CurrencyEuroIcon,
  TrophyIcon,
  AcademicCapIcon,
  HeartIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface PracticeAnalytics {
  totalPatients: number;
  activePatients: number;
  newPatientsThisMonth: number;
  averageComplianceRate: number;
  totalRevenue: number;
  monthlyRevenue: number;
  appointmentsThisWeek: number;
  completedTreatments: number;
  averageTreatmentDuration: number;
  patientSatisfactionScore: number;
  treatmentSuccessRate: number;
  referralRate: number;
  trends: {
    patients: 'up' | 'down' | 'stable';
    revenue: 'up' | 'down' | 'stable';
    compliance: 'up' | 'down' | 'stable';
    satisfaction: 'up' | 'down' | 'stable';
  };
  monthlyData: {
    month: string;
    patients: number;
    revenue: number;
    compliance: number;
    appointments: number;
  }[];
  topPerformingTreatments: {
    type: string;
    count: number;
    successRate: number;
    avgDuration: number;
  }[];
  insights: string[];
  alerts: {
    type: 'warning' | 'info' | 'success';
    message: string;
    action?: string;
  }[];
}

const PractitionerAnalytics: React.FC = () => {
  const { t } = useLanguage();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');
  const [analyticsData, setAnalyticsData] = useState<PracticeAnalytics>({
    totalPatients: 127,
    activePatients: 89,
    newPatientsThisMonth: 12,
    averageComplianceRate: 84,
    totalRevenue: 45600,
    monthlyRevenue: 8900,
    appointmentsThisWeek: 24,
    completedTreatments: 34,
    averageTreatmentDuration: 18.5,
    patientSatisfactionScore: 4.7,
    treatmentSuccessRate: 92,
    referralRate: 23,
    trends: {
      patients: 'up',
      revenue: 'up',
      compliance: 'stable',
      satisfaction: 'up'
    },
    monthlyData: [
      { month: 'Jan', patients: 15, revenue: 7200, compliance: 82, appointments: 95 },
      { month: 'Fév', patients: 18, revenue: 8100, compliance: 85, appointments: 102 },
      { month: 'Mar', patients: 12, revenue: 8900, compliance: 84, appointments: 98 },
      { month: 'Avr', patients: 20, revenue: 9500, compliance: 87, appointments: 110 }
    ],
    topPerformingTreatments: [
      { type: 'Invisalign', count: 45, successRate: 95, avgDuration: 16.2 },
      { type: 'Bagues traditionnelles', count: 32, successRate: 88, avgDuration: 22.1 },
      { type: 'Gouttières', count: 28, successRate: 92, avgDuration: 14.8 },
      { type: 'Contention', count: 22, successRate: 98, avgDuration: 6.5 }
    ],
    insights: [
      'Votre taux de satisfaction patient a augmenté de 8% ce mois',
      'Les traitements Invisalign montrent les meilleurs résultats',
      'Pic d\'activité détecté les mardis et jeudis',
      'Opportunité d\'optimisation: créneaux libres le vendredi après-midi'
    ],
    alerts: [
      { type: 'warning', message: '3 patients ont un taux de conformité < 70%', action: 'Voir les patients' },
      { type: 'info', message: 'Nouveau rapport mensuel disponible', action: 'Télécharger' },
      { type: 'success', message: 'Objectif mensuel de revenus atteint à 105%' }
    ]
  });

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <ArrowTrendingUpIcon className="w-5 h-5 text-green-500" />;
      case 'down':
        return <ArrowTrendingDownIcon className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 bg-gray-400 rounded-full" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getPerformanceColor = (score: number, threshold: number = 80) => {
    if (score >= threshold) return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:bg-green-900/20 dark:text-green-400';
    if (score >= threshold * 0.7) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400 dark:bg-yellow-900/20 dark:text-yellow-400';
    return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:bg-red-900/20 dark:text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* En-tête avec sélecteur de période */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-adaptive">{t('practitioner.analytics.title')}</h2>
          <p className="text-adaptive-secondary">{t('practitioner.analytics.subtitle')}</p>
        </div>
        <div className="flex space-x-2">
          {(['week', 'month', 'quarter'] as const).map((period) => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedPeriod === period
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'surface-secondary text-adaptive hover:surface-elevated'
              }`}
            >
              {t(`analytics.period.${period}`)}
            </button>
          ))}
        </div>
      </div>

      {/* Alertes importantes */}
      {analyticsData.alerts.length > 0 && (
        <div className="space-y-3">
          {analyticsData.alerts.map((alert, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className={`p-4 rounded-xl border-l-4 ${
                alert.type === 'warning' 
                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 text-yellow-800 dark:text-yellow-200'
                  : alert.type === 'success'
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-400 text-green-800 dark:text-green-200'
                  : 'bg-blue-50 dark:bg-blue-900/20 border-blue-400 text-blue-800 dark:text-blue-200'
              }`}
            >
              <div className="flex justify-between items-center">
                <span className="font-medium">{alert.message}</span>
                {alert.action && (
                  <button className="text-sm underline hover:no-underline">
                    {alert.action}
                  </button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-adaptive-secondary text-sm">{t('practitioner.analytics.totalPatients')}</p>
              <p className="text-2xl font-bold text-adaptive">{analyticsData.totalPatients}</p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(analyticsData.trends.patients)}
                <span className="text-sm text-adaptive-secondary">+{analyticsData.newPatientsThisMonth} ce mois</span>
              </div>
            </div>
            <UserGroupIcon className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-adaptive-secondary text-sm">{t('practitioner.analytics.monthlyRevenue')}</p>
              <p className="text-2xl font-bold text-adaptive">{formatCurrency(analyticsData.monthlyRevenue)}</p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(analyticsData.trends.revenue)}
                <span className="text-sm text-adaptive-secondary">vs mois dernier</span>
              </div>
            </div>
            <CurrencyEuroIcon className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-adaptive-secondary text-sm">{t('practitioner.analytics.avgCompliance')}</p>
              <p className="text-2xl font-bold text-adaptive">{analyticsData.averageComplianceRate}%</p>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(analyticsData.trends.compliance)}
                <span className={`text-sm px-2 py-1 rounded-full ${getPerformanceColor(analyticsData.averageComplianceRate)}`}>
                  {analyticsData.averageComplianceRate >= 80 ? 'Excellent' : 'À améliorer'}
                </span>
              </div>
            </div>
            <ShieldCheckIcon className="w-8 h-8 text-purple-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-adaptive-secondary text-sm">{t('practitioner.analytics.satisfaction')}</p>
              <div className="flex items-center space-x-2">
                <p className="text-2xl font-bold text-adaptive">{analyticsData.patientSatisfactionScore}</p>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(analyticsData.patientSatisfactionScore)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(analyticsData.trends.satisfaction)}
                <span className="text-sm text-adaptive-secondary">Basé sur 89 avis</span>
              </div>
            </div>
            <HeartIcon className="w-8 h-8 text-red-500" />
          </div>
        </motion.div>
      </div>

      {/* Graphiques et analyses détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution mensuelle */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-adaptive">{t('practitioner.analytics.monthlyEvolution')}</h3>
            <ChartBarIcon className="w-6 h-6 text-blue-500" />
          </div>
          <div className="space-y-4">
            {analyticsData.monthlyData.map((data, index) => (
              <div key={data.month} className="flex items-center justify-between">
                <span className="text-adaptive-secondary font-medium">{data.month}</span>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm text-adaptive">{data.patients} patients</div>
                    <div className="text-xs text-adaptive-secondary">{formatCurrency(data.revenue)}</div>
                  </div>
                  <div className="w-20 surface-tertiary dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(data.compliance / 100) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-adaptive">{data.compliance}%</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Top traitements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="surface-elevated p-6 rounded-xl"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-adaptive">{t('practitioner.analytics.topTreatments')}</h3>
            <TrophyIcon className="w-6 h-6 text-yellow-500" />
          </div>
          <div className="space-y-4">
            {analyticsData.topPerformingTreatments.map((treatment, index) => (
              <div key={treatment.type} className="flex items-center justify-between p-3 surface-secondary rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                    index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-adaptive">{treatment.type}</div>
                    <div className="text-sm text-adaptive-secondary">{treatment.count} cas</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-adaptive">{treatment.successRate}% succès</div>
                  <div className="text-xs text-adaptive-secondary">{treatment.avgDuration} mois moy.</div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Insights et recommandations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="surface-elevated p-6 rounded-xl"
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
            <SparklesIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-adaptive">{t('practitioner.analytics.aiInsights')}</h3>
            <p className="text-adaptive-secondary text-sm">{t('practitioner.analytics.aiInsightsDesc')}</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {analyticsData.insights.map((insight, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 + index * 0.1 }}
              className="flex items-start space-x-3 p-4 surface-secondary rounded-lg"
            >
              <LightBulbIcon className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <p className="text-adaptive text-sm">{insight}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Métriques avancées */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="surface-elevated p-6 rounded-xl text-center"
        >
          <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full mb-4">
            <AcademicCapIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h4 className="text-lg font-semibold text-adaptive mb-2">{t('practitioner.analytics.successRate')}</h4>
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">{analyticsData.treatmentSuccessRate}%</p>
          <p className="text-adaptive-secondary text-sm mt-2">Taux de réussite des traitements</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
          className="surface-elevated p-6 rounded-xl text-center"
        >
          <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full mb-4">
            <ClockIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h4 className="text-lg font-semibold text-adaptive mb-2">{t('practitioner.analytics.avgDuration')}</h4>
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{analyticsData.averageTreatmentDuration}</p>
          <p className="text-adaptive-secondary text-sm mt-2">Mois en moyenne</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="surface-elevated p-6 rounded-xl text-center"
        >
          <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full mb-4">
            <BoltIcon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h4 className="text-lg font-semibold text-adaptive mb-2">{t('practitioner.analytics.referralRate')}</h4>
          <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{analyticsData.referralRate}%</p>
          <p className="text-adaptive-secondary text-sm mt-2">Taux de recommandation</p>
        </motion.div>
      </div>
    </div>
  );
};

export default PractitionerAnalytics;
