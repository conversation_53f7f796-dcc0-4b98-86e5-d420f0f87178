const puppeteer = require('puppeteer');

async function testApplication() {
  console.log('🚀 DÉMARRAGE DES TESTS COMPLETS ORTHOPROGRESS');
  console.log('================================================');
  
  let browser;
  try {
    // Lancer le navigateur
    browser = await puppeteer.launch({ 
      headless: false, // Mode visible pour voir les tests
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Écouter les erreurs de console
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Erreur Console:', msg.text());
      }
    });
    
    page.on('pageerror', error => {
      console.log('❌ Erreur Page:', error.message);
    });
    
    console.log('\n📱 TEST 1: Chargement de la page d\'accueil');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Vérifier le titre
    const title = await page.title();
    console.log('✅ Titre:', title);
    
    // Attendre que React se charge
    await page.waitForSelector('#root', { timeout: 10000 });
    console.log('✅ Root element trouvé');
    
    // Vérifier si on est redirigé vers /auth
    const currentUrl = page.url();
    console.log('✅ URL actuelle:', currentUrl);
    
    if (currentUrl.includes('/auth')) {
      console.log('\n🔐 TEST 2: Page d\'authentification');
      
      // Vérifier la présence du logo
      const logoExists = await page.$('div[class*="gradient"]') !== null;
      console.log('✅ Logo présent:', logoExists);
      
      // Vérifier les boutons de connexion
      const loginFormExists = await page.$('form') !== null;
      console.log('✅ Formulaire de connexion présent:', loginFormExists);
      
      // Tester le changement de thème
      const themeButton = await page.$('button[class*="fixed"]');
      if (themeButton) {
        await themeButton.click();
        console.log('✅ Bouton thème cliqué');
        await page.waitForTimeout(1000);
      }
      
      // Simuler une connexion patient
      console.log('\n👤 TEST 3: Connexion Patient');
      
      // Remplir le formulaire de connexion
      const emailInput = await page.$('input[type="email"]');
      const passwordInput = await page.$('input[type="password"]');
      
      if (emailInput && passwordInput) {
        await emailInput.type('<EMAIL>');
        await passwordInput.type('password123');
        console.log('✅ Formulaire rempli');
        
        // Cliquer sur le bouton de connexion
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          console.log('✅ Bouton connexion cliqué');
          
          // Attendre la redirection
          await page.waitForTimeout(2000);
          const newUrl = page.url();
          console.log('✅ Nouvelle URL:', newUrl);
        }
      }
    }
    
    console.log('\n🎯 TEST 4: Navigation dans l\'application');
    
    // Prendre une capture d'écran
    await page.screenshot({ path: 'test-screenshot.png', fullPage: true });
    console.log('✅ Capture d\'écran sauvegardée: test-screenshot.png');
    
    // Tester la responsivité
    console.log('\n📱 TEST 5: Responsivité');
    await page.setViewport({ width: 768, height: 1024 }); // Tablette
    await page.waitForTimeout(1000);
    console.log('✅ Mode tablette testé');
    
    await page.setViewport({ width: 375, height: 667 }); // Mobile
    await page.waitForTimeout(1000);
    console.log('✅ Mode mobile testé');
    
    console.log('\n🎉 TESTS TERMINÉS AVEC SUCCÈS !');
    
  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Vérifier si Puppeteer est disponible
try {
  testApplication();
} catch (error) {
  console.log('⚠️  Puppeteer non disponible, tests manuels requis');
  console.log('📋 PLAN DE TEST MANUEL:');
  console.log('1. Ouvrir http://localhost:3000');
  console.log('2. Vérifier la redirection vers /auth');
  console.log('3. Tester le formulaire de connexion');
  console.log('4. Vérifier le changement de thème');
  console.log('5. Tester la navigation');
  console.log('6. Vérifier la responsivité');
}
