// Script de test complet pour OrthoProgress
console.log('🧪 TESTS COMPLETS ORTHOPROGRESS');
console.log('================================\n');

// Fonction pour tester l'état de l'application
function testApplicationState() {
  console.log('📋 1. ÉTAT DE L\'APPLICATION');
  console.log('----------------------------');
  
  // Vérifier l'URL actuelle
  console.log(`✓ URL actuelle: ${window.location.href}`);
  console.log(`✓ Chemin: ${window.location.pathname}`);
  
  // Vérifier le titre
  console.log(`✓ Titre: ${document.title}`);
  
  // Vérifier la langue
  const htmlLang = document.documentElement.lang;
  console.log(`✓ Langue: ${htmlLang || 'non définie'}`);
  
  // Vérifier le thème
  const isDark = document.documentElement.classList.contains('dark');
  console.log(`✓ Thème: ${isDark ? 'Sombre' : 'Clair'}`);
  
  console.log('');
}

// Fonction pour tester l'interface utilisateur
function testUserInterface() {
  console.log('🎨 2. INTERFACE UTILISATEUR');
  console.log('---------------------------');
  
  // Compter les éléments principaux
  const buttons = document.querySelectorAll('button');
  const links = document.querySelectorAll('a');
  const forms = document.querySelectorAll('form');
  const inputs = document.querySelectorAll('input');
  
  console.log(`✓ Boutons: ${buttons.length}`);
  console.log(`✓ Liens: ${links.length}`);
  console.log(`✓ Formulaires: ${forms.length}`);
  console.log(`✓ Champs de saisie: ${inputs.length}`);
  
  // Vérifier les icônes
  const icons = document.querySelectorAll('svg');
  console.log(`✓ Icônes SVG: ${icons.length}`);
  
  // Vérifier les images
  const images = document.querySelectorAll('img');
  console.log(`✓ Images: ${images.length}`);
  
  console.log('');
}

// Fonction pour tester la navigation
function testNavigation() {
  console.log('🧭 3. NAVIGATION');
  console.log('----------------');
  
  // Vérifier la navigation principale
  const navElements = document.querySelectorAll('nav');
  console.log(`✓ Éléments de navigation: ${navElements.length}`);
  
  // Vérifier les liens de navigation
  const navLinks = document.querySelectorAll('nav a, [role="navigation"] a');
  console.log(`✓ Liens de navigation: ${navLinks.length}`);
  
  // Vérifier le menu mobile
  const mobileMenu = document.querySelector('[class*="mobile"], [class*="hamburger"]');
  console.log(`✓ Menu mobile: ${mobileMenu ? 'Présent' : 'Non trouvé'}`);
  
  console.log('');
}

// Fonction pour tester l'accessibilité
function testAccessibility() {
  console.log('♿ 4. ACCESSIBILITÉ');
  console.log('-------------------');
  
  // Vérifier les attributs alt des images
  const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
  console.log(`✓ Images sans alt: ${imagesWithoutAlt.length}`);
  
  // Vérifier les labels des formulaires
  const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
  const labelsCount = document.querySelectorAll('label').length;
  console.log(`✓ Labels de formulaire: ${labelsCount}`);
  console.log(`✓ Champs sans label: ${inputsWithoutLabels.length}`);
  
  // Vérifier les rôles ARIA
  const elementsWithRole = document.querySelectorAll('[role]');
  console.log(`✓ Éléments avec rôle ARIA: ${elementsWithRole.length}`);
  
  // Vérifier les titres de section
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  console.log(`✓ Titres de section: ${headings.length}`);
  
  console.log('');
}

// Fonction pour tester les fonctionnalités
function testFunctionality() {
  console.log('⚙️ 5. FONCTIONNALITÉS');
  console.log('---------------------');
  
  // Tester le localStorage
  try {
    localStorage.setItem('test', 'value');
    localStorage.removeItem('test');
    console.log('✓ LocalStorage: Fonctionnel');
  } catch (e) {
    console.log('❌ LocalStorage: Erreur');
  }
  
  // Vérifier les données utilisateur
  const userData = localStorage.getItem('user');
  console.log(`✓ Données utilisateur: ${userData ? 'Présentes' : 'Absentes'}`);
  
  // Vérifier les préférences de thème
  const themeData = localStorage.getItem('theme');
  console.log(`✓ Préférences thème: ${themeData || 'Par défaut'}`);
  
  // Tester les événements
  let eventListeners = 0;
  const testElement = document.createElement('div');
  testElement.addEventListener('click', () => eventListeners++);
  testElement.click();
  console.log(`✓ Gestionnaires d'événements: ${eventListeners > 0 ? 'Fonctionnels' : 'Problème'}`);
  
  console.log('');
}

// Fonction pour tester les performances rapides
function testQuickPerformance() {
  console.log('⚡ 6. PERFORMANCES RAPIDES');
  console.log('--------------------------');
  
  // Temps de chargement
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    const loadTime = Math.round(navigation.loadEventEnd - navigation.navigationStart);
    console.log(`✓ Temps de chargement: ${loadTime}ms`);
    
    if (loadTime < 1000) console.log('  🟢 Excellent');
    else if (loadTime < 2000) console.log('  🟡 Bon');
    else console.log('  🔴 À améliorer');
  }
  
  // Nombre de ressources
  const resources = performance.getEntriesByType('resource');
  console.log(`✓ Ressources chargées: ${resources.length}`);
  
  // Taille approximative
  const totalSize = resources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
  const sizeMB = (totalSize / 1024 / 1024).toFixed(2);
  console.log(`✓ Taille totale: ~${sizeMB}MB`);
  
  console.log('');
}

// Fonction pour tester la responsivité rapide
function testQuickResponsive() {
  console.log('📱 7. RESPONSIVITÉ RAPIDE');
  console.log('-------------------------');
  
  // Vérifier les classes responsives
  const responsiveElements = document.querySelectorAll('[class*="sm:"], [class*="md:"], [class*="lg:"], [class*="xl:"]');
  console.log(`✓ Éléments responsifs: ${responsiveElements.length}`);
  
  // Vérifier les grilles
  const gridElements = document.querySelectorAll('[class*="grid-cols"]');
  console.log(`✓ Grilles responsives: ${gridElements.length}`);
  
  // Vérifier le viewport
  const viewport = document.querySelector('meta[name="viewport"]');
  console.log(`✓ Meta viewport: ${viewport ? 'Présent' : 'Manquant'}`);
  
  console.log('');
}

// Fonction pour générer un rapport final
function generateFinalReport() {
  console.log('📊 8. RAPPORT FINAL');
  console.log('-------------------');
  
  let score = 100;
  const issues = [];
  
  // Vérifications critiques
  if (document.querySelectorAll('img:not([alt])').length > 0) {
    score -= 10;
    issues.push('Images sans attribut alt');
  }
  
  if (!document.querySelector('meta[name="viewport"]')) {
    score -= 15;
    issues.push('Meta viewport manquant');
  }
  
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.navigationStart;
    if (loadTime > 3000) {
      score -= 20;
      issues.push('Temps de chargement trop long');
    }
  }
  
  if (!localStorage.getItem('user') && window.location.pathname !== '/auth') {
    score -= 5;
    issues.push('État d\'authentification incohérent');
  }
  
  console.log(`✓ Score global: ${score}/100`);
  
  if (score >= 90) {
    console.log('🟢 EXCELLENT - Application prête pour la production');
  } else if (score >= 75) {
    console.log('🟡 BON - Quelques améliorations possibles');
  } else if (score >= 60) {
    console.log('🟠 MOYEN - Améliorations recommandées');
  } else {
    console.log('🔴 FAIBLE - Corrections nécessaires');
  }
  
  if (issues.length > 0) {
    console.log('\n⚠️ Points d\'amélioration:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  console.log('\n✅ Tests terminés avec succès !');
}

// Fonction principale
function runCompleteTests() {
  console.log('Démarrage des tests complets...\n');
  
  testApplicationState();
  testUserInterface();
  testNavigation();
  testAccessibility();
  testFunctionality();
  testQuickPerformance();
  testQuickResponsive();
  
  setTimeout(() => {
    generateFinalReport();
  }, 1000);
}

// Démarrer les tests
runCompleteTests();
