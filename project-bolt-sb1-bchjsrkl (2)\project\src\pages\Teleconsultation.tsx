import React, { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { Video, Calendar, Clock, User, Phone, MessageCircle, Camera, Mic, MicOff, VideoOff, PhoneOff } from 'lucide-react';

const Teleconsultation: React.FC = () => {
  const { t } = useLanguage();
  const [isInCall, setIsInCall] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  const upcomingAppointments = [
    {
      id: 1,
      doctor: 'Dr. <PERSON>',
      date: '2024-07-05',
      time: '14:30',
      type: 'Suivi de routine',
      status: 'confirmed'
    },
    {
      id: 2,
      doctor: 'Dr. <PERSON>',
      date: '2024-07-12',
      time: '10:00',
      type: 'Consultation de contrôle',
      status: 'pending'
    }
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isInCall) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isInCall]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startCall = () => {
    setIsInCall(true);
    setCallDuration(0);
  };

  const endCall = () => {
    setIsInCall(false);
    setCallDuration(0);
    setIsMuted(false);
    setIsVideoOff(false);
  };

  if (isInCall) {
    return (
      <div className="fixed inset-0 bg-gray-900 flex flex-col">
        {/* En-tête de l'appel */}
        <div className="bg-gray-800 p-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <User className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-medium">Dr. Martin Dubois</h3>
              <p className="text-sm text-gray-300">Orthodontiste</p>
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-mono">{formatDuration(callDuration)}</div>
            <div className="text-sm text-gray-300">En cours</div>
          </div>
        </div>

        {/* Zone vidéo */}
        <div className="flex-1 relative">
          {/* Vidéo du docteur */}
          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
            {isVideoOff ? (
              <div className="text-center text-white">
                <VideoOff className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-300">Caméra désactivée</p>
              </div>
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <div className="text-center text-white">
                  <User className="h-24 w-24 mx-auto mb-4" />
                  <p className="text-xl">Dr. Martin Dubois</p>
                </div>
              </div>
            )}
          </div>

          {/* Vidéo du patient (picture-in-picture) */}
          <div className="absolute top-4 right-4 w-48 h-36 bg-gray-700 rounded-lg overflow-hidden border-2 border-white">
            <div className="w-full h-full bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center">
              <User className="h-12 w-12 text-white" />
            </div>
          </div>
        </div>

        {/* Contrôles de l'appel */}
        <div className="bg-gray-800 p-6">
          <div className="flex items-center justify-center space-x-6">
            <button
              onClick={() => setIsMuted(!isMuted)}
              className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                isMuted ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              {isMuted ? (
                <MicOff className="h-6 w-6 text-white" />
              ) : (
                <Mic className="h-6 w-6 text-white" />
              )}
            </button>

            <button
              onClick={() => setIsVideoOff(!isVideoOff)}
              className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                isVideoOff ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              {isVideoOff ? (
                <VideoOff className="h-6 w-6 text-white" />
              ) : (
                <Camera className="h-6 w-6 text-white" />
              )}
            </button>

            <button
              onClick={endCall}
              className="w-14 h-14 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors"
            >
              <PhoneOff className="h-7 w-7 text-white" />
            </button>

            <button className="w-12 h-12 bg-gray-600 hover:bg-gray-700 rounded-full flex items-center justify-center transition-colors">
              <MessageCircle className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Téléconsultation</h1>
        <p className="text-gray-600">Consultez votre orthodontiste à distance</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Rendez-vous à venir */}
        <div className="lg:col-span-2">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Prochains rendez-vous</h2>
          <div className="space-y-4">
            {upcomingAppointments.map((appointment) => (
              <div key={appointment.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{appointment.doctor}</h3>
                      <p className="text-sm text-gray-600">{appointment.type}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(appointment.date).toLocaleDateString('fr-FR')}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Clock className="h-4 w-4" />
                          <span>{appointment.time}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      appointment.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {appointment.status === 'confirmed' ? 'Confirmé' : 'En attente'}
                    </span>
                    <button
                      onClick={startCall}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Video className="h-4 w-4" />
                      <span>Rejoindre</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Test de connexion */}
          <div className="mt-8 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Test de connexion</h3>
            <p className="text-gray-600 mb-4">
              Vérifiez que votre caméra et microphone fonctionnent correctement avant votre consultation.
            </p>
            <div className="flex space-x-4">
              <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <Camera className="h-4 w-4" />
                <span>Tester la caméra</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <Mic className="h-4 w-4" />
                <span>Tester le micro</span>
              </button>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Consultation d'urgence */}
          <div className="bg-red-50 rounded-xl p-6 border border-red-200">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Urgence ?</h3>
            <p className="text-red-700 text-sm mb-4">
              En cas d'urgence orthodontique, contactez immédiatement votre praticien.
            </p>
            <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              <Phone className="h-4 w-4" />
              <span>Appel d'urgence</span>
            </button>
          </div>

          {/* Conseils pour la téléconsultation */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Conseils pour votre consultation</h3>
            <ul className="space-y-3 text-sm text-gray-600">
              <li className="flex items-start space-x-2">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                <span>Assurez-vous d'avoir une connexion internet stable</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                <span>Trouvez un endroit calme et bien éclairé</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                <span>Ayez vos gouttières à portée de main</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                <span>Préparez vos questions à l'avance</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                <span>Testez votre équipement avant la consultation</span>
              </li>
            </ul>
          </div>

          {/* Historique des consultations */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Dernières consultations</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div>
                  <p className="text-sm font-medium text-gray-900">Dr. Martin Dubois</p>
                  <p className="text-xs text-gray-500">28 juin 2024</p>
                </div>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">Terminée</span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div>
                  <p className="text-sm font-medium text-gray-900">Dr. Sophie Laurent</p>
                  <p className="text-xs text-gray-500">15 juin 2024</p>
                </div>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">Terminée</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Teleconsultation;
