import React from 'react';
import { motion } from 'framer-motion';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';
import AdvancedCommunication from '../components/Communication/AdvancedCommunication';

const Teleconsultation: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen surface-primary">
      <div className="max-w-6xl mx-auto h-screen flex flex-col">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-6 pb-0"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl">
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-adaptive">
                {t('communication.title')}
              </h1>
              <p className="text-adaptive-secondary">
                {t('communication.subtitle')}
              </p>
            </div>
          </div>
        </motion.div>

        <div className="flex-1 px-6 pb-6">
          <AdvancedCommunication />
        </div>
      </div>
    </div>
  );
};

export default Teleconsultation;

