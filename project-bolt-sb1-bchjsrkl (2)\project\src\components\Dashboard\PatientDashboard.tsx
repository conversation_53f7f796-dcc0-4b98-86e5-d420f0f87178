import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Calendar, Camera, FileText, MessageSquare, Video, Bell, Trophy, BarChart3, BookOpen, Target } from 'lucide-react';
import { Link } from 'react-router-dom';

const PatientDashboard: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const quickActions = [
    {
      title: 'Suivi quotidien',
      description: 'Marquer le port d\'appareil d\'aujourd\'hui',
      icon: Calendar,
      link: '/patient/suivi',
      color: 'bg-blue-500'
    },
    {
      title: 'Envoyer des photos',
      description: 'Partager vos photos de progression',
      icon: Camera,
      link: '/patient/photos',
      color: 'bg-green-500'
    },
    {
      title: 'Mes récompenses',
      description: 'Voir vos badges et points',
      icon: Trophy,
      link: '/patient/recompenses',
      color: 'bg-yellow-500'
    },
    {
      title: 'Rappels intelligents',
      description: 'Configurer vos notifications',
      icon: Bell,
      link: '/patient/rappels',
      color: 'bg-purple-500'
    },
    {
      title: 'Mes analyses',
      description: 'Voir votre progression détaillée',
      icon: BarChart3,
      link: '/patient/analytics',
      color: 'bg-indigo-500'
    },
    {
      title: 'Contenu éducatif',
      description: 'Apprendre et s\'informer',
      icon: BookOpen,
      link: '/patient/education',
      color: 'bg-orange-500'
    },
    {
      title: 'Dossier médical',
      description: 'Consulter votre dossier',
      icon: FileText,
      link: '/patient/dossier',
      color: 'bg-gray-500'
    },
    {
      title: 'Téléconsultation',
      description: 'Contacter votre praticien',
      icon: Video,
      link: '/teleconsultation',
      color: 'bg-red-500'
    },
    {
      title: 'Forum patients',
      description: 'Échanger avec d\'autres patients',
      icon: MessageSquare,
      link: '/patient/communaute',
      color: 'bg-teal-500'
    }
  ];

  // Simulate some user stats
  const userStats = {
    complianceRate: 87,
    currentStreak: 5,
    totalPoints: 1250,
    level: 3
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 text-white">
        <h1 className="text-3xl font-bold mb-2">
          {t('dashboard.welcome')}, {user?.fullName}!
        </h1>
        <p className="text-blue-100 mb-4">
          Suivez votre progression orthodontique et restez connecté avec votre praticien
        </p>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-blue-700 bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <div>
                <p className="text-sm text-blue-100">Conformité</p>
                <p className="text-lg font-bold">{userStats.complianceRate}%</p>
              </div>
            </div>
          </div>
          <div className="bg-blue-700 bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <div>
                <p className="text-sm text-blue-100">Série actuelle</p>
                <p className="text-lg font-bold">{userStats.currentStreak} jours</p>
              </div>
            </div>
          </div>
          <div className="bg-blue-700 bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Trophy className="h-5 w-5" />
              <div>
                <p className="text-sm text-blue-100">Points</p>
                <p className="text-lg font-bold">{userStats.totalPoints}</p>
              </div>
            </div>
          </div>
          <div className="bg-blue-700 bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <div>
                <p className="text-sm text-blue-100">Niveau</p>
                <p className="text-lg font-bold">{userStats.level}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-100 group"
          >
            <div className="flex items-start space-x-4">
              <div className={`${action.color} p-3 rounded-lg group-hover:scale-110 transition-transform`}>
                <action.icon className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity & Next Appointment */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Progression récente</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Cette semaine</span>
              <span className="text-sm font-medium text-green-600">6/7 jours</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '86%' }}></div>
            </div>
            <p className="text-xs text-gray-500">Excellent travail ! Continuez ainsi.</p>
            
            <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                🎯 <strong>Objectif de la semaine:</strong> Porter l'appareil 22h/jour
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Prochain rendez-vous</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Contrôle mensuel</p>
                <p className="text-sm text-gray-600">15 janvier 2025 à 14h30</p>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                📅 <strong>Rappel:</strong> Préparez vos questions pour le praticien
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Motivational Section */}
      <div className="bg-gradient-to-r from-green-400 to-blue-500 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold mb-2">Continuez comme ça ! 🎉</h3>
            <p className="text-green-100">
              Votre régularité est excellente. Vous êtes sur la bonne voie pour un sourire parfait !
            </p>
          </div>
          <div className="text-right">
            <div className="bg-white bg-opacity-20 rounded-lg p-4">
              <Trophy className="h-8 w-8 text-yellow-300 mx-auto mb-2" />
              <p className="text-sm font-medium">Niveau {userStats.level}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;