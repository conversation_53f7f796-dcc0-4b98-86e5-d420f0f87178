import React from 'react';
import { motion } from 'framer-motion';
import { CephalometricAnalysisComponent } from '../components/CephalometricAnalysis';
import { useLanguage } from '../contexts/LanguageContext';

const CephalometricAnalysisPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900"
    >
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-adaptive-primary dark:text-white mb-4">
            {t('cephalometricAnalysis')}
          </h1>
          <p className="text-adaptive-secondary dark:text-gray-300 text-lg">
            {t('cephalometricAnalysisDescription')}
          </p>
        </div>
        
        <CephalometricAnalysisComponent 
          patientId="current-patient"
          onAnalysisComplete={(analysis) => {
            console.log('Analyse céphalométrique terminée:', analysis);
          }}
        />
      </div>
    </motion.div>
  );
};

export default CephalometricAnalysisPage;
