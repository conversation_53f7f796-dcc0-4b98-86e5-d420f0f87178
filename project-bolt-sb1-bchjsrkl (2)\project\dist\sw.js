const CACHE_NAME = 'orthoprogress-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});

// Notifications push
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'Rappel OrthoProgress',
    icon: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
    badge: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Ouvrir l\'app',
        icon: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('OrthoProgress', options)
  );
});