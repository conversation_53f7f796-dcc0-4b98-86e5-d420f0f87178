import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Video, Phone, MessageSquare, Plus, X, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface Appointment {
  id: string;
  date: string;
  time: string;
  type: 'consultation' | 'suivi' | 'urgence' | 'teleconsultation';
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  practitioner: {
    name: string;
    specialty: string;
  };
  notes?: string;
  location?: string;
  videoLink?: string;
}

interface AppointmentRequest {
  type: 'consultation' | 'suivi' | 'urgence' | 'teleconsultation';
  preferredDate: string;
  preferredTime: string;
  message: string;
  contactMethod: 'phone' | 'video' | 'message';
}

const Appointments: React.FC = () => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [requestData, setRequestData] = useState<AppointmentRequest>({
    type: 'consultation',
    preferredDate: '',
    preferredTime: '',
    message: '',
    contactMethod: 'message'
  });

  // Données simulées
  useEffect(() => {
    const mockAppointments: Appointment[] = [
      {
        id: '1',
        date: '2024-06-15',
        time: '14:30',
        type: 'consultation',
        status: 'confirmed',
        practitioner: {
          name: 'Dr. Martin Dubois',
          specialty: 'Orthodontiste'
        },
        notes: 'Consultation de suivi - Vérification des gouttières',
        location: 'Cabinet Dr. Dubois, 123 Rue de la Santé, Paris'
      },
      {
        id: '2',
        date: '2024-05-20',
        time: '10:00',
        type: 'teleconsultation',
        status: 'completed',
        practitioner: {
          name: 'Dr. Martin Dubois',
          specialty: 'Orthodontiste'
        },
        notes: 'Téléconsultation - Révision du plan de traitement',
        videoLink: 'https://zoom.us/j/123456789'
      },
      {
        id: '3',
        date: '2024-07-01',
        time: '16:00',
        type: 'suivi',
        status: 'pending',
        practitioner: {
          name: 'Dr. Martin Dubois',
          specialty: 'Orthodontiste'
        },
        notes: 'Suivi mensuel - Changement de gouttières'
      }
    ];

    setAppointments(mockAppointments);
    setLoading(false);
  }, []);

  const handleRequestAppointment = async () => {
    try {
      const newRequest: Appointment = {
        id: Date.now().toString(),
        date: requestData.preferredDate,
        time: requestData.preferredTime,
        type: requestData.type,
        status: 'pending',
        practitioner: {
          name: 'Dr. Martin Dubois',
          specialty: 'Orthodontiste'
        },
        notes: requestData.message
      };

      setAppointments(prev => [newRequest, ...prev]);
      setShowRequestModal(false);
      setRequestData({
        type: 'consultation',
        preferredDate: '',
        preferredTime: '',
        message: '',
        contactMethod: 'message'
      });
    } catch (error) {
      console.error('Erreur lors de la demande de rendez-vous:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmé';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulé';
      case 'completed':
        return 'Terminé';
      default:
        return status;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'teleconsultation':
        return <Video className="w-5 h-5" />;
      case 'urgence':
        return <AlertCircle className="w-5 h-5" />;
      default:
        return <Calendar className="w-5 h-5" />;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'consultation':
        return 'Consultation';
      case 'suivi':
        return 'Suivi';
      case 'urgence':
        return 'Urgence';
      case 'teleconsultation':
        return 'Téléconsultation';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Rendez-vous</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Gérez vos consultations et demandes de rendez-vous
            </p>
          </div>
          <button
            onClick={() => setShowRequestModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Demander un rendez-vous
          </button>
        </div>
      </div>

      {/* Informations de contact du cabinet */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Informations du cabinet
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <Phone className="w-5 h-5 text-blue-500 mr-3" />
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Téléphone</div>
              <div className="font-medium text-gray-900 dark:text-white">01 23 45 67 89</div>
            </div>
          </div>
          <div className="flex items-center">
            <MessageSquare className="w-5 h-5 text-green-500 mr-3" />
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Email</div>
              <div className="font-medium text-gray-900 dark:text-white"><EMAIL></div>
            </div>
          </div>
          <div className="flex items-center">
            <Video className="w-5 h-5 text-purple-500 mr-3" />
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Téléconsultation</div>
              <div className="font-medium text-gray-900 dark:text-white">Disponible sur demande</div>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des rendez-vous */}
      <div className="space-y-4">
        {appointments.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 text-center">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Aucun rendez-vous
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Vous n'avez pas encore de rendez-vous programmé
            </p>
            <button
              onClick={() => setShowRequestModal(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-5 h-5 mr-2" />
              Demander un rendez-vous
            </button>
          </div>
        ) : (
          appointments.map(appointment => (
            <div
              key={appointment.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-blue-600 dark:text-blue-400 mr-4">
                    {getTypeIcon(appointment.type)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {getTypeText(appointment.type)}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {appointment.practitioner.name} - {appointment.practitioner.specialty}
                    </p>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                  {getStatusText(appointment.status)}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <Calendar className="w-4 h-4 mr-2" />
                  {new Date(appointment.date).toLocaleDateString('fr-FR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <Clock className="w-4 h-4 mr-2" />
                  {appointment.time}
                </div>
              </div>

              {appointment.notes && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Notes
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {appointment.notes}
                  </p>
                </div>
              )}

              {appointment.location && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Lieu
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {appointment.location}
                  </p>
                </div>
              )}

              {appointment.videoLink && appointment.status === 'confirmed' && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-600">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Lien de téléconsultation disponible
                  </span>
                  <button
                    onClick={() => window.open(appointment.videoLink, '_blank')}
                    className="flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                  >
                    <Video className="w-4 h-4 mr-2" />
                    Rejoindre
                  </button>
                </div>
              )}

              {appointment.status === 'pending' && (
                <div className="flex items-center justify-end pt-4 border-t border-gray-100 dark:border-gray-600">
                  <span className="text-sm text-yellow-600 dark:text-yellow-400 mr-4">
                    En attente de confirmation
                  </span>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Modal de demande de rendez-vous */}
      {showRequestModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Demander un rendez-vous
              </h3>
              <button
                onClick={() => setShowRequestModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Type de rendez-vous */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Type de rendez-vous
                </label>
                <select
                  value={requestData.type}
                  onChange={(e) => setRequestData(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="consultation">Consultation</option>
                  <option value="suivi">Suivi</option>
                  <option value="urgence">Urgence</option>
                  <option value="teleconsultation">Téléconsultation</option>
                </select>
              </div>

              {/* Date préférée */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date préférée
                </label>
                <input
                  type="date"
                  value={requestData.preferredDate}
                  onChange={(e) => setRequestData(prev => ({ ...prev, preferredDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Heure préférée */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Heure préférée
                </label>
                <input
                  type="time"
                  value={requestData.preferredTime}
                  onChange={(e) => setRequestData(prev => ({ ...prev, preferredTime: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Méthode de contact */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Méthode de contact préférée
                </label>
                <div className="grid grid-cols-3 gap-3">
                  <button
                    type="button"
                    onClick={() => setRequestData(prev => ({ ...prev, contactMethod: 'phone' }))}
                    className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                      requestData.contactMethod === 'phone'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                  >
                    <Phone className="w-5 h-5 mx-auto mb-1" />
                    <div className="text-xs">Téléphone</div>
                  </button>
                  <button
                    type="button"
                    onClick={() => setRequestData(prev => ({ ...prev, contactMethod: 'video' }))}
                    className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                      requestData.contactMethod === 'video'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                  >
                    <Video className="w-5 h-5 mx-auto mb-1" />
                    <div className="text-xs">Vidéo</div>
                  </button>
                  <button
                    type="button"
                    onClick={() => setRequestData(prev => ({ ...prev, contactMethod: 'message' }))}
                    className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                      requestData.contactMethod === 'message'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                  >
                    <MessageSquare className="w-5 h-5 mx-auto mb-1" />
                    <div className="text-xs">Message</div>
                  </button>
                </div>
              </div>

              {/* Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Message (optionnel)
                </label>
                <textarea
                  value={requestData.message}
                  onChange={(e) => setRequestData(prev => ({ ...prev, message: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  rows={3}
                  placeholder="Décrivez la raison de votre demande de rendez-vous..."
                />
              </div>

              {/* Boutons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowRequestModal(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={handleRequestAppointment}
                  disabled={!requestData.preferredDate || !requestData.preferredTime}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Envoyer la demande
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Appointments;
