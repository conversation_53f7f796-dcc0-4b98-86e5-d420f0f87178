interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  birthDate: string;
  avatar: string;
}

interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  birthDate?: string;
}

interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

class ProfileService {
  private baseUrl = '/api/profile';

  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération du profil');
      }

      return await response.json();
    } catch (error) {
      console.error('Erreur getUserProfile:', error);
      // Retourner des données mock en cas d'erreur
      return {
        id: userId,
        firstName: 'Marie',
        lastName: 'Dupont',
        email: '<EMAIL>',
        phone: '+33 6 12 34 56 78',
        address: '123 rue de Paris',
        city: 'Paris',
        postalCode: '75001',
        birthDate: '1990-01-01',
        avatar: '/default-avatar.jpg'
      };
    }
  }

  async updateProfile(userId: string, data: UpdateProfileData): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la mise à jour du profil');
      }

      const updatedProfile = await response.json();
      
      // Simuler une notification de succès
      this.showNotification('Profil mis à jour avec succès', 'success');
      
      return updatedProfile;
    } catch (error) {
      console.error('Erreur updateProfile:', error);
      this.showNotification('Erreur lors de la mise à jour du profil', 'error');
      throw error;
    }
  }

  async uploadAvatar(userId: string, file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch(`${this.baseUrl}/${userId}/avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Erreur lors du téléchargement de l\'avatar');
      }

      const result = await response.json();
      this.showNotification('Photo de profil mise à jour', 'success');
      
      return result.avatarUrl;
    } catch (error) {
      console.error('Erreur uploadAvatar:', error);
      this.showNotification('Erreur lors du téléchargement de la photo', 'error');
      throw error;
    }
  }

  async changePassword(userId: string, data: ChangePasswordData): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${userId}/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors du changement de mot de passe');
      }

      this.showNotification('Mot de passe modifié avec succès', 'success');
    } catch (error) {
      console.error('Erreur changePassword:', error);
      this.showNotification('Erreur lors du changement de mot de passe', 'error');
      throw error;
    }
  }

  async validateCurrentPassword(userId: string, password: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/${userId}/validate-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ password })
      });

      return response.ok;
    } catch (error) {
      console.error('Erreur validateCurrentPassword:', error);
      return false;
    }
  }

  private showNotification(message: string, type: 'success' | 'error') {
    // Créer une notification temporaire
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
      type === 'success' 
        ? 'bg-green-500 text-white' 
        : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Supprimer la notification après 3 secondes
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}

export const profileService = new ProfileService();
export type { UserProfile, UpdateProfileData, ChangePasswordData };
