/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/utils/generic_utils" />
import { DataType, fused, serialization } from '@tensorflow/tfjs-core';
/**
 * If `value` is an Array, equivalent to Python's `value * numValues`.
 * If `value` is not an Array, equivalent to Python's `[value] * numValues`
 */
export declare function pyListRepeat(value: any, numValues: number): any[];
export declare function assert(val: boolean, message?: string): void;
/**
 * Count the number of elements of the `array` that are equal to `reference`.
 */
export declare function count<T>(array: T[], refernce: T): number;
/**
 * If an array is of length 1, just return the first element. Otherwise, return
 * the full array.
 * @param tensors
 */
export declare function singletonOrArray<T>(xs: T[]): T | T[];
/**
 * Normalizes a list/tensor into a list.
 *
 * If a tensor is passed, we return
 * a list of size 1 containing the tensor.
 *
 * @param x target object to be normalized.
 */
export declare function toList<T>(x: T | T[]): T[];
/**
 * Generate a UID for a list
 */
export declare function objectListUid(objs: any | any[]): string;
/**
 * Converts string to snake-case.
 * @param name
 */
export declare function toSnakeCase(name: string): string;
export declare function toCamelCase(identifier: string): string;
export declare function serializeKerasObject(instance: serialization.Serializable): serialization.ConfigDictValue;
/**
 * Deserialize a saved Keras Object
 * @param identifier either a string ID or a saved Keras dictionary
 * @param moduleObjects a list of Python class names to object constructors
 * @param customObjects a list of Python class names to object constructors
 * @param printableModuleName debug text for the object being reconstituted
 * @param fastWeightInit Optional flag to use fast weight initialization
 *   during deserialization. This is applicable to cases in which
 *   the initialization will be immediately overwritten by loaded weight
 *   values. Default: `false`.
 * @returns a TensorFlow.js Layers object
 */
export declare function deserializeKerasObject(identifier: string | serialization.ConfigDict, moduleObjects?: {
    [objName: string]: any;
}, customObjects?: {
    [objName: string]: any;
}, printableModuleName?: string, fastWeightInit?: boolean): any;
/**
 * Compares two numbers for sorting.
 * @param a
 * @param b
 */
export declare function numberCompare(a: number, b: number): 0 | 1 | -1;
/**
 * Comparison of two numbers for reverse sorting.
 * @param a
 * @param b
 */
export declare function reverseNumberCompare(a: number, b: number): number;
/**
 * Convert a string into the corresponding DType.
 * @param dtype
 * @returns An instance of DType.
 */
export declare function stringToDType(dtype: string): DataType;
/**
 * Test the element-by-element equality of two Arrays of strings.
 * @param xs First array of strings.
 * @param ys Second array of strings.
 * @returns Wether the two arrays are all equal, element by element.
 */
export declare function stringsEqual(xs: string[], ys: string[]): boolean;
/**
 * Get the unique elements of an array.
 * @param xs Array.
 * @returns An Array consisting of the unique elements in `xs`.
 */
export declare function unique<T>(xs: T[]): T[];
/**
 * Determine if an Object is empty (i.e., does not have own properties).
 * @param obj Object
 * @returns Whether the Object is empty.
 * @throws ValueError: If object is `null` or `undefined`.
 */
export declare function isObjectEmpty(obj: {}): boolean;
/**
 * Helper function used to build type union/enum run-time checkers.
 * @param values The list of allowed values.
 * @param label A string name for the type
 * @param value The value to test.
 * @throws ValueError: If the value is not in values nor `undefined`/`null`.
 */
export declare function checkStringTypeUnionValue(values: string[], label: string, value: string): void;
/**
 * Helper function for verifying the types of inputs.
 *
 * Ensures that the elements of `x` are all of type `expectedType`.
 * Also verifies that the length of `x` is within bounds.
 *
 * @param x Object to test.
 * @param expectedType The string expected type of all of the elements in the
 * Array.
 * @param minLength Return false if x.length is less than this.
 * @param maxLength Return false if x.length is greater than this.
 * @returns true if and only if `x` is an `Array<expectedType>` with
 * length >= `minLength` and <= `maxLength`.
 */
export declare function checkArrayTypeAndLength(x: any, expectedType: string, minLength?: number, maxLength?: number): boolean;
/**
 * Assert that a value or an array of value are positive integer.
 *
 * @param value The value being asserted on. May be a single number or an array
 *   of numbers.
 * @param name Name of the value, used to make the error message.
 */
export declare function assertPositiveInteger(value: number | number[], name: string): void;
/**
 * Format a value into a display-friendly, human-readable fashion.
 *
 * - `null` is formatted as `'null'`
 * - Strings are formated with flanking pair of quotes.
 * - Arrays are formatted with flanking pair of square brackets.
 *
 * @param value The value to display.
 * @return Formatted string.
 */
export declare function formatAsFriendlyString(value: any): string;
/**
 * Returns a function `f2` (decorator) which wraps the original function
 * `f`. `f2` guarantees that `f` can be called at most once
 * every `waitMs` ms. If `f2` is called more often, it will return
 * the last returned result of `f`.
 *
 * @param f The original function `f` to wrap.
 * @param waitMs The time between two consecutive calls to `f` in ms.
 */
export declare function debounce<T>(f: (...args: Array<{}>) => T, waitMs: number, nowFunc?: Function): (...args: Array<{}>) => T;
/**
 * Returns the fusable activation given a layers identifier.
 *
 * @param activationName The layers identifier string.
 * @return The name of the fusable activation.
 */
export declare function mapActivationToFusedKernel(activationName: string): fused.Activation;
type PossibleValues = Array<Array<boolean | string | number>>;
/**
 * Returns the cartesian product of sets of values.
 * This works the same as itertools.product in Python.
 *
 * Example:
 *
 * filters = [128, 256, 512]
 * paddings = ['same', 'valid']
 *
 * product = [ [128, 'same'], [128, 'valid'], [256, 'same'], [256, 'valid'],
 * [512, 'same'], [512, 'valid']]
 *
 * @param arrayOfValues List/array of values.
 * @return The cartesian product.
 */
export declare function getCartesianProductOfValues(...arrayOfValues: PossibleValues): PossibleValues;
export {};
