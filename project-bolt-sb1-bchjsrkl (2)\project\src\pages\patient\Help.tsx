import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Search, HelpCircle, MessageCircle, Phone, Mail, Book, Video, FileText, ChevronDown, ChevronRight } from 'lucide-react';

const Help: React.FC = () => {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqData = [
    {
      id: 1,
      question: 'Comment porter correctement mes gouttières ?',
      answer: 'Portez vos gouttières 20-22 heures par jour. Retirez-les uniquement pour manger, boire (sauf de l\'eau) et vous brosser les dents. Assurez-vous qu\'elles s\'ajustent bien en appuyant doucement avec vos doigts.'
    },
    {
      id: 2,
      question: 'Que faire si ma gouttière me fait mal ?',
      answer: 'Un léger inconfort est normal les premiers jours. Si la douleur persiste ou s\'aggrave, contactez votre orthodontiste. Vous pouvez prendre un antalgique léger si nécessaire.'
    },
    {
      id: 3,
      question: 'Comment nettoyer mes gouttières ?',
      answer: 'Rincez vos gouttières à l\'eau tiède et brossez-les délicatement avec une brosse à dents souple. Évitez l\'eau chaude qui pourrait les déformer. Utilisez les pastilles de nettoyage recommandées.'
    },
    {
      id: 4,
      question: 'Puis-je manger avec mes gouttières ?',
      answer: 'Non, retirez toujours vos gouttières avant de manger ou boire autre chose que de l\'eau. Cela évite les taches et les dommages.'
    },
    {
      id: 5,
      question: 'Que faire si je perds une gouttière ?',
      answer: 'Contactez immédiatement votre orthodontiste. En attendant, portez la gouttière précédente si vous l\'avez encore, ou passez à la suivante selon les instructions de votre praticien.'
    },
    {
      id: 6,
      question: 'À quelle fréquence dois-je changer de gouttière ?',
      answer: 'Généralement tous les 7-14 jours selon votre plan de traitement. Suivez strictement les instructions de votre orthodontiste et ne changez jamais de gouttière sans son accord.'
    }
  ];

  const contactOptions = [
    {
      icon: Phone,
      title: 'Urgence orthodontique',
      description: 'Appelez en cas d\'urgence',
      contact: '+33 1 23 45 67 89',
      available: '24h/24, 7j/7'
    },
    {
      icon: MessageCircle,
      title: 'Chat en direct',
      description: 'Discutez avec notre équipe',
      contact: 'Démarrer une conversation',
      available: 'Lun-Ven 9h-18h'
    },
    {
      icon: Mail,
      title: 'Email',
      description: 'Envoyez-nous un message',
      contact: '<EMAIL>',
      available: 'Réponse sous 24h'
    }
  ];

  const resources = [
    {
      icon: Video,
      title: 'Tutoriels vidéo',
      description: 'Apprenez les bonnes pratiques',
      items: ['Port des gouttières', 'Nettoyage', 'Changement', 'Hygiène dentaire']
    },
    {
      icon: Book,
      title: 'Guide du patient',
      description: 'Manuel complet du traitement',
      items: ['Étapes du traitement', 'Conseils pratiques', 'FAQ détaillée', 'Troubleshooting']
    },
    {
      icon: FileText,
      title: 'Documents',
      description: 'Vos documents de traitement',
      items: ['Plan de traitement', 'Ordonnances', 'Certificats', 'Rapports de suivi']
    }
  ];

  const filteredFaq = faqData.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-adaptive mb-2">Centre d'aide</h1>
        <p className="text-adaptive-secondary">Trouvez rapidement les réponses à vos questions</p>
      </div>

      {/* Barre de recherche */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Rechercher dans l'aide..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* FAQ */}
        <div className="lg:col-span-2">
          <h2 className="text-2xl font-bold text-adaptive mb-6">Questions fréquentes</h2>
          <div className="space-y-4">
            {filteredFaq.map((faq) => (
              <div key={faq.id} className="surface-primary rounded-lg border border-adaptive overflow-hidden">
                <button
                  onClick={() => setExpandedFaq(expandedFaq === faq.id ? null : faq.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:surface-secondary transition-colors"
                >
                  <span className="font-medium text-adaptive">{faq.question}</span>
                  {expandedFaq === faq.id ? (
                    <ChevronDown className="h-5 w-5 text-adaptive-tertiary" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-adaptive-tertiary" />
                  )}
                </button>
                {expandedFaq === faq.id && (
                  <div className="px-6 pb-4">
                    <p className="text-adaptive-secondary leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredFaq.length === 0 && (
            <div className="text-center py-8">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-adaptive-tertiary">Aucun résultat trouvé pour votre recherche.</p>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact */}
          <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
            <h3 className="text-lg font-semibold text-adaptive mb-4">Nous contacter</h3>
            <div className="space-y-4">
              {contactOptions.map((option, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <option.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-adaptive">{option.title}</h4>
                    <p className="text-sm text-adaptive-secondary mb-1">{option.description}</p>
                    <p className="text-sm font-medium text-blue-600">{option.contact}</p>
                    <p className="text-xs text-adaptive-tertiary">{option.available}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Ressources */}
          <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
            <h3 className="text-lg font-semibold text-adaptive mb-4">Ressources utiles</h3>
            <div className="space-y-4">
              {resources.map((resource, index) => (
                <div key={index} className="border border-adaptive rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="surface-tertiary p-2 rounded-lg">
                      <resource.icon className="h-5 w-5 text-adaptive-secondary" />
                    </div>
                    <div>
                      <h4 className="font-medium text-adaptive">{resource.title}</h4>
                      <p className="text-sm text-adaptive-secondary">{resource.description}</p>
                    </div>
                  </div>
                  <ul className="space-y-1">
                    {resource.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="text-sm text-adaptive-secondary flex items-center">
                        <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Help;
