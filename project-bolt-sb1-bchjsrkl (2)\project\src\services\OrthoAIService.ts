import * as tf from '@tensorflow/tfjs';
import { apiService } from './apiService';

// Types pour l'IA orthodontique
export interface ToothAnalysis {
  toothNumber: number;
  position: { x: number; y: number; z?: number };
  rotation: { x: number; y: number; z: number };
  movement: { distance: number; direction: string };
  health: 'excellent' | 'good' | 'fair' | 'poor';
  issues: string[];
}

export interface ProgressAnalysis {
  overallProgress: number; // 0-100%
  estimatedCompletion: Date;
  currentPhase: string;
  nextMilestone: string;
  complianceScore: number;
  recommendations: string[];
  teeth: ToothAnalysis[];
  riskFactors: string[];
}

export interface PhotoQuality {
  score: number; // 0-100
  issues: string[];
  suggestions: string[];
  usableForAnalysis: boolean;
}

export interface PredictiveInsights {
  treatmentDuration: {
    estimated: number; // en mois
    confidence: number;
    factors: string[];
  };
  complianceRisk: {
    score: number; // 0-100 (100 = risque élevé)
    factors: string[];
    interventions: string[];
  };
  complications: {
    probability: number;
    types: string[];
    preventiveMeasures: string[];
  };
}

export interface AIConfig {
  modelVersion: string;
  confidenceThreshold: number;
  enableRealTimeAnalysis: boolean;
  enablePredictiveAnalytics: boolean;
  enableAutoAlerts: boolean;
}

class AIService {
  private models: Map<string, tf.LayersModel> = new Map();
  private isInitialized = false;
  private config: AIConfig = {
    modelVersion: '2.1.0',
    confidenceThreshold: 0.85,
    enableRealTimeAnalysis: true,
    enablePredictiveAnalytics: true,
    enableAutoAlerts: true,
  };

  // Initialisation des modèles IA
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🧠 Initialisation des modèles IA...');

      // Charger les modèles pré-entraînés
      await Promise.all([
        this.loadToothDetectionModel(),
        this.loadProgressAnalysisModel(),
        this.loadQualityAssessmentModel(),
        this.loadPredictiveModel(),
      ]);

      this.isInitialized = true;
      console.log('✅ Modèles IA initialisés avec succès');
    } catch (error) {
      console.error('❌ Erreur initialisation IA:', error);
      throw new Error('Impossible d\'initialiser les modèles IA');
    }
  }

  // Analyse complète d'une photo orthodontique
  async analyzePhoto(
    imageData: ImageData | HTMLImageElement | string,
    patientId: string,
    photoType: 'intraoral' | 'extraoral' | 'cephalometric'
  ): Promise<{
    quality: PhotoQuality;
    analysis: ProgressAnalysis;
    insights: PredictiveInsights;
  }> {
    await this.ensureInitialized();

    try {
      // 1. Préprocessing de l'image
      const preprocessedImage = await this.preprocessImage(imageData);

      // 2. Évaluation de la qualité
      const quality = await this.assessPhotoQuality(preprocessedImage, photoType);

      if (!quality.usableForAnalysis) {
        throw new Error('Photo de qualité insuffisante pour l\'analyse');
      }

      // 3. Détection et analyse des dents
      const teethDetection = await this.detectTeeth(preprocessedImage);
      const progressAnalysis = await this.analyzeProgress(teethDetection, patientId);

      // 4. Insights prédictifs
      const insights = await this.generatePredictiveInsights(progressAnalysis, patientId);

      // 5. Sauvegarde des résultats
      await this.saveAnalysisResults(patientId, {
        quality,
        analysis: progressAnalysis,
        insights,
        timestamp: new Date(),
      });

      return { quality, analysis: progressAnalysis, insights };
    } catch (error) {
      console.error('Erreur analyse photo:', error);
      throw error;
    }
  }

  // Analyse de la qualité des photos
  private async assessPhotoQuality(
    image: tf.Tensor,
    photoType: string
  ): Promise<PhotoQuality> {
    const qualityModel = this.models.get('quality_assessment');
    if (!qualityModel) throw new Error('Modèle qualité non chargé');

    const prediction = qualityModel.predict(image) as tf.Tensor;
    const scores = await prediction.data();

    const overallScore = scores[0] * 100;
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Analyse des critères de qualité
    if (scores[1] < 0.7) {
      issues.push('Éclairage insuffisant');
      suggestions.push('Améliorer l\'éclairage de la prise de vue');
    }

    if (scores[2] < 0.8) {
      issues.push('Image floue');
      suggestions.push('Stabiliser l\'appareil photo et faire la mise au point');
    }

    if (scores[3] < 0.6) {
      issues.push('Angle de prise de vue incorrect');
      suggestions.push(`Respecter l\'angle standard pour les photos ${photoType}`);
    }

    if (scores[4] < 0.7) {
      issues.push('Contraste insuffisant');
      suggestions.push('Ajuster les paramètres de contraste');
    }

    return {
      score: Math.round(overallScore),
      issues,
      suggestions,
      usableForAnalysis: overallScore >= this.config.confidenceThreshold * 100,
    };
  }

  // Détection et segmentation des dents
  private async detectTeeth(image: tf.Tensor): Promise<ToothAnalysis[]> {
    const detectionModel = this.models.get('tooth_detection');
    if (!detectionModel) throw new Error('Modèle détection non chargé');

    const predictions = detectionModel.predict(image) as tf.Tensor;
    const results = await predictions.data();

    const teeth: ToothAnalysis[] = [];
    
    // Traitement des résultats (32 dents maximum)
    for (let i = 0; i < 32; i++) {
      const baseIndex = i * 10; // 10 valeurs par dent
      
      if (results[baseIndex] > this.config.confidenceThreshold) {
        const tooth: ToothAnalysis = {
          toothNumber: i + 1,
          position: {
            x: results[baseIndex + 1],
            y: results[baseIndex + 2],
            z: results[baseIndex + 3],
          },
          rotation: {
            x: results[baseIndex + 4],
            y: results[baseIndex + 5],
            z: results[baseIndex + 6],
          },
          movement: {
            distance: results[baseIndex + 7],
            direction: this.getMovementDirection(results[baseIndex + 8]),
          },
          health: this.assessToothHealth(results[baseIndex + 9]),
          issues: this.detectToothIssues(results.slice(baseIndex, baseIndex + 10)),
        };

        teeth.push(tooth);
      }
    }

    return teeth;
  }

  // Analyse du progrès du traitement
  private async analyzeProgress(
    teeth: ToothAnalysis[],
    patientId: string
  ): Promise<ProgressAnalysis> {
    const progressModel = this.models.get('progress_analysis');
    if (!progressModel) throw new Error('Modèle progrès non chargé');

    // Récupérer l'historique du patient
    const history = await this.getPatientHistory(patientId);
    
    // Préparer les données pour le modèle
    const inputData = this.prepareProgressData(teeth, history);
    const inputTensor = tf.tensor2d([inputData]);

    const prediction = progressModel.predict(inputTensor) as tf.Tensor;
    const results = await prediction.data();

    const overallProgress = Math.round(results[0] * 100);
    const estimatedDaysRemaining = Math.round(results[1]);
    const complianceScore = Math.round(results[2] * 100);

    return {
      overallProgress,
      estimatedCompletion: new Date(Date.now() + estimatedDaysRemaining * 24 * 60 * 60 * 1000),
      currentPhase: this.determineCurrentPhase(overallProgress),
      nextMilestone: this.getNextMilestone(overallProgress),
      complianceScore,
      recommendations: this.generateRecommendations(teeth, complianceScore),
      teeth,
      riskFactors: this.identifyRiskFactors(teeth, complianceScore),
    };
  }

  // Génération d'insights prédictifs
  private async generatePredictiveInsights(
    analysis: ProgressAnalysis,
    patientId: string
  ): Promise<PredictiveInsights> {
    const predictiveModel = this.models.get('predictive_analytics');
    if (!predictiveModel) throw new Error('Modèle prédictif non chargé');

    const patientData = await this.getPatientData(patientId);
    const inputData = this.preparePredictiveData(analysis, patientData);
    const inputTensor = tf.tensor2d([inputData]);

    const prediction = predictiveModel.predict(inputTensor) as tf.Tensor;
    const results = await prediction.data();

    return {
      treatmentDuration: {
        estimated: Math.round(results[0]),
        confidence: Math.round(results[1] * 100),
        factors: this.getTreatmentFactors(results.slice(2, 7)),
      },
      complianceRisk: {
        score: Math.round(results[7] * 100),
        factors: this.getComplianceFactors(results.slice(8, 13)),
        interventions: this.getRecommendedInterventions(results[7]),
      },
      complications: {
        probability: Math.round(results[13] * 100),
        types: this.getComplicationTypes(results.slice(14, 19)),
        preventiveMeasures: this.getPreventiveMeasures(results.slice(14, 19)),
      },
    };
  }

  // Analyse en temps réel pendant la prise de photo
  async analyzeRealTime(
    videoStream: MediaStream,
    onAnalysis: (analysis: Partial<ProgressAnalysis>) => void
  ): Promise<void> {
    if (!this.config.enableRealTimeAnalysis) return;

    const video = document.createElement('video');
    video.srcObject = videoStream;
    video.play();

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    const analyzeFrame = async () => {
      if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        try {
          const quickAnalysis = await this.quickAnalyze(imageData);
          onAnalysis(quickAnalysis);
        } catch (error) {
          console.warn('Erreur analyse temps réel:', error);
        }
      }

      requestAnimationFrame(analyzeFrame);
    };

    analyzeFrame();
  }

  // Analyse rapide pour le temps réel
  private async quickAnalyze(imageData: ImageData): Promise<Partial<ProgressAnalysis>> {
    const preprocessed = await this.preprocessImage(imageData);
    const quality = await this.assessPhotoQuality(preprocessed, 'intraoral');

    return {
      overallProgress: quality.score,
      recommendations: quality.suggestions,
    };
  }

  // Chargement des modèles
  private async loadToothDetectionModel(): Promise<void> {
    const modelUrl = '/models/tooth_detection/model.json';
    const model = await tf.loadLayersModel(modelUrl);
    this.models.set('tooth_detection', model);
  }

  private async loadProgressAnalysisModel(): Promise<void> {
    const modelUrl = '/models/progress_analysis/model.json';
    const model = await tf.loadLayersModel(modelUrl);
    this.models.set('progress_analysis', model);
  }

  private async loadQualityAssessmentModel(): Promise<void> {
    const modelUrl = '/models/quality_assessment/model.json';
    const model = await tf.loadLayersModel(modelUrl);
    this.models.set('quality_assessment', model);
  }

  private async loadPredictiveModel(): Promise<void> {
    const modelUrl = '/models/predictive_analytics/model.json';
    const model = await tf.loadLayersModel(modelUrl);
    this.models.set('predictive_analytics', model);
  }

  // Utilitaires
  private async preprocessImage(imageData: ImageData | HTMLImageElement | string): Promise<tf.Tensor> {
    let tensor: tf.Tensor;

    if (typeof imageData === 'string') {
      // URL d'image
      const img = new Image();
      img.src = imageData;
      await new Promise(resolve => img.onload = resolve);
      tensor = tf.browser.fromPixels(img);
    } else if (imageData instanceof HTMLImageElement) {
      tensor = tf.browser.fromPixels(imageData);
    } else {
      // ImageData
      tensor = tf.browser.fromPixels(imageData);
    }

    // Normalisation et redimensionnement
    return tensor
      .resizeNearestNeighbor([224, 224])
      .expandDims(0)
      .div(255.0);
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  private getMovementDirection(value: number): string {
    const directions = ['mesial', 'distal', 'buccal', 'lingual', 'intrusion', 'extrusion'];
    return directions[Math.floor(value * directions.length)] || 'stable';
  }

  private assessToothHealth(score: number): 'excellent' | 'good' | 'fair' | 'poor' {
    if (score > 0.9) return 'excellent';
    if (score > 0.7) return 'good';
    if (score > 0.5) return 'fair';
    return 'poor';
  }

  private detectToothIssues(values: number[]): string[] {
    const issues: string[] = [];
    if (values[5] < 0.5) issues.push('Plaque dentaire détectée');
    if (values[6] < 0.5) issues.push('Inflammation gingivale');
    if (values[7] < 0.5) issues.push('Carie suspectée');
    return issues;
  }

  private determineCurrentPhase(progress: number): string {
    if (progress < 25) return 'Phase d\'alignement initial';
    if (progress < 50) return 'Phase de correction principale';
    if (progress < 75) return 'Phase de finition';
    return 'Phase de contention';
  }

  private getNextMilestone(progress: number): string {
    if (progress < 25) return 'Alignement des incisives';
    if (progress < 50) return 'Correction des canines';
    if (progress < 75) return 'Ajustement de l\'occlusion';
    return 'Stabilisation finale';
  }

  private generateRecommendations(teeth: ToothAnalysis[], compliance: number): string[] {
    const recommendations: string[] = [];
    
    if (compliance < 70) {
      recommendations.push('Améliorer le port des aligneurs (minimum 22h/jour)');
    }
    
    const problematicTeeth = teeth.filter(t => t.health === 'poor' || t.issues.length > 0);
    if (problematicTeeth.length > 0) {
      recommendations.push('Attention particulière aux dents avec problèmes détectés');
    }
    
    return recommendations;
  }

  private identifyRiskFactors(teeth: ToothAnalysis[], compliance: number): string[] {
    const risks: string[] = [];
    
    if (compliance < 60) risks.push('Compliance insuffisante');
    if (teeth.some(t => t.issues.includes('Inflammation gingivale'))) {
      risks.push('Problèmes gingivaux');
    }
    
    return risks;
  }

  // Méthodes de données (à implémenter avec l'API)
  private async getPatientHistory(patientId: string): Promise<any[]> {
    try {
      const response = await apiService.get(`/patients/${patientId}/history`);
      return response.data || [];
    } catch (error) {
      console.warn('Impossible de récupérer l\'historique patient');
      return [];
    }
  }

  private async getPatientData(patientId: string): Promise<any> {
    try {
      const response = await apiService.get(`/patients/${patientId}`);
      return response.data || {};
    } catch (error) {
      console.warn('Impossible de récupérer les données patient');
      return {};
    }
  }

  private prepareProgressData(teeth: ToothAnalysis[], history: any[]): number[] {
    // Convertir les données en format numérique pour le modèle
    const data: number[] = [];
    
    // Ajouter les métriques des dents
    teeth.forEach(tooth => {
      data.push(
        tooth.position.x,
        tooth.position.y,
        tooth.rotation.z,
        tooth.movement.distance
      );
    });
    
    // Compléter avec des zéros si nécessaire
    while (data.length < 128) data.push(0);
    
    return data.slice(0, 128);
  }

  private preparePredictiveData(analysis: ProgressAnalysis, patientData: any): number[] {
    return [
      analysis.overallProgress / 100,
      analysis.complianceScore / 100,
      analysis.teeth.length,
      analysis.riskFactors.length,
      patientData.age || 25,
      patientData.treatmentType === 'comprehensive' ? 1 : 0,
      // ... autres features
    ];
  }

  private getTreatmentFactors(values: number[]): string[] {
    const factors = ['Complexité du cas', 'Compliance patient', 'Âge', 'Type de traitement', 'Historique médical'];
    return factors.filter((_, i) => values[i] > 0.5);
  }

  private getComplianceFactors(values: number[]): string[] {
    const factors = ['Motivation', 'Âge', 'Style de vie', 'Support familial', 'Expérience précédente'];
    return factors.filter((_, i) => values[i] > 0.5);
  }

  private getRecommendedInterventions(riskScore: number): string[] {
    const interventions: string[] = [];
    
    if (riskScore > 0.7) {
      interventions.push('Suivi rapproché recommandé');
      interventions.push('Rappels automatiques renforcés');
    }
    
    if (riskScore > 0.5) {
      interventions.push('Éducation patient supplémentaire');
    }
    
    return interventions;
  }

  private getComplicationTypes(values: number[]): string[] {
    const types = ['Résorption radiculaire', 'Problèmes gingivaux', 'Récidive', 'Inconfort', 'Allergie'];
    return types.filter((_, i) => values[i] > 0.3);
  }

  private getPreventiveMeasures(values: number[]): string[] {
    const measures: string[] = [];
    
    if (values[0] > 0.3) measures.push('Surveillance radiographique');
    if (values[1] > 0.3) measures.push('Hygiène renforcée');
    if (values[2] > 0.3) measures.push('Contention prolongée');
    
    return measures;
  }

  private async saveAnalysisResults(patientId: string, results: any): Promise<void> {
    try {
      await apiService.post(`/patients/${patientId}/ai-analysis`, results);
    } catch (error) {
      console.warn('Impossible de sauvegarder les résultats IA');
    }
  }

  // Configuration publique
  getConfig(): AIConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<AIConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Nettoyage des ressources
  dispose(): void {
    this.models.forEach(model => model.dispose());
    this.models.clear();
    this.isInitialized = false;
  }
}

export const aiService = new AIService();
