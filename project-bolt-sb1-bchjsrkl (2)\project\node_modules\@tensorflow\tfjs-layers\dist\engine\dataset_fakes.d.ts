/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/engine/dataset_fakes" />
import * as tfc from '@tensorflow/tfjs-core';
import { Shape } from '../keras_format/common';
import { TensorOrArrayOrMap } from '../types';
import { Dataset, LazyIterator } from './dataset_stub';
import { FitDatasetElement } from './training_dataset';
export interface FakeDatasetArgs {
    /**
     * The shape(s) of the features of a single example.
     *
     * Use an object mapping name to shape, if more than one feature tensors
     * are required.
     */
    xShape: Shape | {
        [name: string]: Shape;
    };
    /**
     * The shape of the target(s) of a single exapmle.
     */
    yShape: Shape | {
        [name: string]: Shape;
    };
    /**
     * A function that generates preset sequence of X tensors.
     *
     * This function is invoked each time a new iterator is created.
     */
    xTensorsFunc?: () => tfc.Tensor[] | {
        [name: string]: tfc.Tensor[];
    };
    /**
     * A function that generates preset sequence of Y tensors.
     *
     * This function is invoked each time a new iterator is created.
     */
    yTensorsFunc?: () => tfc.Tensor[] | {
        [name: string]: tfc.Tensor[];
    };
    /**
     * The size of each batch generated by the iterator.
     */
    batchSize: number;
    /**
     * The number of batches an iterator generates before declaring done to be
     * true.
     */
    numBatches: number;
}
/**
 * A fake dataset with configurable feature and target shapes.
 *
 * The batch size and # of batches are also configurable.
 *
 * The iterator from the dataset always generate random-normal float32 values.
 */
export declare class FakeNumericDataset extends Dataset<FitDatasetElement> {
    readonly args: FakeDatasetArgs;
    constructor(args: FakeDatasetArgs);
    iterator(): Promise<LazyIterator<FitDatasetElement>>;
}
export declare class FakeNumericDatasetLegacyArrayForm extends Dataset<[TensorOrArrayOrMap, TensorOrArrayOrMap]> {
    readonly args: FakeDatasetArgs;
    ds: FakeNumericDataset;
    constructor(args: FakeDatasetArgs);
    iterator(): Promise<LazyIterator<[TensorOrArrayOrMap, TensorOrArrayOrMap]>>;
}
