import React, { useState, useEffect } from 'react';
import { ShoppingCart, Star, Filter, Search, Package, Truck, Shield, Award, Heart, Share2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  currency: string;
  images: string[];
  category: 'aligners' | 'hygiene' | 'accessories' | 'supplements' | 'equipment';
  brand: string;
  rating: number;
  reviewCount: number;
  inStock: boolean;
  stockCount: number;
  features: string[];
  specifications: { [key: string]: string };
  isRecommended: boolean;
  isExclusive: boolean;
  partnerDiscount?: number;
  estimatedDelivery: string;
  freeShipping: boolean;
  tags: string[];
}

interface Partner {
  id: string;
  name: string;
  logo: string;
  description: string;
  specialization: string[];
  rating: number;
  verified: boolean;
  exclusiveDiscount: number;
  products: string[];
  location: string;
}

interface CartItem {
  productId: string;
  quantity: number;
  selectedOptions?: { [key: string]: string };
}

export const Marketplace: React.FC = () => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest'>('relevance');
  const [showFilters, setShowFilters] = useState(false);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [wishlist, setWishlist] = useState<string[]>([]);

  const [products, setProducts] = useState<Product[]>([
    {
      id: '1',
      name: 'Kit de Nettoyage Invisalign Premium',
      description: 'Kit complet pour le nettoyage et l\'entretien de vos aligneurs transparents',
      price: 29.99,
      originalPrice: 39.99,
      currency: 'EUR',
      images: ['/product-cleaning-kit.jpg'],
      category: 'hygiene',
      brand: 'OrthoClean',
      rating: 4.8,
      reviewCount: 156,
      inStock: true,
      stockCount: 45,
      features: [
        'Tablettes de nettoyage (30 unités)',
        'Brosse spécialisée',
        'Étui de transport',
        'Guide d\'utilisation',
      ],
      specifications: {
        'Durée d\'action': '15 minutes',
        'Composition': 'Sans sulfate, sans parabène',
        'Certification': 'FDA approuvé',
      },
      isRecommended: true,
      isExclusive: false,
      partnerDiscount: 25,
      estimatedDelivery: '2-3 jours',
      freeShipping: true,
      tags: ['nettoyage', 'hygiène', 'invisalign'],
    },
    {
      id: '2',
      name: 'Chewies Orthodontiques - Pack de 6',
      description: 'Bâtonnets de mastication pour optimiser l\'ajustement des aligneurs',
      price: 12.99,
      currency: 'EUR',
      images: ['/product-chewies.jpg'],
      category: 'accessories',
      brand: 'AlignFit',
      rating: 4.6,
      reviewCount: 89,
      inStock: true,
      stockCount: 120,
      features: [
        '6 chewies de différentes densités',
        'Matériau médical sûr',
        'Améliore l\'ajustement',
        'Réduit l\'inconfort',
      ],
      specifications: {
        'Matériau': 'Silicone médical',
        'Durée de vie': '2-3 mois',
        'Couleurs': 'Transparent, bleu, rose',
      },
      isRecommended: false,
      isExclusive: true,
      estimatedDelivery: '1-2 jours',
      freeShipping: false,
      tags: ['chewies', 'ajustement', 'confort'],
    },
    {
      id: '3',
      name: 'Scanner Intra-oral Portable',
      description: 'Scanner 3D professionnel pour praticiens orthodontistes',
      price: 15999.99,
      currency: 'EUR',
      images: ['/product-scanner.jpg'],
      category: 'equipment',
      brand: 'ScanTech Pro',
      rating: 4.9,
      reviewCount: 23,
      inStock: true,
      stockCount: 3,
      features: [
        'Résolution 20 microns',
        'Scan couleur en temps réel',
        'Compatible tous logiciels',
        'Formation incluse',
      ],
      specifications: {
        'Résolution': '20 microns',
        'Vitesse': '15 fps',
        'Connectivité': 'USB 3.0, WiFi',
        'Garantie': '3 ans',
      },
      isRecommended: false,
      isExclusive: true,
      partnerDiscount: 15,
      estimatedDelivery: '5-7 jours',
      freeShipping: true,
      tags: ['scanner', 'professionnel', '3D'],
    },
    {
      id: '4',
      name: 'Complément Alimentaire Dents & Gencives',
      description: 'Vitamines et minéraux pour la santé bucco-dentaire pendant le traitement',
      price: 24.99,
      currency: 'EUR',
      images: ['/product-supplement.jpg'],
      category: 'supplements',
      brand: 'DentalVit',
      rating: 4.4,
      reviewCount: 67,
      inStock: true,
      stockCount: 78,
      features: [
        'Vitamine D3 + K2',
        'Calcium et Magnésium',
        'Coenzyme Q10',
        '60 gélules (2 mois)',
      ],
      specifications: {
        'Dosage': '2 gélules/jour',
        'Origine': 'Ingrédients naturels',
        'Certification': 'Bio certifié',
      },
      isRecommended: true,
      isExclusive: false,
      estimatedDelivery: '2-3 jours',
      freeShipping: true,
      tags: ['vitamines', 'santé', 'gencives'],
    },
  ]);

  const [partners, setPartners] = useState<Partner[]>([
    {
      id: '1',
      name: 'Invisalign',
      logo: '/partner-invisalign.png',
      description: 'Leader mondial des aligneurs transparents',
      specialization: ['Aligneurs', 'Technologie 3D', 'Formation'],
      rating: 4.9,
      verified: true,
      exclusiveDiscount: 20,
      products: ['aligners', 'accessories'],
      location: 'International',
    },
    {
      id: '2',
      name: 'Oral-B',
      logo: '/partner-oralb.png',
      description: 'Spécialiste de l\'hygiène bucco-dentaire',
      specialization: ['Brosses à dents', 'Soins dentaires', 'Innovation'],
      rating: 4.7,
      verified: true,
      exclusiveDiscount: 15,
      products: ['hygiene', 'accessories'],
      location: 'Europe',
    },
    {
      id: '3',
      name: 'iTero',
      logo: '/partner-itero.png',
      description: 'Scanners intra-oraux de pointe',
      specialization: ['Scanners 3D', 'Imagerie', 'Logiciels'],
      rating: 4.8,
      verified: true,
      exclusiveDiscount: 10,
      products: ['equipment'],
      location: 'USA',
    },
  ]);

  const categories = [
    { id: 'all', label: 'Tout', icon: '🛍️', count: products.length },
    { id: 'aligners', label: 'Aligneurs', icon: '🦷', count: products.filter(p => p.category === 'aligners').length },
    { id: 'hygiene', label: 'Hygiène', icon: '🧼', count: products.filter(p => p.category === 'hygiene').length },
    { id: 'accessories', label: 'Accessoires', icon: '🔧', count: products.filter(p => p.category === 'accessories').length },
    { id: 'supplements', label: 'Compléments', icon: '💊', count: products.filter(p => p.category === 'supplements').length },
    { id: 'equipment', label: 'Équipement', icon: '⚕️', count: products.filter(p => p.category === 'equipment').length },
  ];

  const addToCart = (productId: string, quantity: number = 1) => {
    setCart(prev => {
      const existingItem = prev.find(item => item.productId === productId);
      if (existingItem) {
        return prev.map(item =>
          item.productId === productId
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }
      return [...prev, { productId, quantity }];
    });
    toast.success('Produit ajouté au panier');
  };

  const toggleWishlist = (productId: string) => {
    setWishlist(prev => {
      if (prev.includes(productId)) {
        toast.success('Retiré de la liste de souhaits');
        return prev.filter(id => id !== productId);
      } else {
        toast.success('Ajouté à la liste de souhaits');
        return [...prev, productId];
      }
    });
  };

  const shareProduct = (product: Product) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Lien copié dans le presse-papiers');
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price_low':
        return a.price - b.price;
      case 'price_high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return b.id.localeCompare(a.id);
      default:
        return 0;
    }
  });

  const getCartItemCount = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 text-green-600 rounded-full">
              <ShoppingCart size={20} />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Marketplace OrthoProgress</h1>
              <p className="text-gray-600">Produits et services orthodontiques certifiés</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
              <Heart size={20} />
              {wishlist.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {wishlist.length}
                </span>
              )}
            </button>
            
            <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
              <ShoppingCart size={20} />
              {getCartItemCount() > 0 && (
                <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getCartItemCount()}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Recherche et filtres */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher des produits..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="relevance">Pertinence</option>
              <option value="price_low">Prix croissant</option>
              <option value="price_high">Prix décroissant</option>
              <option value="rating">Mieux notés</option>
              <option value="newest">Plus récents</option>
            </select>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
            >
              <Filter size={16} />
              Filtres
            </button>
          </div>
        </div>
      </div>

      {/* Catégories */}
      <div className="bg-white rounded-lg p-4 shadow-lg">
        <div className="flex gap-2 overflow-x-auto">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span>{category.icon}</span>
              {category.label}
              <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                {category.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Partenaires en vedette */}
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
          <Award size={20} className="text-yellow-600" />
          Partenaires Certifiés
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {partners.map(partner => (
            <motion.div
              key={partner.id}
              whileHover={{ scale: 1.02 }}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🏢</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900">{partner.name}</h3>
                    {partner.verified && (
                      <Shield size={16} className="text-blue-600" />
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star size={14} className="text-yellow-500 fill-current" />
                    <span className="text-sm text-gray-600">{partner.rating}</span>
                  </div>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-3">{partner.description}</p>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-green-600">
                  -{partner.exclusiveDiscount}% exclusif
                </span>
                <span className="text-xs text-gray-500">{partner.location}</span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Grille de produits */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sortedProducts.map(product => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
          >
            {/* Image du produit */}
            <div className="relative aspect-square bg-gray-200">
              <div className="absolute inset-0 flex items-center justify-center">
                <Package size={48} className="text-gray-400" />
              </div>
              
              {/* Badges */}
              <div className="absolute top-2 left-2 flex flex-col gap-1">
                {product.isRecommended && (
                  <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                    Recommandé
                  </span>
                )}
                {product.isExclusive && (
                  <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">
                    Exclusif
                  </span>
                )}
                {product.partnerDiscount && (
                  <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                    -{product.partnerDiscount}%
                  </span>
                )}
              </div>
              
              {/* Actions */}
              <div className="absolute top-2 right-2 flex flex-col gap-1">
                <button
                  onClick={() => toggleWishlist(product.id)}
                  className={`p-2 rounded-full transition-colors ${
                    wishlist.includes(product.id)
                      ? 'bg-red-100 text-red-600'
                      : 'bg-white text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Heart size={16} fill={wishlist.includes(product.id) ? 'currentColor' : 'none'} />
                </button>
                
                <button
                  onClick={() => shareProduct(product)}
                  className="p-2 bg-white text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <Share2 size={16} />
                </button>
              </div>
            </div>

            {/* Informations du produit */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                  {product.name}
                </h3>
                <div className="flex items-center gap-1 ml-2">
                  <Star size={14} className="text-yellow-500 fill-current" />
                  <span className="text-sm text-gray-600">{product.rating}</span>
                </div>
              </div>
              
              <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                {product.description}
              </p>
              
              <div className="flex items-center gap-2 mb-3">
                <span className="text-lg font-bold text-gray-900">
                  {formatPrice(product.price, product.currency)}
                </span>
                {product.originalPrice && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatPrice(product.originalPrice, product.currency)}
                  </span>
                )}
              </div>
              
              {/* Livraison */}
              <div className="flex items-center gap-2 mb-3 text-xs text-gray-600">
                <Truck size={12} />
                <span>{product.estimatedDelivery}</span>
                {product.freeShipping && (
                  <span className="text-green-600 font-medium">Livraison gratuite</span>
                )}
              </div>
              
              {/* Stock */}
              <div className="flex items-center justify-between mb-4">
                <span className={`text-xs ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                  {product.inStock ? `${product.stockCount} en stock` : 'Rupture de stock'}
                </span>
                <span className="text-xs text-gray-500">{product.brand}</span>
              </div>
              
              {/* Bouton d'achat */}
              <button
                onClick={() => addToCart(product.id)}
                disabled={!product.inStock}
                className={`w-full py-2 px-4 rounded-lg font-medium text-sm transition-colors ${
                  product.inStock
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {product.inStock ? 'Ajouter au panier' : 'Indisponible'}
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Message si aucun produit */}
      {sortedProducts.length === 0 && (
        <div className="bg-white rounded-lg p-12 text-center shadow-lg">
          <Package size={64} className="text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucun produit trouvé</h3>
          <p className="text-gray-600">
            Essayez de modifier vos critères de recherche ou de filtrage.
          </p>
        </div>
      )}
    </div>
  );
};
