import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  PhoneIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  FaceSmileIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  VideoCameraSlashIcon,
  ShareIcon,
  UserGroupIcon,
  MagnifyingGlassIcon,
  ArchiveBoxIcon,
  TrashIcon,
  ArrowUturnLeftIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  LanguageIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface Message {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'voice';
  status: 'sent' | 'delivered' | 'read';
  priority: 'low' | 'normal' | 'urgent';
}

interface Participant {
  id: string;
  name: string;
  avatar: string;
  status: 'online' | 'offline' | 'away';
  role: 'patient' | 'practitioner' | 'admin';
}

const AdvancedCommunication: React.FC = () => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState<'chat' | 'video' | 'voice'>('chat');
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'normal' | 'urgent'>('normal');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'Dr. Martin',
      content: 'Bonjour ! Comment se passe votre traitement cette semaine ?',
      timestamp: new Date(Date.now() - 3600000),
      type: 'text',
      status: 'read',
      priority: 'normal'
    },
    {
      id: '2',
      sender: 'Vous',
      content: 'Très bien ! Je porte mes gouttières 22h par jour comme recommandé.',
      timestamp: new Date(Date.now() - 3000000),
      type: 'text',
      status: 'read',
      priority: 'normal'
    },
    {
      id: '3',
      sender: 'Dr. Martin',
      content: 'Excellent ! Continuez ainsi. Avez-vous des questions ?',
      timestamp: new Date(Date.now() - 1800000),
      type: 'text',
      status: 'read',
      priority: 'normal'
    }
  ]);

  const [participants] = useState<Participant[]>([
    {
      id: '1',
      name: 'Dr. Martin',
      avatar: '/api/placeholder/40/40',
      status: 'online',
      role: 'practitioner'
    },
    {
      id: '2',
      name: 'Assistant Sarah',
      avatar: '/api/placeholder/40/40',
      status: 'online',
      role: 'admin'
    }
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = () => {
    if (messageInput.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        sender: 'Vous',
        content: messageInput,
        timestamp: new Date(),
        type: 'text',
        status: 'sent',
        priority: selectedPriority
      };
      setMessages([...messages, newMessage]);
      setMessageInput('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const startCall = (type: 'video' | 'voice') => {
    setActiveTab(type);
    setIsCallActive(true);
  };

  const endCall = () => {
    setIsCallActive(false);
    setActiveTab('chat');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500';
      case 'normal':
        return 'border-l-blue-500';
      case 'low':
        return 'border-l-gray-400';
      default:
        return 'border-l-gray-400';
    }
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCircleIcon className="w-4 h-4 text-blue-500" />;
      case 'read':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* En-tête */}
      <div className="surface-elevated border-b border-adaptive p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
              <ChatBubbleLeftRightIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-adaptive">{t('communication.title')}</h2>
              <div className="flex items-center space-x-2">
                {participants.map((participant) => (
                  <div key={participant.id} className="flex items-center space-x-1">
                    <div className="relative">
                      <img
                        src={participant.avatar}
                        alt={participant.name}
                        className="w-6 h-6 rounded-full"
                      />
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-900 ${getStatusColor(participant.status)}`} />
                    </div>
                    <span className="text-sm text-adaptive-secondary">{participant.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => startCall('voice')}
              className="p-2 hover-surface rounded-lg transition-colors"
              title={t('communication.voice')}
            >
              <PhoneIcon className="w-5 h-5 text-adaptive" />
            </button>
            <button
              onClick={() => startCall('video')}
              className="p-2 hover-surface rounded-lg transition-colors"
              title={t('communication.video')}
            >
              <VideoCameraIcon className="w-5 h-5 text-adaptive" />
            </button>
            <button className="p-2 hover-surface rounded-lg transition-colors">
              <EllipsisVerticalIcon className="w-5 h-5 text-adaptive" />
            </button>
          </div>
        </div>

        {/* Barre de recherche */}
        <div className="mt-4 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-adaptive-tertiary" />
          <input
            type="text"
            placeholder={t('communication.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="form-input pl-10"
          />
        </div>
      </div>

      {/* Zone d'appel vidéo/vocal */}
      <AnimatePresence>
        {isCallActive && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="surface-secondary border-b border-adaptive p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {activeTab === 'video' ? (
                    <div className="w-32 h-24 bg-gray-900 rounded-lg flex items-center justify-center">
                      <VideoCameraIcon className="w-8 h-8 text-white" />
                    </div>
                  ) : (
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                      <PhoneIcon className="w-8 h-8 text-white" />
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-semibold text-adaptive">
                    {activeTab === 'video' ? t('communication.video') : t('communication.voice')}
                  </h3>
                  <p className="text-adaptive-secondary">Dr. Martin</p>
                  <p className="text-sm text-adaptive-tertiary">{t('communication.call.duration')}: 05:23</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsMuted(!isMuted)}
                  className={`p-3 rounded-full transition-colors ${
                    isMuted ? 'bg-red-500 text-white' : 'surface-tertiary text-adaptive'
                  }`}
                >
                  {isMuted ? <SpeakerXMarkIcon className="w-5 h-5" /> : <SpeakerWaveIcon className="w-5 h-5" />}
                </button>

                {activeTab === 'video' && (
                  <>
                    <button
                      onClick={() => setIsCameraOn(!isCameraOn)}
                      className={`p-3 rounded-full transition-colors ${
                        !isCameraOn ? 'bg-red-500 text-white' : 'surface-tertiary text-adaptive'
                      }`}
                    >
                      {isCameraOn ? <VideoCameraIcon className="w-5 h-5" /> : <VideoCameraSlashIcon className="w-5 h-5" />}
                    </button>
                    <button
                      onClick={() => setIsScreenSharing(!isScreenSharing)}
                      className={`p-3 rounded-full transition-colors ${
                        isScreenSharing ? 'bg-blue-500 text-white' : 'surface-tertiary text-adaptive'
                      }`}
                    >
                      <ShareIcon className="w-5 h-5" />
                    </button>
                  </>
                )}

                <button
                  onClick={endCall}
                  className="p-3 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <PhoneIcon className="w-5 h-5 transform rotate-135" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Zone des messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages
          .filter(message => 
            searchQuery === '' || 
            message.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
            message.sender.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex ${message.sender === 'Vous' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-xs lg:max-w-md ${message.sender === 'Vous' ? 'order-2' : 'order-1'}`}>
                <div className={`surface-secondary rounded-2xl p-4 border-l-4 ${getPriorityColor(message.priority)}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-adaptive">{message.sender}</span>
                    <div className="flex items-center space-x-1">
                      {message.priority === 'urgent' && (
                        <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
                      )}
                      {getMessageStatusIcon(message.status)}
                    </div>
                  </div>
                  <p className="text-adaptive-secondary">{message.content}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-adaptive-tertiary">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                    <div className="flex items-center space-x-1">
                      <button className="p-1 hover-surface rounded">
                        <ArrowUturnLeftIcon className="w-3 h-3 text-adaptive-tertiary" />
                      </button>
                      <button className="p-1 hover-surface rounded">
                        <LanguageIcon className="w-3 h-3 text-adaptive-tertiary" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Zone de saisie */}
      <div className="surface-elevated border-t border-adaptive p-4">
        <div className="flex items-center space-x-2 mb-2">
          <select
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value as 'low' | 'normal' | 'urgent')}
            className="form-input text-sm"
          >
            <option value="low">{t('communication.low')}</option>
            <option value="normal">{t('communication.normal')}</option>
            <option value="urgent">{t('communication.urgent')}</option>
          </select>
        </div>

        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={t('communication.typing')}
              className="form-input resize-none"
              rows={2}
            />
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 hover-surface rounded-lg transition-colors">
              <PaperClipIcon className="w-5 h-5 text-adaptive-secondary" />
            </button>
            <button className="p-2 hover-surface rounded-lg transition-colors">
              <FaceSmileIcon className="w-5 h-5 text-adaptive-secondary" />
            </button>
            <button className="p-2 hover-surface rounded-lg transition-colors">
              <MicrophoneIcon className="w-5 h-5 text-adaptive-secondary" />
            </button>
            <button
              onClick={sendMessage}
              disabled={!messageInput.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedCommunication;
