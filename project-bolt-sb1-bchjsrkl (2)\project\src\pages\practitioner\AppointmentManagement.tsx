import React, { useState } from 'react';
import { Calendar as CalendarIcon, Clock, User, Video, Phone, MessageSquare, Plus, X } from 'lucide-react';

interface Appointment {
  id: string;
  patientName: string;
  patientId: string;
  date: string;
  time: string;
  duration: number;
  type: 'consultation' | 'suivi' | 'urgence' | 'visio';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

const AppointmentManagement: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedView, setSelectedView] = useState<'day' | 'week'>('day');

  const appointments: Appointment[] = [
    {
      id: '1',
      patientName: '<PERSON>',
      patientId: 'MD2024001',
      date: '2024-06-15',
      time: '09:00',
      duration: 30,
      type: 'suivi',
      status: 'scheduled',
      notes: 'Contrôle aligneurs'
    },
    {
      id: '2',
      patientName: '<PERSON>',
      patientId: 'PM2023002',
      date: '2024-06-15',
      time: '10:00',
      duration: 45,
      type: 'consultation',
      status: 'scheduled'
    },
    {
      id: '3',
      patientName: 'Sophie Laurent',
      patientId: 'SL2023003',
      date: '2024-06-15',
      time: '14:30',
      duration: 30,
      type: 'visio',
      status: 'scheduled',
      notes: 'Consultation de suivi à distance'
    }
  ];

  const getAppointmentTypeColor = (type: string) => {
    switch (type) {
      case 'consultation': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'suivi': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'urgence': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'visio': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      default: return 'surface-tertiary text-adaptive';
    }
  };

  const getAppointmentTypeText = (type: string) => {
    switch (type) {
      case 'consultation': return 'Consultation';
      case 'suivi': return 'Suivi';
      case 'urgence': return 'Urgence';
      case 'visio': return 'Visioconférence';
      default: return type;
    }
  };

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return `${hour}:00`;
  });

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-adaptive">Gestion des Rendez-vous</h1>
            <p className="text-adaptive-secondary">Planifiez et gérez vos consultations</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Calendrier et contrôles */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6 mb-6">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() - 1)))}
              className="p-2 hover:surface-tertiary rounded-lg transition-colors"
            >
              ←
            </button>
            <div className="text-lg font-semibold">
              {selectedDate.toLocaleDateString('fr-FR', { 
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
              })}
            </div>
            <button
              onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() + 1)))}
              className="p-2 hover:surface-tertiary rounded-lg transition-colors"
            >
              →
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSelectedView('day')}
              className={`px-4 py-2 rounded-lg ${
                selectedView === 'day' 
                  ? 'bg-blue-500 text-white' 
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              Jour
            </button>
            <button
              onClick={() => setSelectedView('week')}
              className={`px-4 py-2 rounded-lg ${
                selectedView === 'week' 
                  ? 'bg-blue-500 text-white' 
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              Semaine
            </button>
          </div>
        </div>

        {/* Grille des rendez-vous */}
        <div className="relative min-h-[600px] border border-adaptive rounded-lg">
          {/* Heures */}
          <div className="absolute top-0 left-0 w-20 h-full border-r border-adaptive">
            {timeSlots.map((time) => (
              <div
                key={time}
                className="h-[60px] border-b border-adaptive-light text-sm text-adaptive-tertiary px-2 flex items-center justify-center"
              >
                {time}
              </div>
            ))}
          </div>

          {/* Zone des rendez-vous */}
          <div className="ml-20">
            {appointments.map((appointment) => {
              const startHour = parseInt(appointment.time.split(':')[0]);
              const startMinute = parseInt(appointment.time.split(':')[1]);
              const top = (startHour * 60 + startMinute) * (60 / 60); // 60px par heure

              return (
                <div
                  key={appointment.id}
                  className="absolute left-2 right-2 surface-primary rounded-lg border border-adaptive shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  style={{
                    top: `${top}px`,
                    height: `${appointment.duration}px`
                  }}
                >
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        getAppointmentTypeColor(appointment.type)
                      }`}>
                        {getAppointmentTypeText(appointment.type)}
                      </span>
                      <span className="text-sm text-adaptive-tertiary">
                        {appointment.time}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <User className="w-4 h-4 mr-2 text-gray-400" />
                      <span className="font-medium text-adaptive">
                        {appointment.patientName}
                      </span>
                    </div>
                    {appointment.notes && (
                      <p className="mt-1 text-xs text-adaptive-tertiary">
                        {appointment.notes}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Modal d'ajout de rendez-vous */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-adaptive">Nouveau Rendez-vous</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-1 hover:surface-tertiary rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-adaptive-tertiary" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Patient
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Sélectionnez un patient</option>
                  <option value="1">Marie Dubois</option>
                  <option value="2">Pierre Martin</option>
                  <option value="3">Sophie Laurent</option>
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                    Heure
                  </label>
                  <input
                    type="time"
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Type de rendez-vous
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="consultation">Consultation</option>
                  <option value="suivi">Suivi</option>
                  <option value="urgence">Urgence</option>
                  <option value="visio">Visioconférence</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Durée (minutes)
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="15">15 minutes</option>
                  <option value="30">30 minutes</option>
                  <option value="45">45 minutes</option>
                  <option value="60">1 heure</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Notes
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Ajoutez des notes pour ce rendez-vous..."
                ></textarea>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-adaptive-secondary hover:text-adaptive transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  alert('Rendez-vous ajouté avec succès !');
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentManagement;
