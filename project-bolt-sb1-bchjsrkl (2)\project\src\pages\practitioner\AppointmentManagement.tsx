import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, Clock, User, Video, Phone, MessageSquare, Plus, X, Edit, Trash2, CheckCircle, AlertCircle, Search, Filter } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { toast } from 'react-hot-toast';

interface Appointment {
  id: string;
  patientName: string;
  patientId: string;
  date: string;
  time: string;
  duration: number;
  type: 'consultation' | 'suivi' | 'urgence' | 'visio';
  status: 'scheduled' | 'completed' | 'cancelled' | 'confirmed';
  notes?: string;
  patientPhone?: string;
  patientEmail?: string;
  reminderSent?: boolean;
}

interface NewAppointment {
  patientName: string;
  patientId: string;
  date: string;
  time: string;
  duration: number;
  type: 'consultation' | 'suivi' | 'urgence' | 'visio';
  notes: string;
  patientPhone: string;
  patientEmail: string;
}

const AppointmentManagement: React.FC = () => {
  const { t } = useLanguage();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedView, setSelectedView] = useState<'day' | 'week' | 'month'>('day');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);

  const [appointments, setAppointments] = useState<Appointment[]>([
    {
      id: '1',
      patientName: 'Marie Dubois',
      patientId: 'MD2024001',
      date: '2024-06-15',
      time: '09:00',
      duration: 30,
      type: 'suivi',
      status: 'confirmed',
      notes: 'Contrôle aligneurs - Progression excellente',
      patientPhone: '+33 6 12 34 56 78',
      patientEmail: '<EMAIL>',
      reminderSent: true
    },
    {
      id: '2',
      patientName: 'Pierre Martin',
      patientId: 'PM2023002',
      date: '2024-06-15',
      time: '10:00',
      duration: 45,
      type: 'consultation',
      status: 'scheduled',
      notes: 'Première consultation - Évaluation complète',
      patientPhone: '+33 6 98 76 54 32',
      patientEmail: '<EMAIL>',
      reminderSent: false
    },
    {
      id: '3',
      patientName: 'Sophie Laurent',
      patientId: 'SL2023003',
      date: '2024-06-15',
      time: '14:30',
      duration: 30,
      type: 'visio',
      status: 'scheduled',
      notes: 'Téléconsultation de suivi - Invisalign',
      patientPhone: '+33 6 11 22 33 44',
      patientEmail: '<EMAIL>',
      reminderSent: false
    },
    {
      id: '4',
      patientName: 'Lucas Moreau',
      patientId: 'LM2024004',
      date: '2024-06-16',
      time: '11:00',
      duration: 90,
      type: 'urgence',
      status: 'scheduled',
      notes: 'Urgence - Bracket décollé, douleur',
      patientPhone: '+33 6 55 66 77 88',
      patientEmail: '<EMAIL>',
      reminderSent: true
    },
    {
      id: '5',
      patientName: 'Emma Rousseau',
      patientId: 'ER2024005',
      date: '2024-06-17',
      time: '15:00',
      duration: 60,
      type: 'consultation',
      status: 'completed',
      notes: 'Consultation terminée - Plan de traitement établi',
      patientPhone: '+33 6 77 88 99 00',
      patientEmail: '<EMAIL>',
      reminderSent: true
    }
  ]);

  const [newAppointment, setNewAppointment] = useState<NewAppointment>({
    patientName: '',
    patientId: '',
    date: '',
    time: '',
    duration: 60,
    type: 'consultation',
    notes: '',
    patientPhone: '',
    patientEmail: ''
  });

  // Fonctions de gestion des rendez-vous
  const handleAddAppointment = () => {
    if (!newAppointment.patientName || !newAppointment.date || !newAppointment.time) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    const appointment: Appointment = {
      id: Date.now().toString(),
      ...newAppointment,
      status: 'scheduled',
      reminderSent: false
    };

    setAppointments(prev => [...prev, appointment]);
    setNewAppointment({
      patientName: '',
      patientId: '',
      date: '',
      time: '',
      duration: 60,
      type: 'consultation',
      notes: '',
      patientPhone: '',
      patientEmail: ''
    });
    setShowAddModal(false);
    toast.success('Rendez-vous ajouté avec succès');
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setShowEditModal(true);
  };

  const handleUpdateAppointment = () => {
    if (!editingAppointment) return;

    setAppointments(prev =>
      prev.map(apt =>
        apt.id === editingAppointment.id ? editingAppointment : apt
      )
    );
    setEditingAppointment(null);
    setShowEditModal(false);
    toast.success('Rendez-vous modifié avec succès');
  };

  const handleDeleteAppointment = (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
      setAppointments(prev => prev.filter(apt => apt.id !== id));
      toast.success('Rendez-vous supprimé');
    }
  };

  const handleStatusChange = (id: string, newStatus: Appointment['status']) => {
    setAppointments(prev =>
      prev.map(apt =>
        apt.id === id ? { ...apt, status: newStatus } : apt
      )
    );
    toast.success(`Statut mis à jour: ${newStatus}`);
  };

  const sendReminder = (appointment: Appointment) => {
    setAppointments(prev =>
      prev.map(apt =>
        apt.id === appointment.id ? { ...apt, reminderSent: true } : apt
      )
    );
    toast.success(`Rappel envoyé à ${appointment.patientName}`);
  };

  const getAppointmentTypeColor = (type: string) => {
    switch (type) {
      case 'consultation': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'suivi': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'urgence': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'visio': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      default: return 'surface-tertiary text-adaptive';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'confirmed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getAppointmentTypeText = (type: string) => {
    switch (type) {
      case 'consultation': return t('appointments.consultation');
      case 'suivi': return t('appointments.followUp');
      case 'urgence': return t('appointments.emergency');
      case 'visio': return t('appointments.teleconsultation');
      default: return type;
    }
  };

  // Filtrage des rendez-vous
  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = appointment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         appointment.patientId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Statistiques
  const stats = {
    total: appointments.length,
    today: appointments.filter(apt => apt.date === new Date().toISOString().split('T')[0]).length,
    pending: appointments.filter(apt => apt.status === 'scheduled').length,
    completed: appointments.filter(apt => apt.status === 'completed').length
  };

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return `${hour}:00`;
  });

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-adaptive">Gestion des Rendez-vous</h1>
            <p className="text-adaptive-secondary">Planifiez et gérez vos consultations</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Nouveau RDV
          </button>
        </div>
      </div>

      {/* Calendrier et contrôles */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6 mb-6">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() - 1)))}
              className="p-2 hover:surface-tertiary rounded-lg transition-colors"
            >
              ←
            </button>
            <div className="text-lg font-semibold">
              {selectedDate.toLocaleDateString('fr-FR', { 
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
              })}
            </div>
            <button
              onClick={() => setSelectedDate(new Date(selectedDate.setDate(selectedDate.getDate() + 1)))}
              className="p-2 hover:surface-tertiary rounded-lg transition-colors"
            >
              →
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSelectedView('day')}
              className={`px-4 py-2 rounded-lg ${
                selectedView === 'day' 
                  ? 'bg-blue-500 text-white' 
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              Jour
            </button>
            <button
              onClick={() => setSelectedView('week')}
              className={`px-4 py-2 rounded-lg ${
                selectedView === 'week' 
                  ? 'bg-blue-500 text-white' 
                  : 'surface-tertiary text-adaptive-secondary hover:surface-tertiary'
              }`}
            >
              Semaine
            </button>
          </div>
        </div>

        {/* Grille des rendez-vous */}
        <div className="relative min-h-[600px] border border-adaptive rounded-lg">
          {/* Heures */}
          <div className="absolute top-0 left-0 w-20 h-full border-r border-adaptive">
            {timeSlots.map((time) => (
              <div
                key={time}
                className="h-[60px] border-b border-adaptive-light text-sm text-adaptive-tertiary px-2 flex items-center justify-center"
              >
                {time}
              </div>
            ))}
          </div>

          {/* Zone des rendez-vous */}
          <div className="ml-20">
            {appointments.map((appointment) => {
              const startHour = parseInt(appointment.time.split(':')[0]);
              const startMinute = parseInt(appointment.time.split(':')[1]);
              const top = (startHour * 60 + startMinute) * (60 / 60); // 60px par heure

              return (
                <div
                  key={appointment.id}
                  className="absolute left-2 right-2 surface-primary rounded-lg border border-adaptive shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  style={{
                    top: `${top}px`,
                    height: `${appointment.duration}px`
                  }}
                >
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        getAppointmentTypeColor(appointment.type)
                      }`}>
                        {getAppointmentTypeText(appointment.type)}
                      </span>
                      <span className="text-sm text-adaptive-tertiary">
                        {appointment.time}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <User className="w-4 h-4 mr-2 text-gray-400" />
                      <span className="font-medium text-adaptive">
                        {appointment.patientName}
                      </span>
                    </div>
                    {appointment.notes && (
                      <p className="mt-1 text-xs text-adaptive-tertiary">
                        {appointment.notes}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Modal d'ajout de rendez-vous */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-adaptive">Nouveau Rendez-vous</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-1 hover:surface-tertiary rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-adaptive-tertiary" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Patient
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Sélectionnez un patient</option>
                  <option value="1">Marie Dubois</option>
                  <option value="2">Pierre Martin</option>
                  <option value="3">Sophie Laurent</option>
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                    Heure
                  </label>
                  <input
                    type="time"
                    className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Type de rendez-vous
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="consultation">Consultation</option>
                  <option value="suivi">Suivi</option>
                  <option value="urgence">Urgence</option>
                  <option value="visio">Visioconférence</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Durée (minutes)
                </label>
                <select className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="15">15 minutes</option>
                  <option value="30">30 minutes</option>
                  <option value="45">45 minutes</option>
                  <option value="60">1 heure</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Notes
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Ajoutez des notes pour ce rendez-vous..."
                ></textarea>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-adaptive-secondary hover:text-adaptive transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  alert('Rendez-vous ajouté avec succès !');
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentManagement;
