import React, { useState } from 'react';
import { CreditCard, Check, Star, Calendar, Download, AlertCircle } from 'lucide-react';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  period: 'monthly' | 'yearly';
  features: string[];
  popular?: boolean;
}

const SubscriptionManagement: React.FC = () => {
  const [currentPlan, setCurrentPlan] = useState('monthly');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: 'monthly',
      name: 'Abonnement Mensuel',
      price: 24,
      period: 'monthly',
      features: [
        'Gestion illimitée de patients',
        'Suivi en temps réel',
        'Calendrier de rendez-vous',
        'Messagerie sécurisée',
        'Rapports et analytiques',
        'Support technique',
        'Sauvegarde cloud'
      ]
    },
    {
      id: 'yearly',
      name: 'Abonnement Annuel',
      price: 119,
      period: 'yearly',
      popular: true,
      features: [
        'Toutes les fonctionnalités mensuelles',
        'Économie de 5 mois',
        'Support prioritaire',
        'Formations exclusives',
        'Intégration Invisalign avancée',
        'API personnalisée',
        'Consultation stratégique'
      ]
    }
  ];

  const currentSubscription = {
    plan: 'monthly',
    startDate: '2024-01-15',
    nextBilling: '2024-07-15',
    status: 'active',
    autoRenew: true
  };

  const invoices = [
    {
      id: 'INV-2024-06',
      date: '2024-06-15',
      amount: 24,
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-2024-05',
      date: '2024-05-15',
      amount: 24,
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-2024-04',
      date: '2024-04-15',
      amount: 24,
      status: 'paid',
      downloadUrl: '#'
    }
  ];

  const handlePlanChange = (planId: string) => {
    setSelectedPlan(planId);
    setShowPaymentModal(true);
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Gestion de l'Abonnement</h1>
        <p className="text-gray-600">Gérez votre abonnement et vos paiements</p>
      </div>

      {/* Statut actuel */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800">Abonnement Actuel</h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            currentSubscription.status === 'active' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {currentSubscription.status === 'active' ? 'Actif' : 'Inactif'}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Plan actuel</h3>
            <p className="text-lg font-semibold text-gray-800">
              {plans.find(p => p.id === currentSubscription.plan)?.name}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Prochaine facturation</h3>
            <p className="text-lg font-semibold text-gray-800">
              {new Date(currentSubscription.nextBilling).toLocaleDateString('fr-FR')}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Renouvellement automatique</h3>
            <div className="flex items-center">
              <span className="text-lg font-semibold text-gray-800 mr-3">
                {currentSubscription.autoRenew ? 'Activé' : 'Désactivé'}
              </span>
              <button className="text-blue-500 hover:text-blue-700 text-sm">
                Modifier
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Plans disponibles */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Plans Disponibles</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-xl shadow-sm border-2 p-6 ${
                plan.popular 
                  ? 'border-blue-500' 
                  : currentPlan === plan.id 
                    ? 'border-green-500' 
                    : 'border-gray-100'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 mr-1" />
                    Populaire
                  </span>
                </div>
              )}

              {currentPlan === plan.id && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Actuel
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-800 mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-gray-800">
                  €{plan.price}
                  <span className="text-lg font-normal text-gray-500">
                    /{plan.period === 'monthly' ? 'mois' : 'an'}
                  </span>
                </div>
                {plan.period === 'yearly' && (
                  <p className="text-sm text-green-600 mt-1">
                    Économisez €21 par an
                  </p>
                )}
              </div>

              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <Check className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handlePlanChange(plan.id)}
                disabled={currentPlan === plan.id}
                className={`w-full py-3 rounded-lg font-medium transition-colors ${
                  currentPlan === plan.id
                    ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    : plan.popular
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-gray-800 text-white hover:bg-gray-900'
                }`}
              >
                {currentPlan === plan.id ? 'Plan Actuel' : 'Choisir ce plan'}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Historique des factures */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Historique des Factures</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Facture</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Montant</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Statut</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map((invoice) => (
                <tr key={invoice.id} className="border-b border-gray-100">
                  <td className="py-3 px-4 font-mono text-sm">{invoice.id}</td>
                  <td className="py-3 px-4">
                    {new Date(invoice.date).toLocaleDateString('fr-FR')}
                  </td>
                  <td className="py-3 px-4 font-semibold">€{invoice.amount}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      invoice.status === 'paid' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {invoice.status === 'paid' ? 'Payée' : 'Impayée'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button className="flex items-center text-blue-500 hover:text-blue-700 text-sm">
                      <Download className="w-4 h-4 mr-1" />
                      Télécharger
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de paiement */}
      {showPaymentModal && selectedPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Confirmer le changement d'abonnement
            </h3>
            
            <div className="mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-blue-500 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm text-blue-800">
                      Vous allez passer au plan{' '}
                      <strong>{plans.find(p => p.id === selectedPlan)?.name}</strong>
                    </p>
                    <p className="text-sm text-blue-700 mt-1">
                      Le changement sera effectif immédiatement.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numéro de carte
                </label>
                <input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expiration
                  </label>
                  <input
                    type="text"
                    placeholder="MM/AA"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    CVV
                  </label>
                  <input
                    type="text"
                    placeholder="123"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setCurrentPlan(selectedPlan);
                  alert('Abonnement mis à jour avec succès !');
                }}
                className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Confirmer le paiement
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagement;
