/**
 * @license
 * Copyright 2023 Google LLC.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/layers/nlp/models/preprocessor" />
import { serialization } from '@tensorflow/tfjs-core';
import { Layer, LayerArgs } from '../../../engine/topology';
import { Tokenizer } from '../tokenizers';
/**
 * Base class for model Preprocessors.
 */
export declare class Preprocessor extends Layer {
    /** @nocollapse */
    static className: string;
    private _tokenizer;
    constructor(args: LayerArgs);
    /**
     * The tokenizer used to tokenize strings.
     */
    get tokenizer(): Tokenizer;
    set tokenizer(value: Tokenizer);
    getConfig(): serialization.ConfigDict;
    static fromConfig<T extends serialization.Serializable>(cls: serialization.SerializableConstructor<T>, config: serialization.ConfigDict): T;
    static tokenizerCls<T extends serialization.Serializable>(cls: serialization.SerializableConstructor<T>): void;
}
