import React, { useState } from 'react';
import { Calendar, Check, X, Clock, Smile, Brush, Zap } from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, isFuture } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useLanguage } from '../../contexts/LanguageContext';

interface DailyTrackingData {
  date: Date;
  applianceWear: boolean;
  brushingMorning: boolean;
  brushingEvening: boolean;
  bracesCheck: boolean;
  notes?: string;
}

interface DailyTrackingCalendarProps {
  trackingData: DailyTrackingData[];
  onUpdateTracking: (data: DailyTrackingData) => void;
}

const DailyTrackingCalendar: React.FC<DailyTrackingCalendarProps> = ({
  trackingData,
  onUpdateTracking
}) => {
  const { t } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const getTrackingForDate = (date: Date): DailyTrackingData | undefined => {
    return trackingData.find(tracking => isSameDay(tracking.date, date));
  };

  const updateDailyTracking = (date: Date, field: keyof DailyTrackingData, value: boolean) => {
    const existingTracking = getTrackingForDate(date);
    const updatedTracking: DailyTrackingData = {
      date,
      applianceWear: existingTracking?.applianceWear ?? false,
      brushingMorning: existingTracking?.brushingMorning ?? false,
      brushingEvening: existingTracking?.brushingEvening ?? false,
      bracesCheck: existingTracking?.bracesCheck ?? false,
      notes: existingTracking?.notes ?? '',
      [field]: value
    };
    onUpdateTracking(updatedTracking);
  };

  const getDayStatus = (date: Date) => {
    const tracking = getTrackingForDate(date);
    if (!tracking) return 'none';

    const completed = tracking.applianceWear && tracking.brushingMorning && tracking.brushingEvening && tracking.bracesCheck;
    const partial = tracking.applianceWear || tracking.brushingMorning || tracking.brushingEvening || tracking.bracesCheck;

    if (completed) return 'complete';
    if (partial) return 'partial';
    return 'none';
  };

  const getCompletionRate = () => {
    const completedDays = trackingData.filter(tracking =>
      tracking.applianceWear && tracking.brushingMorning && tracking.brushingEvening && tracking.bracesCheck
    ).length;
    return trackingData.length > 0 ? Math.round((completedDays / trackingData.length) * 100) : 0;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentMonth(newMonth);
  };

  const selectedTracking = getTrackingForDate(selectedDate);

  return (
    <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-soft p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-adaptive dark:text-white flex items-center gap-2">
            <Calendar className="w-5 h-5 text-blue-500" />
            {t('dailyTracking.title')}
          </h3>
          <p className="text-sm text-adaptive-secondary dark:text-gray-400">
            {t('dailyTracking.subtitle')}
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {getCompletionRate()}%
          </div>
          <div className="text-xs text-adaptive-tertiary dark:text-gray-400">
            {t('dailyTracking.completionRate')}
          </div>
        </div>
      </div>

      {/* Navigation du calendrier */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-2 hover:surface-tertiary dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          ←
        </button>
        <h4 className="text-lg font-medium text-adaptive dark:text-white">
          {format(currentMonth, 'MMMM yyyy', { locale: fr })}
        </h4>
        <button
          onClick={() => navigateMonth('next')}
          className="p-2 hover:surface-tertiary dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          →
        </button>
      </div>

      {/* Grille du calendrier */}
      <div className="grid grid-cols-7 gap-1 mb-6">
        {[
          t('dailyTracking.calendar.monday'),
          t('dailyTracking.calendar.tuesday'),
          t('dailyTracking.calendar.wednesday'),
          t('dailyTracking.calendar.thursday'),
          t('dailyTracking.calendar.friday'),
          t('dailyTracking.calendar.saturday'),
          t('dailyTracking.calendar.sunday')
        ].map(day => (
          <div key={day} className="p-2 text-center text-xs font-medium text-adaptive-tertiary dark:text-gray-400">
            {day}
          </div>
        ))}
        
        {monthDays.map(date => {
          const status = getDayStatus(date);
          const isSelected = isSameDay(date, selectedDate);
          const isCurrentDay = isToday(date);
          const isFutureDate = isFuture(date);
          
          return (
            <button
              key={date.toISOString()}
              onClick={() => !isFutureDate && setSelectedDate(date)}
              disabled={isFutureDate}
              className={`
                p-2 text-sm rounded-lg transition-all duration-200 relative
                ${isFutureDate 
                  ? 'text-gray-300 dark:text-adaptive-secondary cursor-not-allowed' 
                  : 'hover:surface-tertiary dark:hover:bg-gray-700 cursor-pointer'
                }
                ${isSelected 
                  ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/30' 
                  : ''
                }
                ${isCurrentDay 
                  ? 'font-bold text-blue-600 dark:text-blue-400' 
                  : 'text-adaptive-secondary dark:text-gray-300'
                }
                ${status === 'complete' 
                  ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' 
                  : status === 'partial' 
                  ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' 
                  : ''
                }
              `}
            >
              {format(date, 'd')}
              {status === 'complete' && (
                <div className="absolute top-1 right-1">
                  <Check className="w-3 h-3 text-green-600" />
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Détails du jour sélectionné */}
      {!isFuture(selectedDate) && (
        <div className="border-t border-adaptive dark:border-gray-700 pt-4">
          <h5 className="font-medium text-adaptive dark:text-white mb-3">
            {format(selectedDate, 'EEEE d MMMM yyyy', { locale: fr })}
          </h5>
          
          <div className="space-y-3">
            {/* Port d'appareil */}
            <div className="flex items-center justify-between p-3 surface-secondary dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <Smile className="w-5 h-5 text-blue-500" />
                <span className="text-sm font-medium text-adaptive-secondary dark:text-gray-300">
                  {t('dailyTracking.applianceWear')}
                </span>
              </div>
              <button
                onClick={() => updateDailyTracking(selectedDate, 'applianceWear', !selectedTracking?.applianceWear)}
                className={`
                  w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 active:scale-95
                  ${selectedTracking?.applianceWear
                    ? 'bg-green-500 border-green-500 text-white shadow-lg'
                    : 'border-gray-300 dark:border-gray-600 hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
                  }
                `}
              >
                {selectedTracking?.applianceWear && <Check className="w-5 h-5" />}
              </button>
            </div>

            {/* Brossage matin */}
            <div className="flex items-center justify-between p-3 surface-secondary dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <Brush className="w-5 h-5 text-orange-500" />
                <span className="text-sm font-medium text-adaptive-secondary dark:text-gray-300">
                  {t('dailyTracking.brushingMorning')}
                </span>
              </div>
              <button
                onClick={() => updateDailyTracking(selectedDate, 'brushingMorning', !selectedTracking?.brushingMorning)}
                className={`
                  w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 active:scale-95
                  ${selectedTracking?.brushingMorning
                    ? 'bg-green-500 border-green-500 text-white shadow-lg'
                    : 'border-gray-300 dark:border-gray-600 hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
                  }
                `}
              >
                {selectedTracking?.brushingMorning && <Check className="w-5 h-5" />}
              </button>
            </div>

            {/* Brossage soir */}
            <div className="flex items-center justify-between p-3 surface-secondary dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <Brush className="w-5 h-5 text-purple-500" />
                <span className="text-sm font-medium text-adaptive-secondary dark:text-gray-300">
                  {t('dailyTracking.brushingEvening')}
                </span>
              </div>
              <button
                onClick={() => updateDailyTracking(selectedDate, 'brushingEvening', !selectedTracking?.brushingEvening)}
                className={`
                  w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 active:scale-95
                  ${selectedTracking?.brushingEvening
                    ? 'bg-green-500 border-green-500 text-white shadow-lg'
                    : 'border-gray-300 dark:border-gray-600 hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
                  }
                `}
              >
                {selectedTracking?.brushingEvening && <Check className="w-5 h-5" />}
              </button>
            </div>

            {/* Contrôle des bagues */}
            <div className="flex items-center justify-between p-3 surface-secondary dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <Zap className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-adaptive-secondary dark:text-gray-300">
                  {t('dailyTracking.bracesCheck')}
                </span>
              </div>
              <button
                onClick={() => updateDailyTracking(selectedDate, 'bracesCheck', !selectedTracking?.bracesCheck)}
                className={`
                  w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 hover:scale-110 active:scale-95
                  ${selectedTracking?.bracesCheck
                    ? 'bg-green-500 border-green-500 text-white shadow-lg'
                    : 'border-gray-300 dark:border-gray-600 hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
                  }
                `}
              >
                {selectedTracking?.bracesCheck && <Check className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Légende */}
          <div className="mt-4 flex items-center gap-4 text-xs text-adaptive-tertiary dark:text-gray-400">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-100 dark:bg-green-900/30 rounded"></div>
              <span>{t('dailyTracking.legend.complete')}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-yellow-100 dark:bg-yellow-900/30 rounded"></div>
              <span>{t('dailyTracking.legend.partial')}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 surface-tertiary dark:bg-gray-700 rounded"></div>
              <span>{t('dailyTracking.legend.notDone')}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DailyTrackingCalendar;
