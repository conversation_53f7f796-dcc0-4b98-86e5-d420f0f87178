# OrthoProgress - Plateforme de Suivi Orthodontique

OrthoProgress est une application web moderne dédiée au suivi orthodontique, offrant des interfaces spécialisées pour les patients et les praticiens.

## 🚀 Fonctionnalités

### Pour les Patients
- **Tableau de bord personnalisé** - Vue d'ensemble de votre traitement
- **Suivi quotidien** - Enregistrement du port d'appareil et du brossage
- **Dossier médical** - Accès à votre historique de traitement
- **Gestion des rendez-vous** - Planification et suivi des consultations
- **Photos de suivi** - Envoi de photos à votre praticien
- **Rappels personnalisés** - Notifications pour vos soins
- **Ressources éducatives** - Guides et conseils orthodontiques
- **Communauté** - Échanges avec d'autres patients
- **Analytiques** - Visualisation de vos progrès

### Pour les Praticiens
- **Gestion des patients** - Base de données complète des patients
- **Calendrier de rendez-vous** - Planification et organisation
- **Gestion Invisalign** - Création et suivi des cas Invisalign
- **Communications** - Messagerie sécurisée avec les patients
- **Facturation** - Gestion financière intégrée
- **Abonnement** - Gestion de l'abonnement (24€/mois)
- **Analytiques avancées** - Statistiques de pratique
- **Rapports** - Génération de rapports détaillés
- **Communauté professionnelle** - Échanges entre praticiens

## 🛠️ Technologies Utilisées

- **Frontend**: React 18 + TypeScript
- **Routing**: React Router DOM v6
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Date Handling**: date-fns

## 📦 Installation

1. **Cloner le repository**
```bash
git clone <repository-url>
cd orthoprogress
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Lancer l'application en développement**
```bash
npm run dev
```

4. **Construire pour la production**
```bash
npm run build
```

## 🏗️ Structure du Projet

```
src/
├── components/           # Composants réutilisables
│   ├── Auth/            # Composants d'authentification
│   └── Layout/          # Composants de mise en page
├── contexts/            # Contextes React (Auth, Theme, Language)
├── pages/               # Pages de l'application
│   ├── patient/         # Pages spécifiques aux patients
│   └── practitioner/    # Pages spécifiques aux praticiens
├── types/               # Définitions TypeScript
└── App.tsx             # Composant principal avec routing
```

## 🔐 Authentification

L'application supporte deux types d'utilisateurs :

### Patients
- Connexion via **code patient** fourni par le praticien
- Interface simplifiée et intuitive

### Praticiens
- Connexion via **email et mot de passe**
- Interface complète de gestion

## 🎨 Thèmes et Langues

- **Mode sombre/clair** - Basculement automatique ou manuel
- **Multilingue** - Support français avec possibilité d'extension
- **Design responsive** - Optimisé pour tous les appareils

## 📱 Pages Principales

### Interface Patient
- `/patient/dashboard` - Tableau de bord
- `/patient/suivi` - Suivi quotidien avec calendrier interactif
- `/patient/dossier` - Dossier médical
- `/patient/rendez-vous` - Gestion des rendez-vous
- `/patient/photos` - Envoi de photos
- `/patient/rappels` - Gestion des rappels
- `/patient/education` - Ressources éducatives
- `/patient/communaute` - Forum communautaire
- `/patient/analytiques` - Statistiques personnelles

### Interface Praticien
- `/practitioner/dashboard` - Tableau de bord avec statistiques
- `/practitioner/patients` - Gestion complète des patients
- `/practitioner/appointments` - Calendrier de rendez-vous
- `/practitioner/invisalign` - Gestion des cas Invisalign
- `/practitioner/messages` - Communications
- `/practitioner/billing` - Facturation
- `/practitioner/subscription` - Gestion de l'abonnement
- `/practitioner/analytics` - Analytiques de pratique
- `/practitioner/reports` - Génération de rapports

## 💰 Modèle d'Abonnement

- **Abonnement mensuel** : 24€/mois
- **Abonnement annuel** : 119€/an (économie de 5 mois)
- **Fonctionnalités incluses** :
  - Gestion illimitée de patients
  - Suivi en temps réel
  - Support technique
  - Sauvegarde cloud
  - Intégration Invisalign

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` à la racine :
```env
VITE_API_URL=http://localhost:3001
VITE_APP_NAME=OrthoProgress
```

### Tailwind CSS
Configuration personnalisée dans `tailwind.config.js` avec :
- Palette de couleurs orthodontiques
- Composants personnalisés
- Animations fluides

## 🚀 Déploiement

### Build de production
```bash
npm run build
```

### Prévisualisation
```bash
npm run preview
```

### Déploiement recommandé
- **Vercel** - Déploiement automatique depuis Git
- **Netlify** - Configuration simple avec redirections SPA
- **AWS S3 + CloudFront** - Pour une solution scalable

## 🧪 Tests

```bash
# Lancer les tests
npm run test

# Tests avec couverture
npm run test:coverage

# Linting
npm run lint
```

## 📈 Fonctionnalités Avancées

### Suivi Quotidien
- Calendrier interactif
- Enregistrement des heures de port
- Suivi du brossage
- Statistiques hebdomadaires

### Gestion Invisalign
- Création de cas avec upload de fichiers
- Suivi du statut de fabrication
- Intégration avec le workflow Invisalign
- Notifications de livraison

### Communauté
- Forum par catégories (questions, expériences, conseils)
- Système de likes et commentaires
- Modération automatique
- Badges et gamification

## 🔒 Sécurité

- Authentification sécurisée
- Protection des routes
- Validation des données
- Chiffrement des communications
- Conformité RGPD

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

- **Email** : <EMAIL>
- **Documentation** : [docs.orthoprogress.com](https://docs.orthoprogress.com)
- **Issues** : [GitHub Issues](https://github.com/orthoprogress/issues)

## 🗺️ Roadmap

### Version 2.0
- [ ] Application mobile native
- [ ] Intégration IA pour analyse de photos
- [ ] Téléconsultation vidéo intégrée
- [ ] API publique pour intégrations tierces

### Version 2.1
- [ ] Support multi-cliniques
- [ ] Système de facturation avancé
- [ ] Rapports personnalisables
- [ ] Intégration avec logiciels dentaires

---

**OrthoProgress** - Révolutionnons ensemble le suivi orthodontique ! 🦷✨
