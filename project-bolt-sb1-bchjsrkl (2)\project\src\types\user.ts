export type UserRole = 'patient' | 'practitioner' | 'admin';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  profileImage?: string;
  createdAt?: Date;
  lastLogin?: Date;
}

export interface Patient extends User {
  role: 'patient';
  practitionerId?: string;
  linkCode?: string;
  medicalRecord?: {
    id: string;
    lastUpdated: Date;
    history: string;
    allergies: string[];
    currentTreatments: string[];
    notes: string;
  };
  treatmentProgress?: {
    startDate: Date;
    estimatedEndDate: Date;
    currentPhase: string;
    dailyTracking: {
      date: Date;
      applianceWear: boolean;
      brushingMorning: boolean;
      brushingEvening: boolean;
      notes?: string;
    }[];
  };
  appointments?: {
    id: string;
    date: Date;
    type: 'regular' | 'emergency' | 'video';
    status: 'scheduled' | 'completed' | 'cancelled';
    notes?: string;
  }[];
  photos?: {
    id: string;
    url: string;
    type: 'intraoral' | 'extraoral';
    date: Date;
    notes?: string;
  }[];
  invoices?: {
    id: string;
    date: Date;
    amount: number;
    status: 'pending' | 'paid' | 'overdue';
    description: string;
  }[];
}

export interface Practitioner extends User {
  role: 'practitioner';
  specialization?: string;
  licenseNumber?: string;
  practice?: {
    name: string;
    address: string;
    phone: string;
    email: string;
  };
  subscription?: {
    status: 'active' | 'inactive' | 'trial';
    plan: 'monthly' | 'annual';
    expiresAt: Date;
  };
  patients?: string[]; // Array of patient IDs
  generatedCodes?: {
    code: string;
    createdAt: Date;
    expiresAt: Date;
    used: boolean;
    usedBy?: string;
  }[];
  invisalignCases?: {
    id: string;
    patientId: string;
    status: 'draft' | 'submitted' | 'in_progress' | 'completed';
    submittedAt?: Date;
    completedAt?: Date;
    notes?: string;
  }[];
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'patient' | 'practitioner';
  confirmPassword: string;
  linkCode?: string; // For patients linking to practitioners
}
