import React from 'react';

const App: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#333', textAlign: 'center' }}>
        🎉 OrthoProgress - Test Minimal
      </h1>
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        margin: '20px auto',
        maxWidth: '600px'
      }}>
        <h2>✅ React fonctionne !</h2>
        <p>Si vous voyez ce message, React se charge correctement.</p>
        <p>Timestamp: {new Date().toLocaleString()}</p>
        
        <div style={{ marginTop: '20px' }}>
          <h3>🔍 Tests de base :</h3>
          <ul>
            <li>✅ React se monte dans #root</li>
            <li>✅ JavaScript s'exécute</li>
            <li>✅ Styles CSS appliqués</li>
            <li>✅ Date dynamique affichée</li>
          </ul>
        </div>
        
        <div style={{ 
          marginTop: '20px', 
          padding: '10px', 
          backgroundColor: '#e8f5e8',
          borderRadius: '4px'
        }}>
          <strong>🎯 Prochaine étape :</strong> Identifier quel composant cause l'écran blanc
        </div>
      </div>
    </div>
  );
};

export default App;
