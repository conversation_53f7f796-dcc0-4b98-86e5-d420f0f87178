import React, { useState, useEffect } from 'react';
import { Upload, Image as ImageIcon, Calendar, Filter, Plus, X, ZoomIn, Download, Share2, Trash2, BarChart3 } from 'lucide-react';
import { photoService, Photo } from '../../services/photoService';
import { useAuth } from '../../contexts/AuthContext';

const Photos: React.FC = () => {
  const { user } = useAuth();
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [analysis, setAnalysis] = useState<any>(null);
  const [selectedType, setSelectedType] = useState<'all' | 'intraoral' | 'extraoral'>('all');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState<Photo | null>(null);
  const [uploadData, setUploadData] = useState({
    type: 'intraoral' as 'intraoral' | 'extraoral',
    notes: '',
    file: null as File | null,
  });
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadData(prev => ({ ...prev, file }));
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!uploadData.file || !user) return;

    try {
      const newPhoto = await photoService.uploadPhoto({
        file: uploadData.file,
        type: uploadData.type,
        notes: uploadData.notes,
        patientId: user.id,
      });

      setPhotos(prev => [newPhoto, ...prev]);
      setShowUploadModal(false);
      setUploadData({
        type: 'intraoral',
        notes: '',
        file: null,
      });
      setPreviewUrl(null);
    } catch (error) {
      console.error('Erreur lors du téléversement:', error);
    }
  };

  useEffect(() => {
    const loadPhotos = async () => {
      if (!user) return;
      
      try {
        const userPhotos = await photoService.getPatientPhotos(user.id);
        setPhotos(userPhotos);
        
        const progressAnalysis = await photoService.analyzeProgress(user.id);
        setAnalysis(progressAnalysis);
      } catch (error) {
        console.error('Erreur lors du chargement des photos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPhotos();
  }, [user]);

  const handleDeletePhoto = async (photoId: string) => {
    try {
      await photoService.deletePhoto(photoId);
      setPhotos(prev => prev.filter(photo => photo.id !== photoId));
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  const handleSharePhoto = async (photo: Photo) => {
    try {
      const shareUrl = await photoService.sharePhoto(photo);
      navigator.clipboard.writeText(shareUrl);
      alert('Lien de partage copié dans le presse-papiers !');
    } catch (error) {
      console.error('Erreur lors du partage:', error);
    }
  };

  const filteredPhotos = photos.filter(
    photo => selectedType === 'all' || photo.type === selectedType
  );

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-adaptive dark:text-white">Photos</h1>
            <p className="text-adaptive-secondary dark:text-gray-400">
              Gérez vos photos de suivi orthodontique
            </p>
          </div>
          <button
            onClick={() => setShowUploadModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Ajouter des photos
          </button>
        </div>
      </div>

      {/* Analyse de progression */}
      {analysis && (
        <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-6 mb-6">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-6 h-6 text-blue-500 mr-3" />
            <h2 className="text-xl font-semibold text-adaptive dark:text-white">
              Analyse de progression
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {analysis.totalPhotos}
              </div>
              <div className="text-sm text-adaptive-secondary dark:text-gray-400">
                Photos totales
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {analysis.intraoralCount}
              </div>
              <div className="text-sm text-adaptive-secondary dark:text-gray-400">
                Intra-orales
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {analysis.extraoralCount}
              </div>
              <div className="text-sm text-adaptive-secondary dark:text-gray-400">
                Extra-orales
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {analysis.progressScore}%
              </div>
              <div className="text-sm text-adaptive-secondary dark:text-gray-400">
                Score de progression
              </div>
            </div>
          </div>

          {analysis.recommendations.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-adaptive-secondary dark:text-gray-300 mb-2">
                Recommandations
              </h3>
              <ul className="space-y-1">
                {analysis.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-sm text-adaptive-secondary dark:text-gray-400 flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Filtres */}
      <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-4 mb-6">
        <div className="flex gap-3">
          <button
            onClick={() => setSelectedType('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedType === 'all'
                ? 'bg-blue-500 text-white'
                : 'surface-tertiary dark:bg-gray-700 text-adaptive-secondary dark:text-gray-300 hover:surface-tertiary dark:hover:bg-gray-600'
            }`}
          >
            Toutes les photos
          </button>
          <button
            onClick={() => setSelectedType('intraoral')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedType === 'intraoral'
                ? 'bg-blue-500 text-white'
                : 'surface-tertiary dark:bg-gray-700 text-adaptive-secondary dark:text-gray-300 hover:surface-tertiary dark:hover:bg-gray-600'
            }`}
          >
            Photos intra-orales
          </button>
          <button
            onClick={() => setSelectedType('extraoral')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedType === 'extraoral'
                ? 'bg-blue-500 text-white'
                : 'surface-tertiary dark:bg-gray-700 text-adaptive-secondary dark:text-gray-300 hover:surface-tertiary dark:hover:bg-gray-600'
            }`}
          >
            Photos extra-orales
          </button>
        </div>
      </div>

      {/* Grille de photos */}
      {filteredPhotos.length === 0 ? (
        <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-8 text-center">
          <ImageIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-adaptive dark:text-white mb-2">
            Aucune photo
          </h3>
          <p className="text-adaptive-secondary dark:text-gray-400 mb-4">
            Commencez à ajouter des photos pour suivre votre progression
          </p>
          <button
            onClick={() => setShowUploadModal(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Ajouter des photos
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPhotos.map(photo => (
            <div
              key={photo.id}
              className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 overflow-hidden group"
            >
              <div className="relative aspect-video">
                <img
                  src={photo.url}
                  alt="Photo orthodontique"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <button
                    onClick={() => setShowViewModal(photo)}
                    className="p-2 surface-primary rounded-full text-adaptive hover:surface-tertiary transition-colors"
                  >
                    <ZoomIn className="w-5 h-5" />
                  </button>
                </div>
              </div>
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    photo.type === 'intraoral'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:bg-blue-900/20 dark:text-blue-300'
                      : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:bg-purple-900/20 dark:text-purple-300'
                  }`}>
                    {photo.type === 'intraoral' ? 'Intra-orale' : 'Extra-orale'}
                  </span>
                  <span className="text-sm text-adaptive-tertiary dark:text-gray-400 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(photo.date).toLocaleDateString()}
                  </span>
                </div>
                {photo.notes && (
                  <p className="text-sm text-adaptive-secondary dark:text-gray-400 mb-3">
                    {photo.notes}
                  </p>
                )}
                
                {/* Actions */}
                <div className="flex items-center justify-between pt-3 border-t border-adaptive-light dark:border-gray-600">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSharePhoto(photo)}
                      className="p-2 text-adaptive-tertiary hover:text-blue-500 transition-colors"
                      title="Partager"
                    >
                      <Share2 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => window.open(photo.url, '_blank')}
                      className="p-2 text-adaptive-tertiary hover:text-green-500 transition-colors"
                      title="Télécharger"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                  <button
                    onClick={() => handleDeletePhoto(photo.id)}
                    className="p-2 text-adaptive-tertiary hover:text-red-500 transition-colors"
                    title="Supprimer"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal d'upload */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary dark:bg-gray-800 rounded-xl p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-adaptive dark:text-white">
                Ajouter des photos
              </h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setUploadData({ type: 'intraoral', notes: '', file: null });
                  setPreviewUrl(null);
                }}
                className="text-adaptive-tertiary hover:text-adaptive-secondary dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Type de photo */}
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary dark:text-gray-300 mb-2">
                  Type de photo
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setUploadData(prev => ({ ...prev, type: 'intraoral' }))}
                    className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                      uploadData.type === 'intraoral'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'border-adaptive dark:border-gray-600 hover:border-adaptive dark:hover:border-gray-500'
                    }`}
                  >
                    Intra-orale
                  </button>
                  <button
                    type="button"
                    onClick={() => setUploadData(prev => ({ ...prev, type: 'extraoral' }))}
                    className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                      uploadData.type === 'extraoral'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'border-adaptive dark:border-gray-600 hover:border-adaptive dark:hover:border-gray-500'
                    }`}
                  >
                    Extra-orale
                  </button>
                </div>
              </div>

              {/* Upload de fichier */}
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary dark:text-gray-300 mb-2">
                  Photo
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-adaptive dark:border-gray-600 border-dashed rounded-lg">
                  <div className="space-y-1 text-center">
                    {previewUrl ? (
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Aperçu"
                          className="mx-auto h-32 w-auto rounded-lg"
                        />
                        <button
                          onClick={() => {
                            setPreviewUrl(null);
                            setUploadData(prev => ({ ...prev, file: null }));
                          }}
                          className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <>
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-adaptive-secondary dark:text-gray-400">
                          <label
                            htmlFor="file-upload"
                            className="relative cursor-pointer rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 focus-within:outline-none"
                          >
                            <span>Téléverser une photo</span>
                            <input
                              id="file-upload"
                              name="file-upload"
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={handleFileSelect}
                            />
                          </label>
                          <p className="pl-1">ou glisser-déposer</p>
                        </div>
                        <p className="text-xs text-adaptive-tertiary dark:text-gray-400">
                          PNG, JPG jusqu'à 10MB
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary dark:text-gray-300 mb-2">
                  Notes (optionnel)
                </label>
                <textarea
                  value={uploadData.notes}
                  onChange={(e) => setUploadData(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 border border-adaptive dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  rows={3}
                  placeholder="Ajoutez des notes ou commentaires sur cette photo..."
                />
              </div>

              {/* Boutons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowUploadModal(false);
                    setUploadData({ type: 'intraoral', notes: '', file: null });
                    setPreviewUrl(null);
                  }}
                  className="px-4 py-2 text-adaptive-secondary dark:text-gray-300 hover:text-adaptive dark:hover:text-white transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={handleUpload}
                  disabled={!uploadData.file}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Téléverser
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de visualisation */}
      {showViewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary dark:bg-gray-800 rounded-xl p-6 w-full max-w-4xl mx-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  showViewModal.type === 'intraoral'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:bg-blue-900/20 dark:text-blue-300'
                    : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300 dark:bg-purple-900/20 dark:text-purple-300'
                }`}>
                  {showViewModal.type === 'intraoral' ? 'Intra-orale' : 'Extra-orale'}
                </span>
                <span className="ml-3 text-sm text-adaptive-tertiary dark:text-gray-400">
                  {new Date(showViewModal.date).toLocaleDateString()}
                </span>
              </div>
              <button
                onClick={() => setShowViewModal(null)}
                className="text-adaptive-tertiary hover:text-adaptive-secondary dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="relative aspect-video mb-4">
              <img
                src={showViewModal.url}
                alt="Photo orthodontique"
                className="w-full h-full object-contain rounded-lg"
              />
            </div>

            {showViewModal.notes && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-adaptive-secondary dark:text-gray-300 mb-2">
                  Notes
                </h4>
                <p className="text-adaptive-secondary dark:text-gray-400">
                  {showViewModal.notes}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Photos;
