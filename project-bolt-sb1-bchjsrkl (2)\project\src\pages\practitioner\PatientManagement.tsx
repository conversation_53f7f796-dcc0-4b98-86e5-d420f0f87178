import React, { useState } from 'react';
import { Search, Plus, Eye, Edit, MessageSquare, Calendar, Phone, Mail } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

interface Patient {
  id: string;
  name: string;
  email: string;
  phone: string;
  treatmentType: string;
  startDate: string;
  nextAppointment: string;
  status: 'active' | 'completed' | 'paused';
  progress: number;
  patientCode: string;
}

const PatientManagement: React.FC = () => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);

  const patients: Patient[] = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+33 6 12 34 56 78',
      treatmentType: 'Invisalign',
      startDate: '2024-01-15',
      nextAppointment: '2024-06-20',
      status: 'active',
      progress: 65,
      patientCode: 'MD2024001'
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+33 6 98 76 54 32',
      treatmentType: 'Bagues traditionnelles',
      startDate: '2023-09-10',
      nextAppointment: '2024-06-18',
      status: 'active',
      progress: 80,
      patientCode: 'PM2023002'
    },
    {
      id: '3',
      name: 'Sophie Laurent',
      email: '<EMAIL>',
      phone: '+33 6 11 22 33 44',
      treatmentType: 'Invisalign',
      startDate: '2023-03-20',
      nextAppointment: '',
      status: 'completed',
      progress: 100,
      patientCode: 'SL2023003'
    }
  ];

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.patientCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || patient.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  // Gestionnaires d'événements pour les boutons
  const handleViewDetails = (patientId: string) => {
    console.log('Voir détails du patient:', patientId);
    // TODO: Implémenter la navigation vers les détails du patient
  };

  const handleEditPatient = (patientId: string) => {
    console.log('Modifier patient:', patientId);
    // TODO: Implémenter l'édition du patient
  };

  const handleSendMessage = (patientId: string) => {
    console.log('Envoyer message au patient:', patientId);
    // TODO: Implémenter l'envoi de message
  };

  const handleScheduleAppointment = (patientId: string) => {
    console.log('Planifier RDV pour patient:', patientId);
    // TODO: Implémenter la planification de RDV
  };



  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return t('patients.active');
      case 'completed': return t('patients.completed');
      case 'paused': return t('patients.paused');
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'completed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'paused': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default: return 'surface-tertiary text-adaptive';
    }
  };



  const generatePatientCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-adaptive">{t('patients.title')}</h1>
            <p className="text-adaptive-secondary">{t('patients.subtitle')}</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            {t('patients.newPatient')}
          </button>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={t('patients.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
              />
            </div>
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
          >
            <option value="all">{t('patients.allStatuses')}</option>
            <option value="active">{t('patients.active')}</option>
            <option value="completed">{t('patients.completed')}</option>
            <option value="paused">{t('patients.paused')}</option>
          </select>
        </div>
      </div>

      {/* Liste des patients */}
      <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light">
        <div className="p-6 border-b border-adaptive-light">
          <h2 className="text-xl font-semibold text-adaptive">
            {t('patients.title')} ({filteredPatients.length})
          </h2>
        </div>

        <div className="divide-y divide-gray-100">
          {filteredPatients.map((patient) => (
            <div key={patient.id} className="p-6 hover:surface-secondary transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h3 className="text-lg font-semibold text-adaptive mr-3">
                      {patient.name}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(patient.status)}`}>
                      {getStatusText(patient.status)}
                    </span>
                    <span className="ml-3 px-2 py-1 surface-tertiary text-adaptive-secondary rounded text-xs font-mono">
                      {patient.patientCode}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-adaptive-secondary">
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 mr-2" />
                      {patient.email}
                    </div>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      {patient.phone}
                    </div>
                    <div>
                      <strong>{t('patients.treatmentType')}:</strong> {patient.treatmentType}
                    </div>
                    <div>
                      <strong>{t('patients.startDate')}:</strong> {new Date(patient.startDate).toLocaleDateString('fr-FR')}
                    </div>
                  </div>

                  {patient.nextAppointment && (
                    <div className="mt-2 text-sm text-blue-600">
                      <Calendar className="w-4 h-4 inline mr-1" />
                      {t('patients.nextAppointment')}: {new Date(patient.nextAppointment).toLocaleDateString('fr-FR')}
                    </div>
                  )}

                  <div className="mt-3">
                    <div className="flex items-center justify-between text-sm text-adaptive-secondary mb-1">
                      <span>{t('patients.treatmentProgress')}</span>
                      <span>{patient.progress}%</span>
                    </div>
                    <div className="w-full surface-tertiary rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${patient.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-6">
                  <button
                    onClick={() => handleViewDetails(patient.id)}
                    className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                    title={t('patients.viewDetails')}
                  >
                    <Eye className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleEditPatient(patient.id)}
                    className="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors"
                    title={t('patients.editPatient')}
                  >
                    <Edit className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleSendMessage(patient.id)}
                    className="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors"
                    title={t('patients.sendMessage')}
                  >
                    <MessageSquare className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleScheduleAppointment(patient.id)}
                    className="p-2 text-gray-400 hover:text-orange-500 hover:bg-orange-50 rounded-lg transition-colors"
                    title={t('patients.scheduleAppointment')}
                  >
                    <Calendar className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal d'ajout de patient */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="surface-primary rounded-xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-semibold text-adaptive mb-4">{t('patients.newPatient')}</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  Nom complet
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  placeholder="Nom du patient"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  {t('patients.email')}
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  {t('patients.phone')}
                </label>
                <input
                  type="tel"
                  className="w-full px-3 py-2 border border-adaptive rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent surface-primary text-adaptive"
                  placeholder="+33 6 12 34 56 78"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-adaptive-secondary mb-1">
                  {t('patients.patientCode')} (généré automatiquement)
                </label>
                <input
                  type="text"
                  value={generatePatientCode()}
                  readOnly
                  className="w-full px-3 py-2 border border-adaptive rounded-lg surface-secondary text-adaptive font-mono"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-adaptive-secondary hover:text-adaptive transition-colors"
              >
                {t('invisalign.cancel')}
              </button>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  alert('Patient ajouté avec succès !');
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientManagement;
