import React, { useState, useEffect } from 'react';
import { BookOpen, Video, FileText, Search, Filter, Play, Clock, Star, Download, ExternalLink } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface EducationalContent {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'guide' | 'infographic';
  category: 'orthodontics' | 'hygiene' | 'nutrition' | 'lifestyle' | 'treatment';
  duration?: number; // en minutes pour les vidéos
  readTime?: number; // en minutes pour les articles
  rating: number;
  views: number;
  thumbnail?: string;
  url?: string;
  downloadUrl?: string;
  author: string;
  publishedAt: string;
  tags: string[];
  featured: boolean;
}

const Education: React.FC = () => {
  const { user } = useAuth();
  const [content, setContent] = useState<EducationalContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  // Données simulées
  useEffect(() => {
    const mockContent: EducationalContent[] = [
      {
        id: '1',
        title: 'Guide complet du traitement Invisalign',
        description: 'Tout ce que vous devez savoir sur votre traitement orthodontique avec les gouttières transparentes.',
        type: 'guide',
        category: 'orthodontics',
        readTime: 15,
        rating: 4.8,
        views: 1250,
        thumbnail: 'https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400',
        author: 'Dr. Marie Dubois',
        publishedAt: '2024-01-15',
        tags: ['invisalign', 'traitement', 'orthodontie'],
        featured: true
      },
      {
        id: '2',
        title: 'Techniques de brossage pour porteurs d\'appareils',
        description: 'Apprenez les meilleures techniques pour maintenir une hygiène dentaire parfaite pendant votre traitement.',
        type: 'video',
        category: 'hygiene',
        duration: 8,
        rating: 4.9,
        views: 2100,
        thumbnail: 'https://images.unsplash.com/photo-1588776814546-1ffcf47267a5?w=400',
        url: 'https://youtube.com/watch?v=example',
        author: 'Dr. Pierre Martin',
        publishedAt: '2024-02-01',
        tags: ['brossage', 'hygiène', 'soins'],
        featured: true
      },
      {
        id: '3',
        title: 'Alimentation et orthodontie : ce qu\'il faut savoir',
        description: 'Découvrez quels aliments privilégier et lesquels éviter pendant votre traitement orthodontique.',
        type: 'article',
        category: 'nutrition',
        readTime: 10,
        rating: 4.6,
        views: 890,
        thumbnail: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400',
        author: 'Nutritionniste Sophie Leroy',
        publishedAt: '2024-01-20',
        tags: ['alimentation', 'nutrition', 'conseils'],
        featured: false
      },
      {
        id: '4',
        title: 'Infographie : Les étapes du traitement orthodontique',
        description: 'Visualisez les différentes phases de votre traitement orthodontique.',
        type: 'infographic',
        category: 'orthodontics',
        rating: 4.7,
        views: 1500,
        thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400',
        downloadUrl: '/downloads/etapes-traitement.pdf',
        author: 'Cabinet Orthodontique Moderne',
        publishedAt: '2024-01-10',
        tags: ['étapes', 'traitement', 'infographie'],
        featured: false
      },
      {
        id: '5',
        title: 'Gestion de la douleur pendant le traitement',
        description: 'Conseils pratiques pour gérer l\'inconfort et la douleur liés au traitement orthodontique.',
        type: 'article',
        category: 'treatment',
        readTime: 12,
        rating: 4.5,
        views: 750,
        thumbnail: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400',
        author: 'Dr. Jean Dupont',
        publishedAt: '2024-02-10',
        tags: ['douleur', 'confort', 'conseils'],
        featured: false
      },
      {
        id: '6',
        title: 'Sport et orthodontie : restez actif en sécurité',
        description: 'Comment continuer à pratiquer vos activités sportives favorites pendant votre traitement.',
        type: 'video',
        category: 'lifestyle',
        duration: 12,
        rating: 4.4,
        views: 650,
        thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        url: 'https://youtube.com/watch?v=example2',
        author: 'Coach Sportif Thomas',
        publishedAt: '2024-02-05',
        tags: ['sport', 'activité', 'protection'],
        featured: false
      }
    ];

    setContent(mockContent);
    setLoading(false);
  }, []);

  const filteredContent = content
    .filter(item => 
      selectedCategory === 'all' || item.category === selectedCategory
    )
    .filter(item => 
      selectedType === 'all' || item.type === selectedType
    )
    .filter(item =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );

  const featuredContent = content.filter(item => item.featured);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'article':
        return <FileText className="w-5 h-5" />;
      case 'guide':
        return <BookOpen className="w-5 h-5" />;
      case 'infographic':
        return <FileText className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'video':
        return 'Vidéo';
      case 'article':
        return 'Article';
      case 'guide':
        return 'Guide';
      case 'infographic':
        return 'Infographie';
      default:
        return type;
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'orthodontics':
        return 'Orthodontie';
      case 'hygiene':
        return 'Hygiène';
      case 'nutrition':
        return 'Nutrition';
      case 'lifestyle':
        return 'Mode de vie';
      case 'treatment':
        return 'Traitement';
      default:
        return category;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}min` : ''}`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">Éducation</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Accédez à des ressources éducatives pour optimiser votre traitement
        </p>
      </div>

      {/* Contenu en vedette */}
      {featuredContent.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Contenu en vedette
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredContent.map(item => (
              <div
                key={item.id}
                className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white relative overflow-hidden"
              >
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    {getTypeIcon(item.type)}
                    <span className="ml-2 text-sm font-medium opacity-90">
                      {getTypeText(item.type)}
                    </span>
                    <Star className="w-4 h-4 ml-auto text-yellow-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                  <p className="text-blue-100 mb-4 line-clamp-2">{item.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm opacity-90">
                      <Clock className="w-4 h-4 mr-1" />
                      {item.duration ? formatDuration(item.duration) : `${item.readTime} min de lecture`}
                    </div>
                    <button className="px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors">
                      Voir
                    </button>
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filtres et recherche */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Rechercher du contenu éducatif..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex gap-3">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Toutes les catégories</option>
              <option value="orthodontics">Orthodontie</option>
              <option value="hygiene">Hygiène</option>
              <option value="nutrition">Nutrition</option>
              <option value="lifestyle">Mode de vie</option>
              <option value="treatment">Traitement</option>
            </select>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Tous les types</option>
              <option value="video">Vidéos</option>
              <option value="article">Articles</option>
              <option value="guide">Guides</option>
              <option value="infographic">Infographies</option>
            </select>
          </div>
        </div>
      </div>

      {/* Grille de contenu */}
      {filteredContent.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 text-center">
          <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Aucun contenu trouvé
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Essayez de modifier vos filtres ou votre recherche
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContent.map(item => (
            <div
              key={item.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden group hover:shadow-md transition-shadow"
            >
              {item.thumbnail && (
                <div className="relative aspect-video">
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-full object-cover"
                  />
                  {item.type === 'video' && (
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Play className="w-12 h-12 text-white" />
                    </div>
                  )}
                  <div className="absolute top-3 left-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.type === 'video' ? 'bg-red-500 text-white' :
                      item.type === 'article' ? 'bg-blue-500 text-white' :
                      item.type === 'guide' ? 'bg-green-500 text-white' :
                      'bg-purple-500 text-white'
                    }`}>
                      {getTypeText(item.type)}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                    {getCategoryText(item.category)}
                  </span>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Star className="w-4 h-4 mr-1 text-yellow-500" />
                    {item.rating}
                  </div>
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                  {item.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                  {item.description}
                </p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {item.duration ? formatDuration(item.duration) : `${item.readTime} min`}
                  </div>
                  <div>{item.views} vues</div>
                </div>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {item.tags.slice(0, 3).map(tag => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-600">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Par {item.author}
                  </div>
                  <div className="flex gap-2">
                    {item.downloadUrl && (
                      <button
                        onClick={() => window.open(item.downloadUrl, '_blank')}
                        className="p-2 text-gray-500 hover:text-green-500 transition-colors"
                        title="Télécharger"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => item.url ? window.open(item.url, '_blank') : null}
                      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                    >
                      {item.type === 'video' ? 'Regarder' : 'Lire'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Education;
