import React, { useState, useEffect } from 'react';
import { Trophy, Star, Gift, Target, Calendar, Award, Zap, Crown, Medal, Gem, Lock, CheckCircle, Clock, TrendingUp } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  points: number;
  unlocked: boolean;
  unlockedDate?: string;
  category: 'daily' | 'weekly' | 'monthly' | 'milestone';
  progress?: number;
  maxProgress?: number;
}

interface Reward {
  id: string;
  title: string;
  description: string;
  cost: number;
  category: 'discount' | 'gift' | 'premium' | 'consultation';
  available: boolean;
  claimed: boolean;
  image?: string;
}

interface UserStats {
  totalPoints: number;
  level: number;
  streak: number;
  completedAchievements: number;
  totalAchievements: number;
  nextLevelPoints: number;
  currentLevelPoints: number;
}

const Rewards: React.FC = () => {
  const { user } = useAuth();
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [activeTab, setActiveTab] = useState<'achievements' | 'rewards' | 'leaderboard'>('achievements');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const mockUserStats: UserStats = {
      totalPoints: 2450,
      level: 8,
      streak: 12,
      completedAchievements: 15,
      totalAchievements: 25,
      nextLevelPoints: 3000,
      currentLevelPoints: 2000
    };

    const mockAchievements: Achievement[] = [
      {
        id: '1',
        title: 'Premier pas',
        description: 'Complétez votre premier suivi quotidien',
        icon: <Star className="w-6 h-6" />,
        points: 50,
        unlocked: true,
        unlockedDate: '2024-01-15',
        category: 'milestone'
      },
      {
        id: '2',
        title: 'Régularité',
        description: 'Portez vos gouttières 7 jours consécutifs',
        icon: <Calendar className="w-6 h-6" />,
        points: 100,
        unlocked: true,
        unlockedDate: '2024-01-22',
        category: 'weekly'
      },
      {
        id: '3',
        title: 'Perfectionniste',
        description: 'Atteignez 22h de port quotidien pendant 5 jours',
        icon: <Target className="w-6 h-6" />,
        points: 200,
        unlocked: true,
        unlockedDate: '2024-02-01',
        category: 'weekly'
      },
      {
        id: '4',
        title: 'Marathonien',
        description: 'Maintenez une série de 30 jours',
        icon: <Trophy className="w-6 h-6" />,
        points: 500,
        unlocked: false,
        category: 'monthly',
        progress: 12,
        maxProgress: 30
      },
      {
        id: '5',
        title: 'Expert en hygiène',
        description: 'Brossez-vous les dents 3 fois par jour pendant 1 semaine',
        icon: <Zap className="w-6 h-6" />,
        points: 150,
        unlocked: true,
        unlockedDate: '2024-01-28',
        category: 'weekly'
      },
      {
        id: '6',
        title: 'Photographe',
        description: 'Prenez 10 photos de suivi',
        icon: <Medal className="w-6 h-6" />,
        points: 100,
        unlocked: false,
        category: 'milestone',
        progress: 7,
        maxProgress: 10
      }
    ];

    const mockRewards: Reward[] = [
      {
        id: '1',
        title: '10% de réduction',
        description: 'Réduction sur votre prochaine consultation',
        cost: 500,
        category: 'discount',
        available: true,
        claimed: false
      },
      {
        id: '2',
        title: 'Kit de nettoyage premium',
        description: 'Kit complet pour l\'entretien de vos gouttières',
        cost: 800,
        category: 'gift',
        available: true,
        claimed: false
      },
      {
        id: '3',
        title: 'Consultation gratuite',
        description: 'Une consultation de suivi offerte',
        cost: 1500,
        category: 'consultation',
        available: true,
        claimed: false
      },
      {
        id: '4',
        title: 'Accès premium 1 mois',
        description: 'Fonctionnalités premium pendant 1 mois',
        cost: 1000,
        category: 'premium',
        available: true,
        claimed: true
      },
      {
        id: '5',
        title: 'Étui de voyage personnalisé',
        description: 'Étui élégant avec vos initiales',
        cost: 1200,
        category: 'gift',
        available: false,
        claimed: false
      }
    ];

    setUserStats(mockUserStats);
    setAchievements(mockAchievements);
    setRewards(mockRewards);
    setLoading(false);
  }, []);

  const getLevelProgress = () => {
    if (!userStats) return 0;
    const progress = userStats.totalPoints - userStats.currentLevelPoints;
    const total = userStats.nextLevelPoints - userStats.currentLevelPoints;
    return (progress / total) * 100;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'daily':
        return <Calendar className="w-4 h-4" />;
      case 'weekly':
        return <Target className="w-4 h-4" />;
      case 'monthly':
        return <Crown className="w-4 h-4" />;
      case 'milestone':
        return <Award className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  const getRewardIcon = (category: string) => {
    switch (category) {
      case 'discount':
        return <Gift className="w-6 h-6 text-green-500" />;
      case 'gift':
        return <Gem className="w-6 h-6 text-purple-500" />;
      case 'premium':
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 'consultation':
        return <Award className="w-6 h-6 text-blue-500" />;
      default:
        return <Gift className="w-6 h-6 text-adaptive-tertiary" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 surface-tertiary dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 surface-tertiary dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-adaptive dark:text-white">Récompenses</h1>
        <p className="text-adaptive-secondary dark:text-gray-400">
          Gagnez des points et débloquez des récompenses
        </p>
      </div>

      {/* Statistiques utilisateur */}
      {userStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Points totaux</p>
                <p className="text-2xl font-bold">{userStats.totalPoints.toLocaleString()}</p>
              </div>
              <Star className="w-8 h-8 text-blue-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Niveau</p>
                <p className="text-2xl font-bold">{userStats.level}</p>
              </div>
              <Crown className="w-8 h-8 text-purple-200" />
            </div>
            <div className="mt-3">
              <div className="bg-purple-400 rounded-full h-2">
                <div 
                  className="surface-primary rounded-full h-2 transition-all duration-300"
                  style={{ width: `${getLevelProgress()}%` }}
                ></div>
              </div>
              <p className="text-xs text-purple-100 mt-1">
                {userStats.nextLevelPoints - userStats.totalPoints} points pour le niveau suivant
              </p>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Série actuelle</p>
                <p className="text-2xl font-bold">{userStats.streak} jours</p>
              </div>
              <Zap className="w-8 h-8 text-green-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Succès</p>
                <p className="text-2xl font-bold">
                  {userStats.completedAchievements}/{userStats.totalAchievements}
                </p>
              </div>
              <Trophy className="w-8 h-8 text-orange-200" />
            </div>
          </div>
        </div>
      )}

      {/* Onglets */}
      <div className="mb-6">
        <div className="border-b border-adaptive dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('achievements')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'achievements'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-adaptive-tertiary dark:text-gray-400 hover:text-adaptive-secondary dark:hover:text-gray-300'
              }`}
            >
              Succès
            </button>
            <button
              onClick={() => setActiveTab('rewards')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'rewards'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-adaptive-tertiary dark:text-gray-400 hover:text-adaptive-secondary dark:hover:text-gray-300'
              }`}
            >
              Boutique
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'leaderboard'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-adaptive-tertiary dark:text-gray-400 hover:text-adaptive-secondary dark:hover:text-gray-300'
              }`}
            >
              Classement
            </button>
          </nav>
        </div>
      </div>

      {/* Succès */}
      {activeTab === 'achievements' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {achievements.map(achievement => (
            <div
              key={achievement.id}
              className={`surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-6 transition-all duration-200 ${
                achievement.unlocked 
                  ? 'ring-2 ring-green-200 dark:ring-green-800' 
                  : 'opacity-75'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-lg ${
                  achievement.unlocked 
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' 
                    : 'surface-tertiary dark:bg-gray-700 text-gray-400'
                }`}>
                  {achievement.unlocked ? achievement.icon : <Lock className="w-6 h-6" />}
                </div>
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(achievement.category)}
                  <span className="text-xs text-adaptive-tertiary dark:text-gray-400 capitalize">
                    {achievement.category}
                  </span>
                </div>
              </div>

              <h3 className={`text-lg font-semibold mb-2 ${
                achievement.unlocked 
                  ? 'text-adaptive dark:text-white' 
                  : 'text-adaptive-tertiary dark:text-gray-400'
              }`}>
                {achievement.title}
              </h3>

              <p className="text-adaptive-secondary dark:text-gray-400 text-sm mb-4">
                {achievement.description}
              </p>

              {achievement.progress !== undefined && achievement.maxProgress && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-adaptive-secondary dark:text-gray-400 mb-1">
                    <span>Progression</span>
                    <span>{achievement.progress}/{achievement.maxProgress}</span>
                  </div>
                  <div className="surface-tertiary dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 rounded-full h-2 transition-all duration-300"
                      style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium text-adaptive dark:text-white">
                    {achievement.points} points
                  </span>
                </div>
                {achievement.unlocked && achievement.unlockedDate && (
                  <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                    <CheckCircle className="w-3 h-3" />
                    <span>Débloqué</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Boutique de récompenses */}
      {activeTab === 'rewards' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rewards.map(reward => (
            <div
              key={reward.id}
              className={`surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700 p-6 ${
                !reward.available ? 'opacity-50' : ''
              } ${reward.claimed ? 'ring-2 ring-green-200 dark:ring-green-800' : ''}`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="p-3 surface-tertiary dark:bg-gray-700 rounded-lg">
                  {getRewardIcon(reward.category)}
                </div>
                {reward.claimed && (
                  <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 px-2 py-1 rounded-full">
                    <CheckCircle className="w-3 h-3" />
                    <span>Réclamé</span>
                  </div>
                )}
              </div>

              <h3 className="text-lg font-semibold text-adaptive dark:text-white mb-2">
                {reward.title}
              </h3>

              <p className="text-adaptive-secondary dark:text-gray-400 text-sm mb-4">
                {reward.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium text-adaptive dark:text-white">
                    {reward.cost} points
                  </span>
                </div>
                <button
                  disabled={!reward.available || reward.claimed || (userStats?.totalPoints || 0) < reward.cost}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    reward.claimed
                      ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 cursor-not-allowed'
                      : !reward.available || (userStats?.totalPoints || 0) < reward.cost
                      ? 'surface-tertiary dark:bg-gray-700 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {reward.claimed ? 'Réclamé' : !reward.available ? 'Indisponible' : 'Échanger'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Classement */}
      {activeTab === 'leaderboard' && (
        <div className="surface-primary dark:bg-gray-800 rounded-xl shadow-sm border border-adaptive-light dark:border-gray-700">
          <div className="p-6 border-b border-adaptive-light dark:border-gray-700">
            <h2 className="text-xl font-semibold text-adaptive dark:text-white">
              Classement mensuel
            </h2>
            <p className="text-adaptive-secondary dark:text-gray-400 text-sm">
              Top des patients les plus assidus ce mois-ci
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {[
                { rank: 1, name: 'Marie L.', points: 3250, streak: 28 },
                { rank: 2, name: 'Pierre D.', points: 2890, streak: 22 },
                { rank: 3, name: 'Sophie M.', points: 2650, streak: 19 },
                { rank: 4, name: 'Vous', points: userStats?.totalPoints || 0, streak: userStats?.streak || 0 },
                { rank: 5, name: 'Lucas B.', points: 2200, streak: 15 }
              ].map((player, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-4 rounded-lg ${
                    player.name === 'Vous' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                      : 'surface-secondary dark:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      player.rank === 1 ? 'bg-yellow-500 text-white' :
                      player.rank === 2 ? 'bg-gray-400 text-white' :
                      player.rank === 3 ? 'bg-orange-500 text-white' :
                      'bg-gray-300 dark:bg-gray-600 text-adaptive-secondary dark:text-gray-300'
                    }`}>
                      {player.rank}
                    </div>
                    <div>
                      <p className={`font-medium ${
                        player.name === 'Vous' 
                          ? 'text-blue-900 dark:text-blue-100' 
                          : 'text-adaptive dark:text-white'
                      }`}>
                        {player.name}
                      </p>
                      <p className="text-sm text-adaptive-tertiary dark:text-gray-400">
                        Série: {player.streak} jours
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      player.name === 'Vous' 
                        ? 'text-blue-900 dark:text-blue-100' 
                        : 'text-adaptive dark:text-white'
                    }`}>
                      {player.points.toLocaleString()} pts
                    </p>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-xs text-green-500">+{Math.floor(Math.random() * 100 + 50)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Rewards;
