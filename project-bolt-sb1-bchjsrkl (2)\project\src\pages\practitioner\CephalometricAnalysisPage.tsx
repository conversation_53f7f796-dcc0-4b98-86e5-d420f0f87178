import React, { useState, useCallback, useRef } from 'react';
import { Upload, Brain, Zap, Eye, Download, Save, RotateCcw, Settings, Target, Layers, Cpu } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import CephalometricAnalysis from '../../components/CephalometricAnalysis';
import { toast } from 'react-hot-toast';

interface AIModel {
  id: string;
  name: string;
  accuracy: number;
  speed: string;
  description: string;
  icon: React.ReactNode;
}

interface AnalysisProtocol {
  id: string;
  name: string;
  description: string;
  landmarks: number;
  measurements: string[];
}

const CephalometricAnalysisPage: React.FC = () => {
  const { t } = useLanguage();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('hrnet-farnet');
  const [selectedProtocol, setSelectedProtocol] = useState<string>('steiner');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const aiModels: AIModel[] = [
    {
      id: 'hrnet-farnet',
      name: 'HRNet + FARNet',
      accuracy: 98.7,
      speed: 'Ultra-rapide',
      description: 'Modèle hybride de pointe combinant HRNet et FARNet pour une précision sub-millimétrique',
      icon: <Brain className="w-5 h-5" />
    },
    {
      id: 'mask-rcnn-vit',
      name: 'Mask R-CNN + ViT',
      accuracy: 97.9,
      speed: 'Rapide',
      description: 'Architecture transformer avec détection d\'instance pour landmarks complexes',
      icon: <Target className="w-5 h-5" />
    },
    {
      id: 'yolo-v8-ceph',
      name: 'YOLOv8 Cephalometric',
      accuracy: 96.8,
      speed: 'Temps réel',
      description: 'Détection en temps réel optimisée pour radiographies céphalométriques',
      icon: <Zap className="w-5 h-5" />
    },
    {
      id: 'deeplab-v3-plus',
      name: 'DeepLab v3+',
      accuracy: 95.5,
      speed: 'Modéré',
      description: 'Segmentation sémantique avancée pour structures anatomiques complexes',
      icon: <Layers className="w-5 h-5" />
    }
  ];

  const analysisProtocols: AnalysisProtocol[] = [
    {
      id: 'steiner',
      name: 'Analyse de Steiner',
      description: 'Analyse classique avec mesures SNA, SNB, ANB et relations dento-squelettiques',
      landmarks: 12,
      measurements: ['SNA', 'SNB', 'ANB', 'SN-GoGn', 'U1-NA', 'L1-NB']
    },
    {
      id: 'ricketts',
      name: 'Analyse de Ricketts',
      description: 'Analyse complète avec 32 mesures pour évaluation tridimensionnelle',
      landmarks: 18,
      measurements: ['Convexité faciale', 'Axe facial', 'Profondeur faciale', 'Plan mandibulaire']
    },
    {
      id: 'tweed',
      name: 'Analyse de Tweed',
      description: 'Triangle de Tweed pour évaluation de la position incisive mandibulaire',
      landmarks: 8,
      measurements: ['FMA', 'FMIA', 'IMPA']
    },
    {
      id: 'mcnamara',
      name: 'Analyse de McNamara',
      description: 'Analyse moderne avec évaluation des tissus mous et esthétique faciale',
      landmarks: 15,
      measurements: ['Position maxillaire', 'Longueur mandibulaire', 'Hauteur faciale']
    },
    {
      id: 'bjork',
      name: 'Björk-Jarabak',
      description: 'Analyse de croissance avec prédiction du pattern de croissance',
      landmarks: 14,
      measurements: ['Ratio de Jarabak', 'Angle goniaque', 'Arc mandibulaire']
    },
    {
      id: 'downs',
      name: 'Analyse de Downs',
      description: 'Première analyse céphalométrique standardisée avec 10 mesures clés',
      landmarks: 10,
      measurements: ['Angle facial', 'Convexité', 'Plan A-B', 'Plan mandibulaire']
    }
  ];

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Fichier trop volumineux. Maximum 10MB.');
        return;
      }

      const validTypes = ['image/jpeg', 'image/png', 'image/tiff', 'image/dicom'];
      if (!validTypes.includes(file.type)) {
        toast.error('Format non supporté. Utilisez JPEG, PNG, TIFF ou DICOM.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
        toast.success('Image chargée avec succès');
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const performAIAnalysis = useCallback(async () => {
    if (!selectedImage) {
      toast.error('Veuillez d\'abord charger une image');
      return;
    }

    setIsAnalyzing(true);
    const selectedModelData = aiModels.find(m => m.id === selectedModel);
    const selectedProtocolData = analysisProtocols.find(p => p.id === selectedProtocol);

    toast.loading(`Analyse en cours avec ${selectedModelData?.name}...`);

    try {
      // Simulation d'analyse IA avancée
      await new Promise(resolve => setTimeout(resolve, 3000));

      const mockResults = {
        model: selectedModelData,
        protocol: selectedProtocolData,
        confidence: 0.987,
        processingTime: '2.3s',
        landmarks: selectedProtocolData?.landmarks || 12,
        measurements: {
          sna: 82.5,
          snb: 78.2,
          anb: 4.3,
          fma: 25.8,
          impa: 95.2
        },
        interpretation: 'Classe II squelettique modérée avec protrusion incisive supérieure',
        recommendations: [
          'Traitement orthodontique avec extractions possibles',
          'Contrôle de l\'ancrage postérieur',
          'Évaluation de la croissance résiduelle'
        ]
      };

      setAnalysisResults(mockResults);
      toast.success('Analyse terminée avec succès');
    } catch (error) {
      toast.error('Erreur lors de l\'analyse IA');
    } finally {
      setIsAnalyzing(false);
    }
  }, [selectedImage, selectedModel, selectedProtocol, aiModels, analysisProtocols]);

  const resetAnalysis = useCallback(() => {
    setSelectedImage(null);
    setAnalysisResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-adaptive bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t('cephalometric.title')}
            </h1>
            <p className="text-adaptive-secondary mt-2 text-lg">
              {t('cephalometric.subtitle')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={resetAnalysis}
              className="flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              <RotateCcw className="w-5 h-5 mr-2" />
              Réinitialiser
            </button>
            <button className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
              <Settings className="w-5 h-5 mr-2" />
              Paramètres IA
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1 space-y-6">
          {/* Image Upload */}
          <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
            <h3 className="text-lg font-semibold text-adaptive mb-4 flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              {t('cephalometric.uploadImage')}
            </h3>
            
            <div className="space-y-4">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*,.dcm"
                onChange={handleImageUpload}
                className="hidden"
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="w-full p-4 border-2 border-dashed border-adaptive-light rounded-lg hover:border-blue-500 transition-colors flex flex-col items-center justify-center space-y-2"
              >
                <Upload className="w-8 h-8 text-adaptive-secondary" />
                <span className="text-adaptive-secondary">
                  Cliquez pour charger une radiographie
                </span>
                <span className="text-xs text-adaptive-tertiary">
                  JPEG, PNG, TIFF, DICOM (max 10MB)
                </span>
              </button>

              {selectedImage && (
                <div className="relative">
                  <img
                    src={selectedImage}
                    alt="Radiographie"
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                    ✓ Chargée
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* AI Model Selection */}
          <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
            <h3 className="text-lg font-semibold text-adaptive mb-4 flex items-center">
              <Cpu className="w-5 h-5 mr-2" />
              Modèle IA
            </h3>
            
            <div className="space-y-3">
              {aiModels.map((model) => (
                <div
                  key={model.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedModel === model.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-adaptive-light hover:border-blue-300'
                  }`}
                  onClick={() => setSelectedModel(model.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {model.icon}
                      <span className="font-medium text-adaptive">{model.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        {model.accuracy}%
                      </span>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {model.speed}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs text-adaptive-secondary">{model.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Protocol Selection */}
          <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
            <h3 className="text-lg font-semibold text-adaptive mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              {t('cephalometric.protocol')}
            </h3>
            
            <div className="space-y-3">
              {analysisProtocols.map((protocol) => (
                <div
                  key={protocol.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedProtocol === protocol.id
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                      : 'border-adaptive-light hover:border-purple-300'
                  }`}
                  onClick={() => setSelectedProtocol(protocol.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-adaptive">{protocol.name}</span>
                    <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                      {protocol.landmarks} points
                    </span>
                  </div>
                  <p className="text-xs text-adaptive-secondary mb-2">{protocol.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {protocol.measurements.slice(0, 3).map((measurement, index) => (
                      <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {measurement}
                      </span>
                    ))}
                    {protocol.measurements.length > 3 && (
                      <span className="text-xs text-adaptive-tertiary">
                        +{protocol.measurements.length - 3} autres
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Analysis Button */}
          <button
            onClick={performAIAnalysis}
            disabled={!selectedImage || isAnalyzing}
            className="w-full flex items-center justify-center px-6 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg"
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                Analyse en cours...
              </>
            ) : (
              <>
                <Brain className="w-6 h-6 mr-3" />
                {t('cephalometric.analyzeWithAI')}
              </>
            )}
          </button>
        </div>

        {/* Analysis Area */}
        <div className="lg:col-span-2">
          {selectedImage ? (
            <CephalometricAnalysis
              imageUrl={selectedImage}
              patientId="demo-patient"
              onAnalysisComplete={(analysis) => {
                console.log('Analysis completed:', analysis);
                toast.success('Analyse sauvegardée');
              }}
            />
          ) : (
            <div className="surface-primary rounded-xl shadow-sm border border-adaptive-light p-12 text-center">
              <Eye className="w-16 h-16 text-adaptive-secondary mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-adaptive mb-2">
                Zone d'Analyse IA
              </h3>
              <p className="text-adaptive-secondary">
                Chargez une radiographie céphalométrique pour commencer l'analyse automatique
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Results Panel */}
      {analysisResults && (
        <div className="mt-8 surface-primary rounded-xl shadow-sm border border-adaptive-light p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-adaptive">Résultats de l'Analyse IA</h3>
            <div className="flex items-center space-x-3">
              <button className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                <Save className="w-5 h-5 mr-2" />
                Sauvegarder
              </button>
              <button className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <Download className="w-5 h-5 mr-2" />
                Exporter PDF
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Modèle IA Utilisé</h4>
              <p className="text-blue-700 dark:text-blue-300">{analysisResults.model?.name}</p>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                Confiance: {(analysisResults.confidence * 100).toFixed(1)}%
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">Protocole</h4>
              <p className="text-purple-700 dark:text-purple-300">{analysisResults.protocol?.name}</p>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                {analysisResults.landmarks} points détectés
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">Performance</h4>
              <p className="text-green-700 dark:text-green-300">Temps: {analysisResults.processingTime}</p>
              <p className="text-sm text-green-600 dark:text-green-400">
                Précision sub-millimétrique
              </p>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-adaptive mb-3">Mesures Principales</h4>
              <div className="space-y-2">
                {Object.entries(analysisResults.measurements).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <span className="font-medium">{key.toUpperCase()}</span>
                    <span className="text-blue-600 font-semibold">{value}°</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-adaptive mb-3">Interprétation</h4>
              <p className="text-adaptive-secondary mb-4">{analysisResults.interpretation}</p>
              
              <h4 className="font-semibold text-adaptive mb-3">Recommandations</h4>
              <ul className="space-y-1">
                {analysisResults.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-adaptive-secondary text-sm flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CephalometricAnalysisPage;
