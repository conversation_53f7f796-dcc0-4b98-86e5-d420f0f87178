import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { LogOut, User } from 'lucide-react';
import LanguageSelector from '../LanguageSelector';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const { t } = useLanguage();

  return (
    <header className="surface-primary shadow-sm border-b border-adaptive">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <img 
              src="/WhatsApp Image 2025-06-09 at 08.39.07.jpeg" 
              alt="OrthoProgress" 
              className="h-10 w-10 rounded-lg"
            />
            <h1 className="text-xl font-bold text-adaptive">OrthoProgress</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <LanguageSelector showLabel={false} />
            
            {/* User Info */}
            {user && (
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-adaptive-tertiary" />
                  <span className="text-sm font-medium text-adaptive-secondary">{user.fullName}</span>
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 rounded-full">
                    {t(`auth.${user.role}`)}
                  </span>
                </div>
                
                <button
                  onClick={logout}
                  className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-adaptive-secondary hover:text-adaptive hover:surface-tertiary transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>{t('nav.logout')}</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;