import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { LogOut, User } from 'lucide-react';
import LanguageSelector from '../LanguageSelector';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const { t } = useLanguage();

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <img 
              src="/WhatsApp Image 2025-06-09 at 08.39.07.jpeg" 
              alt="OrthoProgress" 
              className="h-10 w-10 rounded-lg"
            />
            <h1 className="text-xl font-bold text-gray-900">OrthoProgress</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <LanguageSelector showLabel={false} />
            
            {/* User Info */}
            {user && (
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">{user.fullName}</span>
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                    {t(`auth.${user.role}`)}
                  </span>
                </div>
                
                <button
                  onClick={logout}
                  className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>{t('nav.logout')}</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;