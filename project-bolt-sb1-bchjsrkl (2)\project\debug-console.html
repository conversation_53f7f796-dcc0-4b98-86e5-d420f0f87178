<!DOCTYPE html>
<html>
<head>
    <title>Debug Console - OrthoProgress</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #fff; }
        .error { color: #ff6b6b; background: #2d1b1b; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: #ffd93d; background: #2d2a1b; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: #6bcf7f; background: #1b2d1f; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .loading { color: #74c0fc; }
        #status { position: fixed; top: 10px; right: 10px; padding: 10px; border-radius: 5px; }
        .success { background: #2d1b2d; color: #c084fc; }
        .failed { background: #2d1b1b; color: #ff6b6b; }
    </style>
</head>
<body>
    <h1>🔍 Debug Console - OrthoProgress</h1>
    <div id="status" class="loading">⏳ Chargement...</div>
    <div id="logs"></div>
    
    <script>
        const logs = document.getElementById('logs');
        const status = document.getElementById('status');
        
        function addLog(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }
        
        // Capturer les erreurs
        window.addEventListener('error', (e) => {
            addLog(`❌ ERREUR: ${e.message} (${e.filename}:${e.lineno})`, 'error');
            status.textContent = '❌ Erreurs détectées';
            status.className = 'failed';
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            addLog(`❌ PROMISE REJETÉE: ${e.reason}`, 'error');
        });
        
        // Tester la connexion à l'application
        addLog('🚀 Démarrage du diagnostic...', 'info');
        
        // Créer un iframe pour charger l'application
        const iframe = document.createElement('iframe');
        iframe.src = 'http://localhost:3000';
        iframe.style.width = '100%';
        iframe.style.height = '400px';
        iframe.style.border = '2px solid #444';
        iframe.style.marginTop = '20px';
        
        iframe.onload = () => {
            addLog('✅ Iframe chargé avec succès', 'info');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const rootElement = iframeDoc.getElementById('root');
                
                if (rootElement) {
                    if (rootElement.innerHTML.trim() === '') {
                        addLog('❌ PROBLÈME: Le div #root est vide - React ne se monte pas', 'error');
                        status.textContent = '❌ React ne se monte pas';
                        status.className = 'failed';
                    } else {
                        addLog('✅ Le div #root contient du contenu', 'info');
                        addLog(`📄 Contenu: ${rootElement.innerHTML.substring(0, 200)}...`, 'info');
                        status.textContent = '✅ Application chargée';
                        status.className = 'success';
                    }
                } else {
                    addLog('❌ ERREUR: Div #root introuvable', 'error');
                }
                
                // Vérifier les erreurs dans l'iframe
                iframe.contentWindow.addEventListener('error', (e) => {
                    addLog(`❌ ERREUR IFRAME: ${e.message}`, 'error');
                });
                
            } catch (e) {
                addLog(`⚠️ Impossible d'accéder au contenu de l'iframe (CORS): ${e.message}`, 'warning');
                addLog('ℹ️ Ceci est normal pour les applications React en développement', 'info');
            }
        };
        
        iframe.onerror = () => {
            addLog('❌ Erreur de chargement de l\'iframe', 'error');
            status.textContent = '❌ Échec du chargement';
            status.className = 'failed';
        };
        
        document.body.appendChild(iframe);
        
        // Test de connectivité
        fetch('http://localhost:3000')
            .then(response => {
                if (response.ok) {
                    addLog('✅ Serveur accessible (HTTP 200)', 'info');
                } else {
                    addLog(`❌ Serveur répond avec le code: ${response.status}`, 'error');
                }
            })
            .catch(error => {
                addLog(`❌ Erreur de connexion au serveur: ${error.message}`, 'error');
            });
            
        // Attendre 5 secondes puis faire un diagnostic final
        setTimeout(() => {
            addLog('🔍 Diagnostic terminé après 5 secondes', 'info');
            if (status.textContent.includes('⏳')) {
                status.textContent = '⚠️ Diagnostic incomplet';
                status.className = 'warning';
            }
        }, 5000);
    </script>
</body>
</html>
