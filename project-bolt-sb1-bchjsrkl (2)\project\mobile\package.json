{"name": "orthoprogress-mobile", "version": "1.0.0", "description": "OrthoProgress - Application mobile native pour le suivi orthodontique", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace OrthoProgress.xcworkspace -scheme OrthoProgress -configuration Release archive", "bundle:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle", "bundle:ios": "react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle"}, "dependencies": {"react": "18.3.1", "react-native": "0.74.0", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "react-native-screens": "^3.31.1", "react-native-safe-area-context": "^4.10.1", "react-native-gesture-handler": "^2.16.2", "react-native-reanimated": "^3.10.1", "react-native-vector-icons": "^10.1.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "^11.3.1", "react-native-device-info": "^10.13.2", "react-native-keychain": "^8.2.0", "react-native-biometrics": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-image-picker": "^7.1.2", "react-native-image-crop-picker": "^0.40.3", "react-native-vision-camera": "^4.0.0", "react-native-image-resizer": "^3.0.7", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/messaging": "^19.2.2", "@react-native-firebase/analytics": "^19.2.2", "@react-native-firebase/crashlytics": "^19.2.2", "@react-native-firebase/remote-config": "^19.2.2", "react-native-push-notification": "^8.1.1", "@react-native-community/push-notification-ios": "^1.11.0", "react-native-background-job": "^1.2.0", "react-native-background-timer": "^2.4.1", "@tensorflow/tfjs": "^4.19.0", "@tensorflow/tfjs-react-native": "^1.0.0", "@tensorflow/tfjs-platform-react-native": "^1.0.0", "react-native-fs": "^2.20.0", "axios": "^1.7.2", "react-query": "^3.39.3", "zustand": "^4.5.2", "react-hook-form": "^7.51.5", "yup": "^1.4.0", "react-native-svg": "^15.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-blur": "^4.4.0", "lottie-react-native": "^6.7.0", "react-native-haptic-feedback": "^2.2.0", "react-native-sound": "^0.11.2", "react-native-calendar-picker": "^7.1.4", "react-native-chart-kit": "^6.12.0", "react-native-progress": "^5.0.1", "react-native-modal": "^13.0.1", "react-native-actionsheet": "^2.4.2", "react-native-share": "^10.2.1", "react-native-contacts": "^7.0.8", "react-native-permissions": "^4.1.5", "react-native-document-picker": "^9.2.1", "react-native-file-viewer": "^2.1.5", "react-native-maps": "^1.14.0", "react-native-geolocation-service": "^5.3.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-barcode-builder": "^2.0.2", "react-native-webview": "^13.8.6", "react-native-video": "^6.0.0", "react-native-youtube-iframe": "^2.3.0", "react-native-sqlite-storage": "^6.0.1", "realm": "^12.9.0", "react-native-mmkv": "^2.12.2"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/preset-env": "^7.24.6", "@babel/runtime": "^7.24.6", "@react-native/babel-preset": "0.74.84", "@react-native/eslint-config": "0.74.84", "@react-native/metro-config": "0.74.84", "@react-native/typescript-config": "0.74.84", "@types/react": "^18.3.3", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.4.5", "metro-react-native-babel-preset": "^0.77.0", "react-native-flipper": "^0.212.0", "flipper-plugin-react-query": "^1.0.0"}, "engines": {"node": ">=18"}, "keywords": ["orthodontics", "dental", "healthcare", "mobile", "react-native", "ai", "tracking", "compliance"], "author": "OrthoProgress Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/orthoprogress/mobile.git"}, "bugs": {"url": "https://github.com/orthoprogress/mobile/issues"}, "homepage": "https://orthoprogress.com"}