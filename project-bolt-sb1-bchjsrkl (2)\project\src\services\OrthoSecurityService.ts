import CryptoJS from 'crypto-js';
import jwt from 'jsonwebtoken';

// Configuration sécurisée
const ENCRYPTION_KEY = process.env.VITE_ENCRYPTION_KEY || 'ortho-progress-secure-key-2024';
const JWT_SECRET = process.env.VITE_JWT_SECRET || 'ortho-jwt-secret-key';
const API_BASE_URL = process.env.VITE_API_URL || 'https://api.orthoprogress.com';

export interface SecurityConfig {
  encryptionEnabled: boolean;
  auditLogging: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordComplexity: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  details?: any;
}

export interface SecurityToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
  scope: string[];
}

class SecurityService {
  private config: SecurityConfig = {
    encryptionEnabled: true,
    auditLogging: true,
    sessionTimeout: 3600000, // 1 heure
    maxLoginAttempts: 5,
    passwordComplexity: {
      minLength: 12,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    }
  };

  private auditLogs: AuditLog[] = [];
  private loginAttempts: Map<string, number> = new Map();

  // Chiffrement des données sensibles
  encryptData(data: any): string {
    if (!this.config.encryptionEnabled) return JSON.stringify(data);
    
    const jsonString = JSON.stringify(data);
    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
    return encrypted;
  }

  decryptData<T>(encryptedData: string): T {
    if (!this.config.encryptionEnabled) return JSON.parse(encryptedData);
    
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    return JSON.parse(jsonString);
  }

  // Génération de tokens JWT sécurisés
  generateSecureToken(payload: any, expiresIn: string = '1h'): string {
    const tokenPayload = {
      ...payload,
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateUUID() // JWT ID unique
    };

    return jwt.sign(tokenPayload, JWT_SECRET, { 
      expiresIn,
      algorithm: 'HS256',
      issuer: 'OrthoProgress',
      audience: 'orthoprogress-users'
    });
  }

  // Validation des tokens
  validateToken(token: string): any {
    try {
      return jwt.verify(token, JWT_SECRET, {
        issuer: 'OrthoProgress',
        audience: 'orthoprogress-users'
      });
    } catch (error) {
      this.logSecurityEvent('TOKEN_VALIDATION_FAILED', { error: error.message });
      throw new Error('Token invalide');
    }
  }

  // Validation de la complexité des mots de passe
  validatePasswordComplexity(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { passwordComplexity } = this.config;

    if (password.length < passwordComplexity.minLength) {
      errors.push(`Le mot de passe doit contenir au moins ${passwordComplexity.minLength} caractères`);
    }

    if (passwordComplexity.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une majuscule');
    }

    if (passwordComplexity.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une minuscule');
    }

    if (passwordComplexity.requireNumbers && !/\d/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins un chiffre');
    }

    if (passwordComplexity.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins un caractère spécial');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Hachage sécurisé des mots de passe
  async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcryptjs');
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  // Vérification des mots de passe
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const bcrypt = await import('bcryptjs');
    return bcrypt.compare(password, hashedPassword);
  }

  // Gestion des tentatives de connexion
  checkLoginAttempts(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier) || 0;
    return attempts < this.config.maxLoginAttempts;
  }

  recordLoginAttempt(identifier: string, success: boolean): void {
    if (success) {
      this.loginAttempts.delete(identifier);
    } else {
      const attempts = this.loginAttempts.get(identifier) || 0;
      this.loginAttempts.set(identifier, attempts + 1);
    }

    this.logSecurityEvent(success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED', {
      identifier,
      attempts: this.loginAttempts.get(identifier) || 0
    });
  }

  // Audit et logging de sécurité
  logSecurityEvent(action: string, details?: any): void {
    if (!this.config.auditLogging) return;

    const auditLog: AuditLog = {
      id: this.generateUUID(),
      userId: this.getCurrentUserId(),
      action,
      resource: 'SECURITY',
      timestamp: new Date(),
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
      success: !action.includes('FAILED'),
      details
    };

    this.auditLogs.push(auditLog);
    
    // Envoyer au serveur en production
    if (process.env.NODE_ENV === 'production') {
      this.sendAuditLogToServer(auditLog);
    }

    console.log('🔒 Security Event:', auditLog);
  }

  // Nettoyage automatique des logs
  cleanupAuditLogs(): void {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    this.auditLogs = this.auditLogs.filter(log => log.timestamp > thirtyDaysAgo);
  }

  // Détection d'anomalies
  detectAnomalies(): { suspicious: boolean; reasons: string[] } {
    const reasons: string[] = [];
    const recentLogs = this.auditLogs.filter(log => 
      log.timestamp > new Date(Date.now() - 3600000) // Dernière heure
    );

    // Trop de tentatives de connexion échouées
    const failedLogins = recentLogs.filter(log => log.action === 'LOGIN_FAILED').length;
    if (failedLogins > 10) {
      reasons.push('Trop de tentatives de connexion échouées');
    }

    // Accès depuis plusieurs IP différentes
    const uniqueIPs = new Set(recentLogs.map(log => log.ipAddress));
    if (uniqueIPs.size > 3) {
      reasons.push('Accès depuis plusieurs adresses IP');
    }

    // Activité inhabituelle (trop d'actions en peu de temps)
    if (recentLogs.length > 100) {
      reasons.push('Activité anormalement élevée');
    }

    return {
      suspicious: reasons.length > 0,
      reasons
    };
  }

  // Utilitaires
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private getCurrentUserId(): string {
    try {
      const token = localStorage.getItem('accessToken');
      if (token) {
        const decoded = this.validateToken(token);
        return decoded.userId || 'anonymous';
      }
    } catch (error) {
      // Token invalide
    }
    return 'anonymous';
  }

  private getClientIP(): string {
    // En production, ceci serait obtenu du serveur
    return '127.0.0.1';
  }

  private async sendAuditLogToServer(auditLog: AuditLog): Promise<void> {
    try {
      await fetch(`${API_BASE_URL}/audit-logs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(auditLog)
      });
    } catch (error) {
      console.error('Erreur envoi audit log:', error);
    }
  }

  // Méthodes publiques pour l'interface
  getAuditLogs(limit: number = 100): AuditLog[] {
    return this.auditLogs
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  getSecurityConfig(): SecurityConfig {
    return { ...this.config };
  }

  updateSecurityConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logSecurityEvent('SECURITY_CONFIG_UPDATED', newConfig);
  }

  // Nettoyage périodique
  startSecurityMaintenance(): void {
    // Nettoyage des logs toutes les heures
    setInterval(() => {
      this.cleanupAuditLogs();
    }, 3600000);

    // Détection d'anomalies toutes les 10 minutes
    setInterval(() => {
      const anomalies = this.detectAnomalies();
      if (anomalies.suspicious) {
        this.logSecurityEvent('ANOMALY_DETECTED', anomalies);
        console.warn('🚨 Anomalies détectées:', anomalies.reasons);
      }
    }, 600000);
  }
}

export const securityService = new SecurityService();

// Démarrer la maintenance automatique
securityService.startSecurityMaintenance();
