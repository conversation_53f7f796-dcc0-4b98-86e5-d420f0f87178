import React from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { ThemeProvider } from './contexts/ThemeContext';
import Layout from './components/Layout/Layout';
import AuthPage from './pages/AuthPage';
import DashboardPage from './pages/DashboardPage';
import ProtectedRoute from './components/ProtectedRoute';

// Patient pages
import PatientDashboard from './pages/patient/PatientDashboard';
import DailyTracking from './pages/patient/DailyTracking';
import Photos from './pages/patient/Photos';
import Appointments from './pages/patient/Appointments';
import Reminders from './pages/patient/Reminders';
import Billing from './pages/patient/Billing';
import Education from './pages/patient/Education';
import Analytics from './pages/patient/Analytics';
import MedicalRecord from './pages/patient/MedicalRecord';
import Rewards from './pages/patient/Rewards';
import PatientCommunity from './pages/patient/Community';
import Help from './pages/patient/Help';

// Practitioner pages
import PractitionerDashboard from './pages/practitioner/PractitionerDashboard';
import PatientManagement from './pages/practitioner/PatientManagement';
import AppointmentManagement from './pages/practitioner/AppointmentManagement';
import InvisalignManagement from './pages/practitioner/InvisalignManagement';
import SubscriptionManagement from './pages/practitioner/SubscriptionManagement';

// Shared pages
import Community from './pages/Community';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import Teleconsultation from './pages/Teleconsultation';

const router = createBrowserRouter([
  {
    path: '/auth',
    element: <AuthPage />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <Layout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      
      // Patient Routes
      {
        path: 'patient/dashboard',
        element: (
          <ProtectedRoute requiredRole="patient">
            <PatientDashboard />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/suivi',
        element: (
          <ProtectedRoute requiredRole="patient">
            <DailyTracking />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/dossier',
        element: (
          <ProtectedRoute requiredRole="patient">
            <MedicalRecord />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/rendez-vous',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Appointments />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/rappels',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Reminders />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/photos',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Photos />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/communaute',
        element: (
          <ProtectedRoute requiredRole="patient">
            <PatientCommunity />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/recompenses',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Rewards />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/profil',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Profile />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/aide',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Help />
          </ProtectedRoute>
        ),
      },
      {
        path: 'teleconsultation',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Teleconsultation />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/education',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Education />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/communaute',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Community />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/analytiques',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Analytics />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/factures',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Billing />
          </ProtectedRoute>
        ),
      },
      {
        path: 'patient/recompenses',
        element: (
          <ProtectedRoute requiredRole="patient">
            <Rewards />
          </ProtectedRoute>
        ),
      },

      // Practitioner Routes
      {
        path: 'practitioner/dashboard',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <PractitionerDashboard />
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/patients',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <PatientManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/appointments',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <AppointmentManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/invisalign',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <InvisalignManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/subscription',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <SubscriptionManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/messages',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Messages</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/billing',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Facturation</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/analytics',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Analytiques</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/reports',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Rapports</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'practitioner/community',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <Community />
          </ProtectedRoute>
        ),
      },

      // Legacy routes for compatibility
      {
        path: 'progress',
        element: (
          <ProtectedRoute requiredRole="patient">
            <DailyTracking />
          </ProtectedRoute>
        ),
      },
      {
        path: 'photos',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Photos</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'medical-record',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Dossier Médical</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'teleconsultation',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Téléconsultation</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'gamification',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Gamification</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'reminders',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Rappels</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'analytics',
        element: (
          <ProtectedRoute>
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Analytiques</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'education',
        element: (
          <ProtectedRoute requiredRole="patient">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Éducation</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'patients',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <PatientManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'appointments',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <AppointmentManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'subscription',
        element: (
          <ProtectedRoute requiredRole="practitioner">
            <SubscriptionManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'forum',
        element: (
          <ProtectedRoute>
            <Community />
          </ProtectedRoute>
        ),
      },
      {
        path: 'profile',
        element: (
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        ),
      },
      {
        path: 'settings',
        element: (
          <ProtectedRoute>
            <Settings />
          </ProtectedRoute>
        ),
      },
      // Admin Routes
      {
        path: 'admin',
        element: (
          <ProtectedRoute requiredRole="admin">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Administration</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/vip',
        element: (
          <ProtectedRoute requiredRole="admin">
            <div className="p-6">
              <h1 className="text-3xl font-bold text-gray-800">Gestion VIP</h1>
              <p className="text-gray-600 mt-2">Page en cours de développement...</p>
            </div>
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
]);

function App() {
  return (
    <ThemeProvider>
      <LanguageProvider>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;
