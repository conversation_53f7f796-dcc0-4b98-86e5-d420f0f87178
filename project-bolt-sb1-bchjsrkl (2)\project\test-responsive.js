// Script de test de responsivité pour OrthoProgress
console.log('📱 Test de responsivité OrthoProgress...');

// Fonction pour analyser les classes responsives
function analyzeResponsiveClasses() {
  console.log('\n🔍 Analyse des classes responsives...');
  
  const allElements = document.querySelectorAll('*');
  const responsiveClasses = {
    sm: [],
    md: [],
    lg: [],
    xl: [],
    '2xl': []
  };
  
  allElements.forEach(element => {
    const classes = element.className;
    if (typeof classes === 'string') {
      // Rechercher les classes responsives
      const smClasses = classes.match(/sm:[a-z-]+/g);
      const mdClasses = classes.match(/md:[a-z-]+/g);
      const lgClasses = classes.match(/lg:[a-z-]+/g);
      const xlClasses = classes.match(/xl:[a-z-]+/g);
      const xxlClasses = classes.match(/2xl:[a-z-]+/g);
      
      if (smClasses) responsiveClasses.sm.push(...smClasses);
      if (mdClasses) responsiveClasses.md.push(...mdClasses);
      if (lgClasses) responsiveClasses.lg.push(...lgClasses);
      if (xlClasses) responsiveClasses.xl.push(...xlClasses);
      if (xxlClasses) responsiveClasses['2xl'].push(...xxlClasses);
    }
  });
  
  // Afficher les résultats
  Object.keys(responsiveClasses).forEach(breakpoint => {
    const uniqueClasses = [...new Set(responsiveClasses[breakpoint])];
    console.log(`- ${breakpoint}: ${uniqueClasses.length} classes uniques`);
    if (uniqueClasses.length > 0) {
      console.log(`  Exemples: ${uniqueClasses.slice(0, 5).join(', ')}`);
    }
  });
}

// Fonction pour tester les grilles responsives
function testResponsiveGrids() {
  console.log('\n📐 Test des grilles responsives...');
  
  const gridElements = document.querySelectorAll('[class*="grid-cols"]');
  console.log(`- Éléments avec grilles trouvés: ${gridElements.length}`);
  
  gridElements.forEach((element, index) => {
    const classes = element.className;
    const gridClasses = classes.match(/(?:sm:|md:|lg:|xl:|2xl:)?grid-cols-\d+/g);
    if (gridClasses) {
      console.log(`  Grille ${index + 1}: ${gridClasses.join(', ')}`);
    }
  });
}

// Fonction pour tester les espacements responsifs
function testResponsiveSpacing() {
  console.log('\n📏 Test des espacements responsifs...');
  
  const spacingPatterns = ['p-', 'm-', 'px-', 'py-', 'mx-', 'my-', 'gap-'];
  const responsiveSpacing = {};
  
  spacingPatterns.forEach(pattern => {
    const elements = document.querySelectorAll(`[class*="${pattern}"]`);
    const responsiveElements = Array.from(elements).filter(el => 
      el.className.includes(`sm:${pattern}`) || 
      el.className.includes(`md:${pattern}`) || 
      el.className.includes(`lg:${pattern}`) || 
      el.className.includes(`xl:${pattern}`)
    );
    
    if (responsiveElements.length > 0) {
      responsiveSpacing[pattern] = responsiveElements.length;
    }
  });
  
  Object.keys(responsiveSpacing).forEach(pattern => {
    console.log(`- ${pattern}: ${responsiveSpacing[pattern]} éléments responsifs`);
  });
}

// Fonction pour tester la visibilité responsive
function testResponsiveVisibility() {
  console.log('\n👁️ Test de la visibilité responsive...');
  
  const hiddenElements = document.querySelectorAll('[class*="hidden"]');
  const blockElements = document.querySelectorAll('[class*="block"]');
  const flexElements = document.querySelectorAll('[class*="flex"]');
  
  console.log(`- Éléments avec classes hidden: ${hiddenElements.length}`);
  console.log(`- Éléments avec classes block: ${blockElements.length}`);
  console.log(`- Éléments avec classes flex: ${flexElements.length}`);
  
  // Vérifier les classes de visibilité responsive
  const responsiveVisibility = [];
  [hiddenElements, blockElements, flexElements].forEach(nodeList => {
    Array.from(nodeList).forEach(element => {
      const classes = element.className;
      if (classes.includes('sm:') || classes.includes('md:') || 
          classes.includes('lg:') || classes.includes('xl:')) {
        responsiveVisibility.push(element);
      }
    });
  });
  
  console.log(`- Éléments avec visibilité responsive: ${responsiveVisibility.length}`);
}

// Fonction pour simuler différentes tailles d'écran
function simulateBreakpoints() {
  console.log('\n📱 Simulation des breakpoints Tailwind...');
  
  const breakpoints = {
    'Mobile': { width: 375, height: 667 },
    'Tablet': { width: 768, height: 1024 },
    'Desktop': { width: 1024, height: 768 },
    'Large Desktop': { width: 1440, height: 900 },
    'XL Desktop': { width: 1920, height: 1080 }
  };
  
  Object.keys(breakpoints).forEach(device => {
    const { width, height } = breakpoints[device];
    console.log(`\n${device} (${width}x${height}):`);
    
    // Simuler le viewport (note: on ne peut pas vraiment redimensionner)
    console.log(`- Breakpoint Tailwind: ${getTailwindBreakpoint(width)}`);
    console.log(`- Classes actives estimées: ${getActiveClasses(width)}`);
  });
}

function getTailwindBreakpoint(width) {
  if (width >= 1536) return '2xl';
  if (width >= 1280) return 'xl';
  if (width >= 1024) return 'lg';
  if (width >= 768) return 'md';
  if (width >= 640) return 'sm';
  return 'base';
}

function getActiveClasses(width) {
  const breakpoint = getTailwindBreakpoint(width);
  const allElements = document.querySelectorAll('*');
  let activeClasses = 0;
  
  allElements.forEach(element => {
    const classes = element.className;
    if (typeof classes === 'string') {
      // Compter les classes qui seraient actives à cette taille
      if (width >= 640 && classes.includes('sm:')) activeClasses++;
      if (width >= 768 && classes.includes('md:')) activeClasses++;
      if (width >= 1024 && classes.includes('lg:')) activeClasses++;
      if (width >= 1280 && classes.includes('xl:')) activeClasses++;
      if (width >= 1536 && classes.includes('2xl:')) activeClasses++;
    }
  });
  
  return activeClasses;
}

// Fonction pour tester les images responsives
function testResponsiveImages() {
  console.log('\n🖼️ Test des images responsives...');
  
  const images = document.querySelectorAll('img');
  const responsiveImages = Array.from(images).filter(img => 
    img.className.includes('w-') || img.className.includes('h-') ||
    img.className.includes('max-w-') || img.className.includes('max-h-')
  );
  
  console.log(`- Images totales: ${images.length}`);
  console.log(`- Images avec classes responsives: ${responsiveImages.length}`);
  
  // Vérifier les srcset pour les images adaptatives
  const adaptiveImages = Array.from(images).filter(img => img.srcset);
  console.log(`- Images avec srcset: ${adaptiveImages.length}`);
}

// Fonction principale
function runResponsiveTests() {
  console.log('🚀 Démarrage des tests de responsivité...\n');
  
  analyzeResponsiveClasses();
  testResponsiveGrids();
  testResponsiveSpacing();
  testResponsiveVisibility();
  testResponsiveImages();
  simulateBreakpoints();
  
  console.log('\n✅ Tests de responsivité terminés !');
  console.log('\n📊 Résumé:');
  console.log('- Classes responsives analysées');
  console.log('- Grilles responsives vérifiées');
  console.log('- Espacements responsifs testés');
  console.log('- Visibilité responsive contrôlée');
  console.log('- Images responsives évaluées');
  console.log('- Breakpoints simulés');
}

// Démarrer les tests
runResponsiveTests();
