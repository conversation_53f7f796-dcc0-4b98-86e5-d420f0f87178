import React, { useState, useEffect } from 'react';
import { FileText, Download, Upload, Calendar, User, Heart, AlertTriangle, Plus, Search, Filter, Eye, Edit, Trash2, Clock, CheckCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface MedicalDocument {
  id: string;
  title: string;
  type: 'prescription' | 'report' | 'xray' | 'photo' | 'analysis' | 'certificate';
  date: string;
  doctor: string;
  description: string;
  fileUrl?: string;
  size?: string;
  status: 'active' | 'archived';
}

interface MedicalInfo {
  allergies: string[];
  medications: string[];
  conditions: string[];
  emergencyContact: {
    name: string;
    phone: string;
    relation: string;
  };
  bloodType: string;
  height: string;
  weight: string;
  lastUpdate: string;
}

const MedicalRecord: React.FC = () => {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<MedicalDocument[]>([]);
  const [medicalInfo, setMedicalInfo] = useState<MedicalInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'history'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'prescription' | 'report' | 'xray' | 'photo' | 'analysis' | 'certificate'>('all');
  const [showUploadModal, setShowUploadModal] = useState(false);

  useEffect(() => {
    const mockDocuments: MedicalDocument[] = [
      {
        id: '1',
        title: 'Radiographie panoramique',
        type: 'xray',
        date: '2024-01-15',
        doctor: 'Dr. Martin Dubois',
        description: 'Radiographie panoramique pour évaluation orthodontique initiale',
        fileUrl: '/documents/xray-001.pdf',
        size: '2.4 MB',
        status: 'active'
      },
      {
        id: '2',
        title: 'Prescription Invisalign',
        type: 'prescription',
        date: '2024-01-20',
        doctor: 'Dr. Martin Dubois',
        description: 'Prescription pour traitement Invisalign - 18 mois',
        fileUrl: '/documents/prescription-001.pdf',
        size: '156 KB',
        status: 'active'
      },
      {
        id: '3',
        title: 'Rapport de consultation',
        type: 'report',
        date: '2024-02-01',
        doctor: 'Dr. Martin Dubois',
        description: 'Rapport de suivi mensuel - Évolution positive',
        fileUrl: '/documents/report-001.pdf',
        size: '890 KB',
        status: 'active'
      },
      {
        id: '4',
        title: 'Photos intra-orales',
        type: 'photo',
        date: '2024-02-15',
        doctor: 'Dr. Martin Dubois',
        description: 'Photos de contrôle - Mois 2',
        fileUrl: '/documents/photos-001.zip',
        size: '5.2 MB',
        status: 'active'
      }
    ];

    const mockMedicalInfo: MedicalInfo = {
      allergies: ['Pénicilline', 'Latex'],
      medications: ['Paracétamol (si besoin)', 'Bain de bouche fluoré'],
      conditions: ['Malocclusion classe II', 'Encombrement dentaire'],
      emergencyContact: {
        name: 'Marie Dupont',
        phone: '+33 6 12 34 56 78',
        relation: 'Mère'
      },
      bloodType: 'A+',
      height: '175 cm',
      weight: '68 kg',
      lastUpdate: '2024-02-15'
    };

    setDocuments(mockDocuments);
    setMedicalInfo(mockMedicalInfo);
    setLoading(false);
  }, []);

  const filteredDocuments = documents
    .filter(doc => filterType === 'all' || doc.type === filterType)
    .filter(doc => 
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.doctor.toLowerCase().includes(searchTerm.toLowerCase())
    );

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'prescription':
        return <FileText className="w-5 h-5 text-blue-500" />;
      case 'report':
        return <FileText className="w-5 h-5 text-green-500" />;
      case 'xray':
        return <FileText className="w-5 h-5 text-purple-500" />;
      case 'photo':
        return <FileText className="w-5 h-5 text-pink-500" />;
      case 'analysis':
        return <FileText className="w-5 h-5 text-orange-500" />;
      case 'certificate':
        return <FileText className="w-5 h-5 text-indigo-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'prescription':
        return 'Prescription';
      case 'report':
        return 'Rapport';
      case 'xray':
        return 'Radiographie';
      case 'photo':
        return 'Photo';
      case 'analysis':
        return 'Analyse';
      case 'certificate':
        return 'Certificat';
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Dossier Médical</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Consultez et gérez vos documents médicaux
        </p>
      </div>

      {/* Onglets */}
      <div className="mb-6">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Vue d'ensemble
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Documents
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Historique
            </button>
          </nav>
        </div>
      </div>

      {/* Vue d'ensemble */}
      {activeTab === 'overview' && medicalInfo && (
        <div className="space-y-6">
          {/* Informations personnelles */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Informations personnelles
              </h2>
              <button className="text-blue-500 hover:text-blue-600 dark:text-blue-400">
                <Edit className="w-5 h-5" />
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Groupe sanguin
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {medicalInfo.bloodType}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Taille
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {medicalInfo.height}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Poids
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {medicalInfo.weight}
                </p>
              </div>
            </div>
          </div>

          {/* Contact d'urgence */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Contact d'urgence
            </h2>
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                  <Heart className="w-6 h-6 text-red-500" />
                </div>
              </div>
              <div>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {medicalInfo.emergencyContact.name}
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  {medicalInfo.emergencyContact.relation} • {medicalInfo.emergencyContact.phone}
                </p>
              </div>
            </div>
          </div>

          {/* Allergies et conditions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Allergies
              </h2>
              {medicalInfo.allergies.length > 0 ? (
                <div className="space-y-2">
                  {medicalInfo.allergies.map((allergy, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                      <span className="text-gray-900 dark:text-white">{allergy}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Aucune allergie connue</p>
              )}
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Conditions médicales
              </h2>
              {medicalInfo.conditions.length > 0 ? (
                <div className="space-y-2">
                  {medicalInfo.conditions.map((condition, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-blue-500" />
                      <span className="text-gray-900 dark:text-white">{condition}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Aucune condition particulière</p>
              )}
            </div>
          </div>

          {/* Médicaments */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Médicaments actuels
            </h2>
            {medicalInfo.medications.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {medicalInfo.medications.map((medication, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-900 dark:text-white">{medication}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">Aucun médicament en cours</p>
            )}
          </div>
        </div>
      )}

      {/* Documents */}
      {activeTab === 'documents' && (
        <div className="space-y-6">
          {/* Barre de recherche et filtres */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Rechercher un document..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">Tous les types</option>
                  <option value="prescription">Prescriptions</option>
                  <option value="report">Rapports</option>
                  <option value="xray">Radiographies</option>
                  <option value="photo">Photos</option>
                  <option value="analysis">Analyses</option>
                  <option value="certificate">Certificats</option>
                </select>
                <button
                  onClick={() => setShowUploadModal(true)}
                  className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <Upload className="w-5 h-5 mr-2" />
                  Ajouter
                </button>
              </div>
            </div>
          </div>

          {/* Liste des documents */}
          <div className="space-y-4">
            {filteredDocuments.length === 0 ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Aucun document trouvé
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm
                    ? 'Aucun document ne correspond à votre recherche'
                    : 'Vous n\'avez pas encore de documents'}
                </p>
              </div>
            ) : (
              filteredDocuments.map(document => (
                <div
                  key={document.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        {getTypeIcon(document.type)}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {document.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-1">
                          {document.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>{getTypeText(document.type)}</span>
                          <span>•</span>
                          <span>{formatDate(document.date)}</span>
                          <span>•</span>
                          <span>{document.doctor}</span>
                          {document.size && (
                            <>
                              <span>•</span>
                              <span>{document.size}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                        title="Voir"
                      >
                        <Eye className="w-5 h-5" />
                      </button>
                      <button
                        className="p-2 text-gray-500 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                        title="Télécharger"
                      >
                        <Download className="w-5 h-5" />
                      </button>
                      <button
                        className="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                        title="Supprimer"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Historique */}
      {activeTab === 'history' && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            Historique des modifications
          </h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex-shrink-0">
                <Clock className="w-5 h-5 text-gray-500" />
              </div>
              <div className="flex-1">
                <p className="text-gray-900 dark:text-white">
                  Mise à jour des informations personnelles
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  15 février 2024 à 14:30
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex-shrink-0">
                <Clock className="w-5 h-5 text-gray-500" />
              </div>
              <div className="flex-1">
                <p className="text-gray-900 dark:text-white">
                  Ajout d'un nouveau document - Rapport de consultation
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  1 février 2024 à 10:15
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal d'upload */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Ajouter un document
              </h3>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <Plus className="w-5 h-5 rotate-45" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Titre du document
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: Radiographie de contrôle"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Type de document
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="prescription">Prescription</option>
                  <option value="report">Rapport</option>
                  <option value="xray">Radiographie</option>
                  <option value="photo">Photo</option>
                  <option value="analysis">Analyse</option>
                  <option value="certificate">Certificat</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Fichier
                </label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Cliquez pour sélectionner un fichier ou glissez-déposez
                  </p>
                </div>
              </div>
            </div>
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => setShowUploadModal(false)}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  // Logique d'upload
                }}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Ajouter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MedicalRecord;
