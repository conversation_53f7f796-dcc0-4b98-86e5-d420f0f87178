import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Bell, Clock, Smartphone, Calendar, Zap } from 'lucide-react';

interface Reminder {
  id: string;
  type: 'wear' | 'hygiene' | 'appointment' | 'photo' | 'custom';
  title: string;
  message: string;
  time: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  enabled: boolean;
  smart: boolean;
  lastTriggered?: Date;
}

const SmartReminders: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');

  useEffect(() => {
    checkNotificationPermission();
    loadReminders();
    initializeSmartReminders();
  }, [user?.id]);

  const checkNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);
    }
  };

  const loadReminders = () => {
    const saved = localStorage.getItem(`reminders_${user?.id}`);
    if (saved) {
      setReminders(JSON.parse(saved));
    } else {
      // Initialize default smart reminders
      const defaultReminders: Reminder[] = [
        {
          id: '1',
          type: 'wear',
          title: 'Rappel port d\'appareil',
          message: 'N\'oubliez pas de porter votre appareil orthodontique!',
          time: '08:00',
          frequency: 'daily',
          enabled: true,
          smart: true
        },
        {
          id: '2',
          type: 'hygiene',
          title: 'Hygiène bucco-dentaire',
          message: 'Temps de nettoyer votre appareil et vos dents!',
          time: '20:00',
          frequency: 'daily',
          enabled: true,
          smart: true
        },
        {
          id: '3',
          type: 'photo',
          title: 'Photo de progression',
          message: 'Prenez une photo de vos progrès cette semaine',
          time: '18:00',
          frequency: 'weekly',
          enabled: true,
          smart: true
        }
      ];
      setReminders(defaultReminders);
      saveReminders(defaultReminders);
    }
  };

  const saveReminders = (updatedReminders: Reminder[]) => {
    localStorage.setItem(`reminders_${user?.id}`, JSON.stringify(updatedReminders));
  };

  const initializeSmartReminders = () => {
    // Analyze user behavior to optimize reminder times
    const progressData = localStorage.getItem(`progress_${user?.id}`);
    if (progressData) {
      const data = JSON.parse(progressData);
      // Smart algorithm would analyze when user typically marks their progress
      // and adjust reminder times accordingly
      optimizeReminderTimes(data);
    }
  };

  const optimizeReminderTimes = (progressData: any) => {
    // Simple smart algorithm: if user often forgets morning wear,
    // add an additional evening reminder
    const morningMisses = Object.values(progressData).filter((day: any, index) => 
      index % 7 < 3 && !day // Monday-Wednesday misses
    ).length;

    if (morningMisses > 3) {
      // Add evening preparation reminder
      const eveningReminder: Reminder = {
        id: 'smart_evening',
        type: 'custom',
        title: 'Préparation du lendemain',
        message: 'Préparez votre appareil pour demain matin',
        time: '21:30',
        frequency: 'daily',
        enabled: true,
        smart: true
      };
      
      setReminders(prev => {
        const updated = [...prev, eveningReminder];
        saveReminders(updated);
        return updated;
      });
    }
  };

  const scheduleNotification = (reminder: Reminder) => {
    if (notificationPermission !== 'granted') return;

    const now = new Date();
    const [hours, minutes] = reminder.time.split(':').map(Number);
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    // If time has passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const timeUntilNotification = scheduledTime.getTime() - now.getTime();

    setTimeout(() => {
      new Notification(reminder.title, {
        body: reminder.message,
        icon: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
        badge: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
        vibrate: [200, 100, 200],
        tag: reminder.id,
        requireInteraction: true
      });

      // Update last triggered
      setReminders(prev => prev.map(r => 
        r.id === reminder.id 
          ? { ...r, lastTriggered: new Date() }
          : r
      ));
    }, timeUntilNotification);
  };

  const toggleReminder = (id: string) => {
    setReminders(prev => {
      const updated = prev.map(reminder => 
        reminder.id === id 
          ? { ...reminder, enabled: !reminder.enabled }
          : reminder
      );
      saveReminders(updated);
      
      // Schedule or cancel notification
      const reminder = updated.find(r => r.id === id);
      if (reminder?.enabled) {
        scheduleNotification(reminder);
      }
      
      return updated;
    });
  };

  const addCustomReminder = () => {
    const newReminder: Reminder = {
      id: Date.now().toString(),
      type: 'custom',
      title: 'Rappel personnalisé',
      message: 'Message personnalisé',
      time: '12:00',
      frequency: 'daily',
      enabled: true,
      smart: false
    };

    setReminders(prev => {
      const updated = [...prev, newReminder];
      saveReminders(updated);
      return updated;
    });
  };

  const getReminderIcon = (type: string) => {
    switch (type) {
      case 'wear': return Clock;
      case 'hygiene': return Smartphone;
      case 'appointment': return Calendar;
      case 'photo': return Bell;
      default: return Bell;
    }
  };

  const getReminderColor = (type: string) => {
    switch (type) {
      case 'wear': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'hygiene': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'appointment': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      case 'photo': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'text-adaptive-secondary surface-tertiary';
    }
  };

  return (
    <div className="space-y-6">
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-adaptive">Rappels intelligents</h2>
          <button
            onClick={addCustomReminder}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Ajouter un rappel
          </button>
        </div>

        {notificationPermission !== 'granted' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <Bell className="h-5 w-5 text-yellow-600" />
              <div>
                <h3 className="font-medium text-yellow-800">Notifications désactivées</h3>
                <p className="text-sm text-yellow-600">
                  Activez les notifications pour recevoir vos rappels.
                  <button
                    onClick={checkNotificationPermission}
                    className="font-medium underline ml-1"
                  >
                    Activer maintenant
                  </button>
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {reminders.map((reminder) => {
            const IconComponent = getReminderIcon(reminder.type);
            return (
              <div
                key={reminder.id}
                className="flex items-center justify-between p-4 surface-secondary rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${getReminderColor(reminder.type)}`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-adaptive">{reminder.title}</h3>
                      {reminder.smart && (
                        <span className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                          <Zap className="h-3 w-3" />
                          <span>Smart</span>
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-adaptive-secondary">{reminder.message}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-adaptive-tertiary">
                        {reminder.time} • {reminder.frequency}
                      </span>
                      {reminder.lastTriggered && (
                        <span className="text-xs text-adaptive-tertiary">
                          Dernier: {reminder.lastTriggered.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={reminder.enabled}
                    onChange={() => toggleReminder(reminder.id)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 surface-tertiary peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:surface-primary after:border-adaptive after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            );
          })}
        </div>

        {/* Smart Insights */}
        <div className="mt-8 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
          <div className="flex items-center space-x-3 mb-3">
            <Zap className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium text-purple-900">Insights intelligents</h3>
          </div>
          <div className="space-y-2 text-sm text-purple-800">
            <p>• Vos rappels sont optimisés selon vos habitudes</p>
            <p>• Meilleur moment pour porter l'appareil: 8h00-22h00</p>
            <p>• Taux de réponse aux rappels: 87%</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartReminders;