// Initialisation des icônes Lucide
lucide.createIcons();

// Gestion de la navigation entre les sections
function showDemo(section) {
    // Masquer toutes les sections
    document.querySelectorAll('.demo-section').forEach(el => {
        el.classList.remove('active');
    });
    
    // Afficher la section sélectionnée
    document.getElementById(`${section}-demo`).classList.add('active');
    
    // Mettre à jour les boutons de navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    const activeBtn = document.querySelector(`[onclick="showDemo('${section}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

// Gestion du formulaire de connexion
function showPatientLogin() {
    document.getElementById('patient-login').classList.remove('hidden');
    document.getElementById('practitioner-login').classList.add('hidden');
    document.querySelectorAll('.user-type-btn').forEach((btn, index) => {
        if (index === 0) {
            btn.classList.remove('border-gray-200');
            btn.classList.add('border-blue-500');
        } else {
            btn.classList.remove('border-blue-500');
            btn.classList.add('border-gray-200');
        }
    });
}

function showPractitionerLogin() {
    document.getElementById('patient-login').classList.add('hidden');
    document.getElementById('practitioner-login').classList.remove('hidden');
    document.querySelectorAll('.user-type-btn').forEach((btn, index) => {
        if (index === 1) {
            btn.classList.remove('border-gray-200');
            btn.classList.add('border-blue-500');
        } else {
            btn.classList.remove('border-blue-500');
            btn.classList.add('border-gray-200');
        }
    });
}

function loginAsPatient() {
    showDemo('patient');
}

function loginAsPractitioner() {
    showDemo('practitioner');
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Réinitialiser l'état initial
    showDemo('auth');
});
