import React, { useState, useEffect } from 'react';
import { Bell, Plus, X, Clock, Calendar, Repeat, Volume2, VolumeX, Edit, Trash2, CheckCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface Reminder {
  id: string;
  title: string;
  description?: string;
  type: 'medication' | 'appointment' | 'cleaning' | 'exercise' | 'custom';
  frequency: 'once' | 'daily' | 'weekly' | 'monthly';
  time: string;
  date?: string;
  isActive: boolean;
  soundEnabled: boolean;
  lastTriggered?: string;
  nextTrigger: string;
}

interface ReminderForm {
  title: string;
  description: string;
  type: 'medication' | 'appointment' | 'cleaning' | 'exercise' | 'custom';
  frequency: 'once' | 'daily' | 'weekly' | 'monthly';
  time: string;
  date: string;
  soundEnabled: boolean;
}

const Reminders: React.FC = () => {
  const { user } = useAuth();
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingReminder, setEditingReminder] = useState<Reminder | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<ReminderForm>({
    title: '',
    description: '',
    type: 'medication',
    frequency: 'daily',
    time: '08:00',
    date: '',
    soundEnabled: true
  });

  // Données simulées
  useEffect(() => {
    const mockReminders: Reminder[] = [
      {
        id: '1',
        title: 'Porter les gouttières',
        description: 'N\'oubliez pas de porter vos gouttières Invisalign',
        type: 'medication',
        frequency: 'daily',
        time: '08:00',
        isActive: true,
        soundEnabled: true,
        nextTrigger: '2024-06-15T08:00:00'
      },
      {
        id: '2',
        title: 'Brossage des dents',
        description: 'Brossage après chaque repas',
        type: 'cleaning',
        frequency: 'daily',
        time: '12:00',
        isActive: true,
        soundEnabled: false,
        nextTrigger: '2024-06-15T12:00:00'
      },
      {
        id: '3',
        title: 'Rendez-vous Dr. Martin',
        description: 'Consultation de suivi mensuelle',
        type: 'appointment',
        frequency: 'once',
        time: '14:30',
        date: '2024-06-15',
        isActive: true,
        soundEnabled: true,
        nextTrigger: '2024-06-15T14:30:00'
      },
      {
        id: '4',
        title: 'Exercices orthodontiques',
        description: 'Exercices de mobilité mandibulaire',
        type: 'exercise',
        frequency: 'daily',
        time: '20:00',
        isActive: false,
        soundEnabled: true,
        nextTrigger: '2024-06-15T20:00:00'
      }
    ];

    setReminders(mockReminders);
    setLoading(false);
  }, []);

  const handleCreateReminder = () => {
    const newReminder: Reminder = {
      id: Date.now().toString(),
      title: formData.title,
      description: formData.description,
      type: formData.type,
      frequency: formData.frequency,
      time: formData.time,
      date: formData.date,
      isActive: true,
      soundEnabled: formData.soundEnabled,
      nextTrigger: calculateNextTrigger(formData)
    };

    setReminders(prev => [newReminder, ...prev]);
    setShowCreateModal(false);
    resetForm();
  };

  const handleUpdateReminder = () => {
    if (!editingReminder) return;

    const updatedReminder: Reminder = {
      ...editingReminder,
      title: formData.title,
      description: formData.description,
      type: formData.type,
      frequency: formData.frequency,
      time: formData.time,
      date: formData.date,
      soundEnabled: formData.soundEnabled,
      nextTrigger: calculateNextTrigger(formData)
    };

    setReminders(prev => prev.map(r => r.id === editingReminder.id ? updatedReminder : r));
    setEditingReminder(null);
    resetForm();
  };

  const calculateNextTrigger = (data: ReminderForm): string => {
    const now = new Date();
    const [hours, minutes] = data.time.split(':').map(Number);
    
    if (data.frequency === 'once' && data.date) {
      const triggerDate = new Date(data.date);
      triggerDate.setHours(hours, minutes, 0, 0);
      return triggerDate.toISOString();
    }
    
    const triggerDate = new Date(now);
    triggerDate.setHours(hours, minutes, 0, 0);
    
    if (triggerDate <= now) {
      triggerDate.setDate(triggerDate.getDate() + 1);
    }
    
    return triggerDate.toISOString();
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'medication',
      frequency: 'daily',
      time: '08:00',
      date: '',
      soundEnabled: true
    });
  };

  const handleEdit = (reminder: Reminder) => {
    setEditingReminder(reminder);
    setFormData({
      title: reminder.title,
      description: reminder.description || '',
      type: reminder.type,
      frequency: reminder.frequency,
      time: reminder.time,
      date: reminder.date || '',
      soundEnabled: reminder.soundEnabled
    });
    setShowCreateModal(true);
  };

  const handleDelete = (id: string) => {
    setReminders(prev => prev.filter(r => r.id !== id));
  };

  const toggleReminder = (id: string) => {
    setReminders(prev => prev.map(r => 
      r.id === id ? { ...r, isActive: !r.isActive } : r
    ));
  };

  const toggleSound = (id: string) => {
    setReminders(prev => prev.map(r => 
      r.id === id ? { ...r, soundEnabled: !r.soundEnabled } : r
    ));
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'medication':
        return '💊';
      case 'appointment':
        return '📅';
      case 'cleaning':
        return '🦷';
      case 'exercise':
        return '🏃';
      default:
        return '⏰';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'medication':
        return 'Médicament';
      case 'appointment':
        return 'Rendez-vous';
      case 'cleaning':
        return 'Hygiène';
      case 'exercise':
        return 'Exercice';
      default:
        return 'Personnalisé';
    }
  };

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'once':
        return 'Une fois';
      case 'daily':
        return 'Quotidien';
      case 'weekly':
        return 'Hebdomadaire';
      case 'monthly':
        return 'Mensuel';
      default:
        return frequency;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Rappels</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Programmez vos rappels personnalisés pour votre traitement
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Nouveau rappel
          </button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Bell className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reminders.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Total rappels
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reminders.filter(r => r.isActive).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Actifs
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <Repeat className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reminders.filter(r => r.frequency === 'daily').length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Quotidiens
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <Volume2 className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="ml-3">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {reminders.filter(r => r.soundEnabled).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Avec son
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des rappels */}
      <div className="space-y-4">
        {reminders.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 text-center">
            <Bell className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Aucun rappel
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Créez votre premier rappel pour ne rien oublier
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-5 h-5 mr-2" />
              Créer un rappel
            </button>
          </div>
        ) : (
          reminders.map(reminder => (
            <div
              key={reminder.id}
              className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 ${
                !reminder.isActive ? 'opacity-60' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="text-2xl mr-4 mt-1">
                    {getTypeIcon(reminder.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mr-3">
                        {reminder.title}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        reminder.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                      }`}>
                        {reminder.isActive ? 'Actif' : 'Inactif'}
                      </span>
                    </div>
                    
                    {reminder.description && (
                      <p className="text-gray-600 dark:text-gray-400 mb-3">
                        {reminder.description}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {reminder.time}
                      </div>
                      <div className="flex items-center">
                        <Repeat className="w-4 h-4 mr-1" />
                        {getFrequencyText(reminder.frequency)}
                      </div>
                      <div className="flex items-center">
                        <span className="w-4 h-4 mr-1">🏷️</span>
                        {getTypeText(reminder.type)}
                      </div>
                      {reminder.date && (
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(reminder.date).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => toggleSound(reminder.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      reminder.soundEnabled
                        ? 'text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20'
                        : 'text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    title={reminder.soundEnabled ? 'Désactiver le son' : 'Activer le son'}
                  >
                    {reminder.soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                  </button>
                  
                  <button
                    onClick={() => toggleReminder(reminder.id)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      reminder.isActive
                        ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/40'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {reminder.isActive ? 'Désactiver' : 'Activer'}
                  </button>
                  
                  <button
                    onClick={() => handleEdit(reminder)}
                    className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    title="Modifier"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(reminder.id)}
                    className="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title="Supprimer"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Modal de création/modification */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {editingReminder ? 'Modifier le rappel' : 'Nouveau rappel'}
              </h3>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setEditingReminder(null);
                  resetForm();
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Titre */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Titre du rappel
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Ex: Porter les gouttières"
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description (optionnel)
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  rows={2}
                  placeholder="Ajoutez des détails sur ce rappel..."
                />
              </div>

              {/* Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Type de rappel
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="medication">💊 Médicament</option>
                  <option value="appointment">📅 Rendez-vous</option>
                  <option value="cleaning">🦷 Hygiène</option>
                  <option value="exercise">🏃 Exercice</option>
                  <option value="custom">⏰ Personnalisé</option>
                </select>
              </div>

              {/* Fréquence */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Fréquence
                </label>
                <select
                  value={formData.frequency}
                  onChange={(e) => setFormData(prev => ({ ...prev, frequency: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="once">Une fois</option>
                  <option value="daily">Quotidien</option>
                  <option value="weekly">Hebdomadaire</option>
                  <option value="monthly">Mensuel</option>
                </select>
              </div>

              {/* Heure */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Heure
                </label>
                <input
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Date (si une fois) */}
              {formData.frequency === 'once' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date
                  </label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              )}

              {/* Son */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="soundEnabled"
                  checked={formData.soundEnabled}
                  onChange={(e) => setFormData(prev => ({ ...prev, soundEnabled: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="soundEnabled" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Activer le son pour ce rappel
                </label>
              </div>

              {/* Boutons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingReminder(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={editingReminder ? handleUpdateReminder : handleCreateReminder}
                  disabled={!formData.title || (formData.frequency === 'once' && !formData.date)}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {editingReminder ? 'Modifier' : 'Créer'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reminders;
