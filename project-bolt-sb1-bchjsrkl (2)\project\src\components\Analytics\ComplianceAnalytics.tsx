import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { TrendingUp, Calendar, Target, Award, BarChart3, PieChart } from 'lucide-react';

interface ComplianceData {
  date: string;
  worn: boolean;
  hours?: number;
  quality?: 'excellent' | 'good' | 'fair' | 'poor';
}

interface AnalyticsMetrics {
  totalDays: number;
  compliantDays: number;
  complianceRate: number;
  currentStreak: number;
  longestStreak: number;
  averageHours: number;
  weeklyTrend: number;
  monthlyTrend: number;
}

const ComplianceAnalytics: React.FC = () => {
  const { user } = useAuth();
  const [complianceData, setComplianceData] = useState<ComplianceData[]>([]);
  const [metrics, setMetrics] = useState<AnalyticsMetrics>({
    totalDays: 0,
    compliantDays: 0,
    complianceRate: 0,
    currentStreak: 0,
    longestStreak: 0,
    averageHours: 0,
    weeklyTrend: 0,
    monthlyTrend: 0
  });

  useEffect(() => {
    loadComplianceData();
  }, [user?.id]);

  const loadComplianceData = () => {
    const progressData = localStorage.getItem(`progress_${user?.id}`);
    if (progressData) {
      const data = JSON.parse(progressData);
      const complianceArray: ComplianceData[] = Object.entries(data).map(([date, worn]) => ({
        date,
        worn: worn as boolean,
        hours: worn ? Math.random() * 4 + 18 : Math.random() * 8, // Simulate hours
        quality: worn ? (['excellent', 'good', 'fair'] as const)[Math.floor(Math.random() * 3)] : 'poor'
      }));
      
      setComplianceData(complianceArray);
      calculateMetrics(complianceArray);
    }
  };

  const calculateMetrics = (data: ComplianceData[]) => {
    const totalDays = data.length;
    const compliantDays = data.filter(d => d.worn).length;
    const complianceRate = totalDays > 0 ? (compliantDays / totalDays) * 100 : 0;
    
    // Calculate current streak
    let currentStreak = 0;
    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].worn) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Calculate longest streak
    let longestStreak = 0;
    let tempStreak = 0;
    data.forEach(d => {
      if (d.worn) {
        tempStreak++;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        tempStreak = 0;
      }
    });

    // Calculate average hours
    const totalHours = data.reduce((sum, d) => sum + (d.hours || 0), 0);
    const averageHours = totalDays > 0 ? totalHours / totalDays : 0;

    // Calculate trends (simplified)
    const lastWeekData = data.slice(-7);
    const lastMonthData = data.slice(-30);
    const weeklyCompliance = lastWeekData.filter(d => d.worn).length / Math.max(lastWeekData.length, 1) * 100;
    const monthlyCompliance = lastMonthData.filter(d => d.worn).length / Math.max(lastMonthData.length, 1) * 100;

    setMetrics({
      totalDays,
      compliantDays,
      complianceRate,
      currentStreak,
      longestStreak,
      averageHours,
      weeklyTrend: weeklyCompliance - complianceRate,
      monthlyTrend: monthlyCompliance - complianceRate
    });
  };

  const getComplianceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
    if (rate >= 75) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
    if (rate >= 60) return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
    return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 5) return '📈';
    if (trend < -5) return '📉';
    return '➡️';
  };

  const generateComplianceScore = () => {
    const baseScore = metrics.complianceRate;
    const streakBonus = Math.min(metrics.currentStreak * 2, 20);
    const consistencyBonus = metrics.longestStreak > 14 ? 10 : 0;
    const hoursBonus = metrics.averageHours > 20 ? 10 : 0;
    
    return Math.min(Math.round(baseScore + streakBonus + consistencyBonus + hoursBonus), 100);
  };

  const getRecommendations = () => {
    const recommendations = [];
    
    if (metrics.complianceRate < 80) {
      recommendations.push({
        type: 'warning',
        title: 'Améliorer la régularité',
        description: 'Essayez de porter votre appareil plus régulièrement pour de meilleurs résultats.'
      });
    }
    
    if (metrics.currentStreak === 0) {
      recommendations.push({
        type: 'info',
        title: 'Reprendre le rythme',
        description: 'Commencez une nouvelle série en portant votre appareil aujourd\'hui.'
      });
    }
    
    if (metrics.averageHours < 20) {
      recommendations.push({
        type: 'tip',
        title: 'Augmenter la durée',
        description: 'Visez 20-22 heures de port par jour pour des résultats optimaux.'
      });
    }
    
    if (metrics.complianceRate > 90) {
      recommendations.push({
        type: 'success',
        title: 'Excellent travail!',
        description: 'Votre régularité est exemplaire. Continuez ainsi!'
      });
    }
    
    return recommendations;
  };

  const complianceScore = generateComplianceScore();
  const recommendations = getRecommendations();

  return (
    <div className="space-y-6">
      {/* Compliance Score */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Score de Conformité</h2>
            <div className="flex items-center space-x-4">
              <div className="text-4xl font-bold">{complianceScore}</div>
              <div className="text-blue-100">
                <p className="text-sm">sur 100</p>
                <p className="text-xs">Excellent: 90+</p>
              </div>
            </div>
          </div>
          <div className="text-right">
            <Award className="h-12 w-12 text-yellow-400 mb-2" />
            <p className="text-sm text-blue-100">
              {complianceScore >= 90 ? 'Excellent' : 
               complianceScore >= 75 ? 'Bon' : 
               complianceScore >= 60 ? 'Moyen' : 'À améliorer'}
            </p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="surface-primary rounded-xl p-4 shadow-sm border border-adaptive-light">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Target className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Taux de conformité</p>
              <p className="text-xl font-bold text-adaptive">{metrics.complianceRate.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl p-4 shadow-sm border border-adaptive-light">
          <div className="flex items-center space-x-3">
            <div className="bg-green-100 p-2 rounded-lg">
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Série actuelle</p>
              <p className="text-xl font-bold text-adaptive">{metrics.currentStreak} jours</p>
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl p-4 shadow-sm border border-adaptive-light">
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-2 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Meilleure série</p>
              <p className="text-xl font-bold text-adaptive">{metrics.longestStreak} jours</p>
            </div>
          </div>
        </div>

        <div className="surface-primary rounded-xl p-4 shadow-sm border border-adaptive-light">
          <div className="flex items-center space-x-3">
            <div className="bg-orange-100 p-2 rounded-lg">
              <BarChart3 className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Heures moyennes</p>
              <p className="text-xl font-bold text-adaptive">{metrics.averageHours.toFixed(1)}h</p>
            </div>
          </div>
        </div>
      </div>

      {/* Trends */}
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h3 className="text-lg font-semibold text-adaptive mb-4">Tendances</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center justify-between p-4 surface-secondary rounded-lg">
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Tendance hebdomadaire</p>
              <p className="text-lg font-bold text-adaptive">
                {metrics.weeklyTrend > 0 ? '+' : ''}{metrics.weeklyTrend.toFixed(1)}%
              </p>
            </div>
            <span className="text-2xl">{getTrendIcon(metrics.weeklyTrend)}</span>
          </div>
          
          <div className="flex items-center justify-between p-4 surface-secondary rounded-lg">
            <div>
              <p className="text-sm font-medium text-adaptive-secondary">Tendance mensuelle</p>
              <p className="text-lg font-bold text-adaptive">
                {metrics.monthlyTrend > 0 ? '+' : ''}{metrics.monthlyTrend.toFixed(1)}%
              </p>
            </div>
            <span className="text-2xl">{getTrendIcon(metrics.monthlyTrend)}</span>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h3 className="text-lg font-semibold text-adaptive mb-4">Recommandations personnalisées</h3>
        <div className="space-y-3">
          {recommendations.map((rec, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                rec.type === 'success' ? 'border-green-200 bg-green-50' :
                rec.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                rec.type === 'info' ? 'border-blue-200 bg-blue-50' :
                'border-purple-200 bg-purple-50'
              }`}
            >
              <h4 className="font-medium text-adaptive mb-1">{rec.title}</h4>
              <p className="text-sm text-adaptive-secondary">{rec.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Analytics */}
      <div className="surface-primary rounded-xl p-6 shadow-sm border border-adaptive-light">
        <h3 className="text-lg font-semibold text-adaptive mb-4">Analyse détaillée</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-adaptive mb-3">Répartition par qualité</h4>
            <div className="space-y-2">
              {['excellent', 'good', 'fair', 'poor'].map(quality => {
                const count = complianceData.filter(d => d.quality === quality).length;
                const percentage = complianceData.length > 0 ? (count / complianceData.length) * 100 : 0;
                return (
                  <div key={quality} className="flex items-center justify-between">
                    <span className="text-sm text-adaptive-secondary capitalize">{quality}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 surface-tertiary rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            quality === 'excellent' ? 'bg-green-500' :
                            quality === 'good' ? 'bg-blue-500' :
                            quality === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-adaptive">{percentage.toFixed(0)}%</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-adaptive mb-3">Statistiques avancées</h4>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-adaptive-secondary">Jours consécutifs max:</span>
                <span className="font-medium">{metrics.longestStreak}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-adaptive-secondary">Moyenne heures/jour:</span>
                <span className="font-medium">{metrics.averageHours.toFixed(1)}h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-adaptive-secondary">Jours total suivi:</span>
                <span className="font-medium">{metrics.totalDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-adaptive-secondary">Objectif recommandé:</span>
                <span className="font-medium">22h/jour</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceAnalytics;