@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
  
  /* Ensure proper dark mode colors for all elements */
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-200;
  }
  
  /* Medical theme colors */
  .medical-gradient {
    @apply bg-gradient-to-br from-blue-500 via-teal-500 to-green-500;
  }
  
  .orthodontic-gradient {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600;
  }
  
  /* Enhanced dark mode components */
  .dark-card {
    @apply bg-gray-800 dark:bg-gray-900 border-gray-700 dark:border-gray-600;
  }
  
  .dark-input {
    @apply bg-gray-700 dark:bg-gray-800 border-gray-600 dark:border-gray-500 text-white placeholder-gray-400;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-green-500 to-blue-600;
  }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
}

::-webkit-scrollbar-corner {
  @apply bg-gray-100 dark:bg-gray-800;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* Enhanced dark mode transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Medical theme specific styles */
.medical-blue {
  @apply text-blue-600 dark:text-blue-400;
}

.medical-teal {
  @apply text-teal-600 dark:text-teal-400;
}

.medical-purple {
  @apply text-purple-600 dark:text-purple-400;
}

/* Enhanced focus states for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Improved button hover effects */
.btn-hover-lift {
  @apply transform hover:-translate-y-0.5 transition-transform duration-200;
}

/* Glass morphism effect for modern look */
.glass-effect {
  @apply backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 border border-white/20 dark:border-gray-700/50;
}

/* Gradient text effects */
.gradient-text-blue {
  @apply bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent;
}

.gradient-text-purple {
  @apply bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent;
}

/* Enhanced shadow effects */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.dark .shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}
