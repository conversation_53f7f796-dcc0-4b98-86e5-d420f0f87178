@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
  
  /* Ensure proper dark mode colors for all elements */
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200;
  }

  /* Premium card styles for market leadership */
  .card-premium {
    @apply bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] backdrop-blur-sm;
  }

  .card-glass {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 transition-all duration-300;
  }

  /* Enhanced stats cards */
  .stats-card {
    @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-200;
  }
  
  /* Medical theme colors - Enhanced for market leadership */
  .medical-gradient {
    @apply bg-gradient-to-br from-blue-500 via-teal-500 to-green-500;
  }

  .orthodontic-gradient {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600;
  }

  /* Premium orthodontic color palette */
  .premium-blue {
    @apply bg-gradient-to-r from-blue-600 to-blue-700;
  }

  .premium-teal {
    @apply bg-gradient-to-r from-teal-500 to-teal-600;
  }

  .premium-green {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  .premium-purple {
    @apply bg-gradient-to-r from-purple-500 to-purple-600;
  }

  /* Enhanced button styles for professional look */
  .btn-premium {
    @apply bg-gradient-to-r from-blue-600 via-teal-600 to-green-600 hover:from-blue-700 hover:via-teal-700 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105;
  }

  .btn-premium-outline {
    @apply border-2 border-gradient-to-r from-blue-600 to-teal-600 text-blue-600 hover:bg-gradient-to-r hover:from-blue-600 hover:to-teal-600 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  }
  
  /* Enhanced dark mode components */
  .dark-card {
    @apply bg-gray-800 dark:bg-gray-900 border-gray-700 dark:border-gray-600;
  }
  
  .dark-input {
    @apply bg-gray-700 dark:bg-gray-800 border-gray-600 dark:border-gray-500 text-white placeholder-gray-400;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600;
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-green-500 to-blue-600;
  }

  /* Classes avancées pour le dark mode parfait */
  .surface-primary {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }

  .surface-secondary {
    @apply bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200;
  }

  .surface-tertiary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
  }

  .surface-elevated {
    @apply bg-white dark:bg-gray-800 shadow-lg dark:shadow-2xl border border-gray-200 dark:border-gray-700;
  }

  .surface-glass {
    @apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg border border-gray-200/50 dark:border-gray-700/50;
  }

  .text-adaptive {
    @apply text-gray-900 dark:text-gray-100;
  }

  .text-adaptive-secondary {
    @apply text-gray-600 dark:text-gray-400;
  }

  .text-adaptive-tertiary {
    @apply text-gray-500 dark:text-gray-500;
  }

  .border-adaptive {
    @apply border-gray-200 dark:border-gray-700;
  }

  .border-adaptive-light {
    @apply border-gray-100 dark:border-gray-800;
  }

  .hover-surface {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
  }

  .hover-surface-strong {
    @apply hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }

  .shadow-adaptive {
    @apply shadow-sm dark:shadow-lg dark:shadow-black/25;
  }

  .shadow-adaptive-lg {
    @apply shadow-lg dark:shadow-2xl dark:shadow-black/40;
  }

  .divide-adaptive {
    @apply divide-gray-200 dark:divide-gray-700;
  }

  .ring-adaptive {
    @apply ring-gray-200 dark:ring-gray-700;
  }

  /* Classes pour les éléments interactifs */
  .interactive-element {
    @apply surface-primary border-adaptive shadow-adaptive hover-surface focus-ring rounded-lg transition-all duration-200;
  }

  .interactive-button {
    @apply surface-secondary border-adaptive shadow-adaptive hover-surface-strong focus-ring rounded-lg transition-all duration-200 cursor-pointer;
  }

  /* Classes pour les formulaires */
  .form-input {
    @apply surface-primary border-adaptive focus-ring rounded-lg px-4 py-3 text-adaptive placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-200;
  }

  .form-label {
    @apply text-adaptive font-medium text-sm mb-2 block;
  }

  .form-error {
    @apply text-red-600 dark:text-red-400 text-sm mt-1;
  }

  .form-success {
    @apply text-green-600 dark:text-green-400 text-sm mt-1;
  }

  /* Classes pour les alertes et notifications */
  .alert-info {
    @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200 rounded-lg p-4;
  }

  .alert-success {
    @apply bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 rounded-lg p-4;
  }

  .alert-warning {
    @apply bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-lg p-4;
  }

  .alert-error {
    @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 rounded-lg p-4;
  }

  /* Classes pour les badges et indicateurs */
  .badge-primary {
    @apply bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-warning {
    @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-error {
    @apply bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Classes pour les liens */
  .link-primary {
    @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200;
  }

  .link-secondary {
    @apply text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200;
  }

  /* Classes pour les séparateurs */
  .separator {
    @apply border-t border-gray-200 dark:border-gray-700;
  }

  .separator-vertical {
    @apply border-l border-gray-200 dark:border-gray-700;
  }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
}

::-webkit-scrollbar-corner {
  @apply bg-gray-100 dark:bg-gray-800;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* Enhanced dark mode transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Medical theme specific styles */
.medical-blue {
  @apply text-blue-600 dark:text-blue-400;
}

.medical-teal {
  @apply text-teal-600 dark:text-teal-400;
}

.medical-purple {
  @apply text-purple-600 dark:text-purple-400;
}

/* Enhanced focus states for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Improved button hover effects */
.btn-hover-lift {
  @apply transform hover:-translate-y-0.5 transition-transform duration-200;
}

/* Glass morphism effect for modern look */
.glass-effect {
  @apply backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 border border-white/20 dark:border-gray-700/50;
}

/* Gradient text effects */
.gradient-text-blue {
  @apply bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent;
}

.gradient-text-purple {
  @apply bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent;
}

/* Enhanced shadow effects */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.dark .shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}
