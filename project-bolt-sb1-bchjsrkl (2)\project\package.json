{"name": "orthoprogress", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"date-fns": "^2.30.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "axios": "^1.7.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "crypto-js": "^4.2.0", "socket.io-client": "^4.7.5", "react-query": "^3.39.3", "react-hook-form": "^7.52.1", "zod": "^3.23.8", "@hookform/resolvers": "^3.7.0", "react-hot-toast": "^2.4.1", "framer-motion": "^11.2.12", "recharts": "^2.12.7", "react-webcam": "^7.2.0", "tensorflow": "^4.20.0", "@tensorflow/tfjs": "^4.20.0", "three": "^0.165.0", "@react-three/fiber": "^8.16.8", "@react-three/drei": "^9.108.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.19"}}