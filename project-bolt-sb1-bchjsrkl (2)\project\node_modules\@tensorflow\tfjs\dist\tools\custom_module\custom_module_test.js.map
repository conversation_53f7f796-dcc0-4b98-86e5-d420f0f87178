{"version": 3, "file": "custom_module_test.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/custom_module_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAmF;AAGnF,IAAM,kBAAkB,GAAmB;IACzC,aAAa,EAAE,cAAM,OAAA,aAAa,EAAb,CAAa;IAClC,kBAAkB,EAAE,cAAM,OAAA,kBAAkB,EAAlB,CAAkB;IAC5C,gBAAgB,EAAE,UAAC,IAAY,IAAK,OAAA,yBAAkB,IAAI,CAAE,EAAxB,CAAwB;IAC5D,eAAe,EAAE,UAAC,UAAkB,EAAE,OAAe;QACnD,IAAM,UAAU,GAAG,UACf,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,cAAI,OAAO,CAAE,CAAC;QAC1E,OAAO;YACL,UAAU,YAAA;YACV,eAAe,EAAE,wBAAiB,UAAU,mBAAS,UAAU,CAAE;YACjE,cAAc,EAAE,UAAG,UAAU,cAAI,OAAO,CAAE;SAC3C,CAAC;IACJ,CAAC;IACD,uBAAuB,EAAE,UAAC,MAAc;QACtC,IAAM,UAAU,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,OAAO;YACL,UAAU,YAAA;YACV,eAAe,EAAE,0BAAmB,MAAM,mBAAS,UAAU,CAAE;YAC/D,YAAY,EAAE,UAAG,MAAM,iBAAc;SACtC,CAAC;IACJ,CAAC;IACD,uBAAuB,EAAE,UAAC,QAAgB;QACxC,OAAO,wBAAiB,QAAQ,CAAE,CAAC;IACrC,CAAC;IACD,kCAAkC,EAAE,UAChC,SAAiB,EAAE,SAAmB;QACxC,OAAO,iBAAU,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAO,SAAS,mBAAS,SAAS,MAAG,CAAC;IAC5E,CAAC;IACD,kBAAkB,EAAE,UAAC,UAAkB;QACrC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;CACF,CAAC;AAEF,QAAQ,CAAC,4CAA4C,EAAE;IACrD,IAAM,eAAe,GAAG,IAAI,CAAC;IAC7B,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;YAChC,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QACI,IAAA,KAAe,IAAA,qCAAqB;QACtC,6DAA6D;QAC7D,MAAgC,EAAE,kBAAkB,CAAC,EAFlD,IAAI,UAAA,EAAE,IAAI,UAEwC,CAAC;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,8CAA8C,CAAC,CAAC;QAC3E,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAEhE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE;QACvC,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,CAAC;YACrB,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,CAAC,aAAa,CAAC;YACvB,eAAe,iBAAA;SAChB,CAAC;QACI,IAAA,KAAe,IAAA,qCAAqB;QACtC,6DAA6D;QAC7D,MAAgC,EAAE,kBAAkB,CAAC,EAFlD,IAAI,UAAA,EAAE,IAAI,UAEwC,CAAC;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAE3C,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,CAAC;YACrB,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACpC,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QAEK,IAAA,IAAI,GAAI,IAAA,qCAAqB;QAChC,qEAAqE;QACrE,MAAgC,EAAE,kBAAkB,CAAC,KAF9C,CAE+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0BAA0B,EAAE;QAC7B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YACjC,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QACK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2BAA2B,EAAE;QAC9B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YACjC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACpC,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QACK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAE7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,6CAA6C,EAAE;IACtD,IAAM,eAAe,GAAG,KAAK,CAAC;IAE9B,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;YAChC,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QAEK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,8CAA8C,CAAC,CAAC;QAC3E,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAEhE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QACjE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,CAAC;YACrB,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACpC,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QAEK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAEjE,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;aAC1C,IAAI,CAAC,CAAC,CAAC,EAAE,8CAAuC,IAAI,CAAE,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0BAA0B,EAAE;QAC7B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YACjC,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvB,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QAEK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAEjE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2BAA2B,EAAE;QAC9B,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YACjC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;YACpC,MAAM,EAAE,EAAc;YACtB,eAAe,iBAAA;SAChB,CAAC;QAEK,IAAA,IAAI,GAAI,IAAA,qCAAqB,EAChC,MAAgC,EAAE,kBAAkB,CAAC,KAD9C,CAC+C;QAE1D,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAEjE,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,6BAA6B,EAAE;IACtC,EAAE,CAAC,oBAAoB,EAAE;QACvB,IAAM,MAAM,GACR,IAAA,2CAA2B,EAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAEpE,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gBAAgB,EAAE;QACnB,IAAM,MAAM,GAAG,IAAA,2CAA2B,EACtC,CAAC,sBAAsB,EAAE,6BAA6B,CAAC,EACvD,kBAAkB,CAAC,CAAC;QAExB,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CACpB,kEAAkE,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}