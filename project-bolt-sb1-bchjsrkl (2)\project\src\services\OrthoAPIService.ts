import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { securityService } from './securityService';

// Configuration API
const API_CONFIG = {
  baseURL: process.env.VITE_API_URL || 'https://api.orthoprogress.com/v1',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Types pour les réponses API
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Intercepteurs pour la sécurité et l'authentification
class ApiService {
  private client: AxiosInstance;
  private refreshTokenPromise: Promise<string> | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '2.0.0',
        'X-Platform': 'web',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Intercepteur de requête - Ajouter l'authentification
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Ajouter un ID de requête unique pour le tracking
        config.headers['X-Request-ID'] = this.generateRequestId();

        // Chiffrer les données sensibles
        if (config.data && this.isSensitiveEndpoint(config.url || '')) {
          config.data = {
            encrypted: securityService.encryptData(config.data),
            timestamp: Date.now(),
          };
        }

        // Log de sécurité
        securityService.logSecurityEvent('API_REQUEST', {
          method: config.method?.toUpperCase(),
          url: config.url,
          requestId: config.headers['X-Request-ID'],
        });

        return config;
      },
      (error) => {
        securityService.logSecurityEvent('API_REQUEST_ERROR', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Intercepteur de réponse - Gestion des erreurs et refresh token
    this.client.interceptors.response.use(
      (response) => {
        // Déchiffrer les données si nécessaire
        if (response.data?.encrypted) {
          response.data = securityService.decryptData(response.data.encrypted);
        }

        securityService.logSecurityEvent('API_RESPONSE_SUCCESS', {
          status: response.status,
          requestId: response.config.headers['X-Request-ID'],
        });

        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Gestion du token expiré (401)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            // Rediriger vers la page de connexion
            this.handleAuthenticationFailure();
            return Promise.reject(refreshError);
          }
        }

        // Gestion des erreurs de rate limiting (429)
        if (error.response?.status === 429) {
          const retryAfter = error.response.headers['retry-after'] || 60;
          await this.delay(retryAfter * 1000);
          return this.client(originalRequest);
        }

        // Retry automatique pour les erreurs serveur (5xx)
        if (error.response?.status >= 500 && originalRequest._retryCount < API_CONFIG.retryAttempts) {
          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
          await this.delay(API_CONFIG.retryDelay * originalRequest._retryCount);
          return this.client(originalRequest);
        }

        securityService.logSecurityEvent('API_RESPONSE_ERROR', {
          status: error.response?.status,
          message: error.message,
          requestId: originalRequest.headers['X-Request-ID'],
        });

        return Promise.reject(this.formatError(error));
      }
    );
  }

  // Méthodes HTTP sécurisées
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // Upload de fichiers avec progression
  async uploadFile(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('timestamp', Date.now().toString());
    formData.append('checksum', await this.calculateFileChecksum(file));

    return this.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Téléchargement de fichiers sécurisé
  async downloadFile(url: string, filename?: string): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  // Gestion de l'authentification
  private async refreshAccessToken(): Promise<string> {
    if (this.refreshTokenPromise) {
      return this.refreshTokenPromise;
    }

    this.refreshTokenPromise = this.performTokenRefresh();
    
    try {
      const newToken = await this.refreshTokenPromise;
      this.refreshTokenPromise = null;
      return newToken;
    } catch (error) {
      this.refreshTokenPromise = null;
      throw error;
    }
  }

  private async performTokenRefresh(): Promise<string> {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh`, {
      refreshToken,
    });

    const { accessToken, refreshToken: newRefreshToken } = response.data.data;
    
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', newRefreshToken);

    securityService.logSecurityEvent('TOKEN_REFRESHED');
    
    return accessToken;
  }

  private handleAuthenticationFailure(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    
    securityService.logSecurityEvent('AUTHENTICATION_FAILURE');
    
    // Rediriger vers la page de connexion
    window.location.href = '/auth';
  }

  // Utilitaires
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private isSensitiveEndpoint(url: string): boolean {
    const sensitivePatterns = [
      '/patients',
      '/medical-records',
      '/photos',
      '/billing',
      '/personal-info',
    ];
    return sensitivePatterns.some(pattern => url.includes(pattern));
  }

  private async calculateFileChecksum(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private formatError(error: any): ApiError {
    return {
      code: error.response?.data?.code || 'UNKNOWN_ERROR',
      message: error.response?.data?.message || error.message || 'Une erreur est survenue',
      details: error.response?.data?.details,
      timestamp: new Date().toISOString(),
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Méthodes de monitoring
  getApiHealth(): Promise<ApiResponse> {
    return this.get('/health');
  }

  getApiMetrics(): Promise<ApiResponse> {
    return this.get('/metrics');
  }

  // Nettoyage des ressources
  destroy(): void {
    this.client.interceptors.request.clear();
    this.client.interceptors.response.clear();
  }
}

// Instance singleton
export const apiService = new ApiService();

// Types d'endpoints spécifiques
export interface AuthEndpoints {
  login: (credentials: { email: string; password: string }) => Promise<ApiResponse>;
  register: (userData: any) => Promise<ApiResponse>;
  logout: () => Promise<ApiResponse>;
  refreshToken: () => Promise<ApiResponse>;
  forgotPassword: (email: string) => Promise<ApiResponse>;
  resetPassword: (token: string, password: string) => Promise<ApiResponse>;
}

export interface PatientEndpoints {
  getPatients: (params?: any) => Promise<ApiResponse>;
  getPatient: (id: string) => Promise<ApiResponse>;
  createPatient: (data: any) => Promise<ApiResponse>;
  updatePatient: (id: string, data: any) => Promise<ApiResponse>;
  deletePatient: (id: string) => Promise<ApiResponse>;
}

export interface MedicalEndpoints {
  getMedicalRecord: (patientId: string) => Promise<ApiResponse>;
  updateMedicalRecord: (patientId: string, data: any) => Promise<ApiResponse>;
  uploadPhoto: (patientId: string, file: File) => Promise<ApiResponse>;
  getPhotos: (patientId: string) => Promise<ApiResponse>;
  analyzeProgress: (patientId: string) => Promise<ApiResponse>;
}

// Export des endpoints typés
export const authApi: AuthEndpoints = {
  login: (credentials) => apiService.post('/auth/login', credentials),
  register: (userData) => apiService.post('/auth/register', userData),
  logout: () => apiService.post('/auth/logout'),
  refreshToken: () => apiService.post('/auth/refresh'),
  forgotPassword: (email) => apiService.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => apiService.post('/auth/reset-password', { token, password }),
};

export const patientApi: PatientEndpoints = {
  getPatients: (params) => apiService.get('/patients', { params }),
  getPatient: (id) => apiService.get(`/patients/${id}`),
  createPatient: (data) => apiService.post('/patients', data),
  updatePatient: (id, data) => apiService.put(`/patients/${id}`, data),
  deletePatient: (id) => apiService.delete(`/patients/${id}`),
};

export const medicalApi: MedicalEndpoints = {
  getMedicalRecord: (patientId) => apiService.get(`/patients/${patientId}/medical-record`),
  updateMedicalRecord: (patientId, data) => apiService.put(`/patients/${patientId}/medical-record`, data),
  uploadPhoto: (patientId, file) => apiService.uploadFile(`/patients/${patientId}/photos`, file),
  getPhotos: (patientId) => apiService.get(`/patients/${patientId}/photos`),
  analyzeProgress: (patientId) => apiService.post(`/patients/${patientId}/analyze-progress`),
};
